{"permissions": {"allow": ["Bash(npm run lint:*)", "Bash(npx tsc:*)", "Bash(npm run build:*)", "Bash(grep:*)", "Bash(rm:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(npx next build:*)", "<PERSON><PERSON>(sed:*)", "Bash(rg:*)", "Bash(npm install)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(mkdir:*)", "Bash(node:*)", "Bash(npm --version)", "Bash(npm install:*)", "Bash(cp:*)", "Bash(/dev/null)", "Bash(NODE_ENV=development npm run dev 2 >& 1)", "Bash(npx next dev:*)", "mcp__ide__getDiagnostics", "Bash(for file in src/dictionaries/{ru,tr,ar}.json)", "Bash(do echo \"Checking $file:\")", "<PERSON><PERSON>(python:*)", "Bash(done)", "Bash(for lang in en de ru tr ar)", "Bash(do echo \"=== $lang.json ===\")", "Bash(find:*)", "<PERSON><PERSON>(clear)", "<PERSON><PERSON>(claude mcp:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(git add:*)", "Bash(git commit:*)", "mcp__desktop-commander__create_directory", "mcp__desktop-commander__write_file", "mcp__desktop-commander__read_file", "mcp__desktop-commander__edit_block", "mcp__desktop-commander__list_directory", "Bash(ls:*)", "Bash(npm run typecheck:*)", "WebFetch(domain:reactbits.dev)", "WebFetch(domain:www.innovatio-pro.com)", "Bash(npm run:*)", "Bash(timeout 10s npm run dev:*)", "Bash(timeout 20s npm run dev:*)", "<PERSON><PERSON>(jq:*)", "Bash(git push:*)"], "deny": []}}