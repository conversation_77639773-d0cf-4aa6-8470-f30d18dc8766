/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: "class",
  theme: {
    screens: {
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
      '3xl': '1920px',
      '4xl': '2560px',
    },
    extend: {
      colors: {
        primary: {
          DEFAULT: "#1a2236", // Dark Blue
          foreground: "#ffffff",
        },
        secondary: {
          DEFAULT: "#475569",
          foreground: "#ffffff",
        },
        destructive: {
          DEFAULT: "#ef4444",
          foreground: "#ffffff",
        },
        muted: {
          DEFAULT: "#f1f5f9",
          foreground: "#64748b",
        },
        accent: {
          DEFAULT: "#3B82F6", // Blue
          foreground: "#ffffff",
        },
        orange: {
          DEFAULT: "#FF7A00",
          50: "#FFF7ED",
          100: "#FFEED4",
          200: "#FFDAA8",
          300: "#FFC171",
          400: "#FF9F38",
          500: "#FF7A00",
          600: "#E55D00",
          700: "#CC4400",
          800: "#B33700",
          900: "#992E00",
        },
        darkBlue: {
          DEFAULT: "#1a2236",
          50: "#f8f9fa",
          100: "#e9ecef",
          200: "#d1d7e0",
          300: "#b8c2d1",
          400: "#9fadc2",
          500: "#6c7b95",
          600: "#4a5568",
          700: "#2d3748",
          800: "#1a202c",
          900: "#1a2236",
        },
      },
      animation: {
        "scroll-slow": "scroll 60s linear infinite",
      },
      keyframes: {
        scroll: {
          "0%": { transform: "translateX(0)" },
          "100%": { transform: "translateX(calc(-264px * 8))" }, // Adjust based on item width and count
        },
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
      },
      fontSize: {
        '8xl': '6rem',
        '9xl': '8rem',
      },
      maxWidth: {
        '8xl': '88rem',
        '9xl': '96rem',
      },
    },
  },
  plugins: [
    require("@tailwindcss/line-clamp"),
    function({ addUtilities }) {
      addUtilities({
        '.scrollbar-hide': {
          /* IE and Edge */
          '-ms-overflow-style': 'none',
          /* Firefox */
          'scrollbar-width': 'none',
          /* Safari and Chrome */
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        }
      })
    }
  ],
};