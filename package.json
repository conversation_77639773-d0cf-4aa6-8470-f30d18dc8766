{"name": "website_nextjs_react", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && next-sitemap", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@notionhq/client": "^3.1.3", "@number-flow/react": "^0.5.9", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.1.2", "@svgr/webpack": "^8.1.0", "@tabler/icons-react": "^3.31.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/nodemailer": "^6.4.17", "@types/react-signature-canvas": "^1.0.7", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "aws-sdk": "^2.1692.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "embla-carousel": "^8.6.0", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.16.0", "gsap": "^3.13.0", "lottie-react": "^2.4.1", "lucide-react": "^0.484.0", "negotiator": "^1.0.0", "next": "^15.3.3", "next-intl": "^3.22.4", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "nodemailer": "^6.10.0", "ogl": "^1.0.11", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-fast-marquee": "^1.6.5", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-signature-canvas": "^1.1.0-alpha.2", "react-syntax-highlighter": "^15.6.1", "react-type-animation": "^3.2.0", "react-use-measure": "^2.1.7", "resend": "^4.5.1", "sharp": "^0.33.5", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "uuid": "^11.1.0", "web-vitals": "^4.2.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "^18.3.14", "@types/react-dom": "^18.3.5", "eslint": "^9", "eslint-config-next": "15.2.1", "tailwindcss": "^4", "typescript": "^5"}}