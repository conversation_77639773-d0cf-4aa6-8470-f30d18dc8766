# Email Setup Guide - Simplified Approach

## ❓ Why Do We Need Email Credentials?

**Common Confusion**: "Why do I need a password when users are just sending me emails?"

**Simple Answer**: To **SEND** emails, not to receive them!

### How Contact Forms Work:
1. **User fills out form** → enters THEIR email address
2. **System sends email TO you** → `<EMAIL>`
3. **To send emails, we need credentials** → like having a "return address" on a letter

Think of it like postal mail: To send a letter, you need a valid return address and postage.

---

## 🎯 Recommended Solution: Resend API

### Why Resend is Better Than SMTP:
- ✅ **No email passwords needed** - just one API key
- ✅ **More reliable** - better deliverability
- ✅ **Easier setup** - no SMTP configuration
- ✅ **Better security** - no password management

### Setup Steps:

1. **Sign up at [Resend.com](https://resend.com)**
2. **Get your API key**
3. **Add to your `.env.local`**:
   ```env
   RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxx
   ```

That's it! No passwords, no SMTP configuration needed.

---

## 📧 Alternative: SMTP (Gmail)

If you prefer using Gmail SMTP, you'll need:

### Gmail App Password Setup:
1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password**:
   - Go to [Google Account Settings](https://myaccount.google.com/security)
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Add to `.env.local`**:
   ```env
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-16-character-app-password
   ```

### 🔐 Important: This is NOT Your Regular Gmail Password!
- Use the **16-character app password** from Google
- Never use your regular Gmail password in code

---

## 🛠 Current Implementation

Our system is now configured to:

1. **Try Resend API first** (if `RESEND_API_KEY` is set)
2. **Fallback to Gmail SMTP** (if Resend is not configured)
3. **📋 Create Lead in Notion CRM** (if configured)

### Environment Variables:
```env
# Option 1: Resend (Recommended)
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxx

# Option 2: Gmail SMTP (Fallback)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Notion CRM Integration (Required for lead generation)
NOTION_TOKEN=secret_your_notion_integration_token_here
NOTION_CRM_DATABASE_ID=your_notion_database_id_here
NEXT_PUBLIC_BASE_URL=https://yourdomain.com
```

---

## 🗃️ Notion CRM Integration

### What Gets Created in Notion:
When a user submits the contact form, the system automatically:

1. **Sends email notification** to `<EMAIL>`
2. **Creates a lead in Notion CRM** with all form data
3. **Generates proposal URL** (if service is selected)
4. **Generates contract URL** (for qualified leads)
5. **Sets lead status** to "New Lead"

### Notion Database Properties:
Your Notion database should have these fields:
- **Name** (Title) - Customer name
- **Email** (Email) - Customer email 
- **Phone** (Phone) - Customer phone number
- **Company** (Text) - Company name
- **ClientID** (Text) - Unique identifier
- **Selected Package** (Select) - Service interest
- **Estimated Budget** (Text) - Budget range
- **Project Timeline** (Text) - Timeline preference
- **Status** (Select) - Lead status
- **ProposalURL** (URL) - Generated proposal link
- **ContractURL** (URL) - Generated contract link
- **Created Date** (Date) - Submission timestamp
- **Notes** (Text) - Combined form data

---

## 🔍 Testing Your Setup

Test your contact form to make sure both emails and Notion leads are working:

1. Fill out the contact form on your website
2. Check the server logs for confirmation
3. Check your email inbox for the message
4. Check your Notion database for the new lead

### Expected Log Output:
```
=== Environment Variables Check ===
RESEND_API_KEY is set: true
NOTION_TOKEN is set: true
NOTION_CRM_DATABASE_ID is set: true
=====================================

=== Using Resend API (Recommended) ===
✅ Resend Email sent successfully!

=== Creating Notion CRM Lead ===
✅ Notion CRM lead created successfully: abc-123-def
```

### Frontend Success Response:
```javascript
✅ Contact form submitted successfully: {
  emailSent: true,
  notionLead: 'Created',
  leadId: 'abc-123-def-456',
  proposalUrl: 'https://yourdomain.com/de/proposal/abc-123-def-456'
}
```

---

## 🚨 Security Best Practices

1. **Never commit passwords to Git**
2. **Use environment variables** for all credentials
3. **Prefer API keys over passwords** (Resend vs SMTP)
4. **Use app passwords, not regular passwords** for Gmail
5. **Secure your Notion integration** with proper permissions

---

## 🆘 Troubleshooting

### "Authentication failed"
- Check your app password (not regular password)
- Ensure 2FA is enabled on Gmail
- Verify environment variables are set correctly

### "Connection failed"
- Check firewall settings
- Verify SMTP host and port
- Consider switching to Resend API

### "No email service available"
- Set either `RESEND_API_KEY` or Gmail SMTP credentials
- Check environment variables are loaded correctly

### "Notion CRM error"
- Verify `NOTION_TOKEN` and `NOTION_CRM_DATABASE_ID` are set
- Check that your Notion integration has access to the database
- Ensure all required properties exist in your Notion database

### "Email sent but no Notion lead"
- This is normal - email sending is prioritized
- Check Notion configuration and database permissions
- Leads will be logged for manual processing if Notion fails

---

## 💡 Recommendation

**Use Resend API + Notion CRM** - this combination provides:
- ✅ **Reliable email delivery**
- ✅ **Automatic lead management**
- ✅ **Proposal/contract generation**
- ✅ **No password complexity**

This setup ensures both email notifications AND lead tracking work seamlessly! 🚀 