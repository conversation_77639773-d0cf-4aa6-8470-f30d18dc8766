# Notion CRM Integration - Required Environment Variables

## Required Environment Variables

Add these variables to your `.env.local` file:

```bash
# Notion Integration
NOTION_TOKEN=secret_your_notion_integration_token_here
NOTION_CRM_DATABASE_ID=your_notion_database_id_here

# Application Base URL (for generating proposal/contract URLs)
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

## How to Get These Values

### 1. NOTION_TOKEN

1. Go to [Notion Integrations](https://www.notion.so/my-integrations)
2. Click "New integration"
3. Give it a name (e.g., "Innovatio-Pro CRM")
4. Select your workspace
5. Click "Submit"
6. Copy the "Internal Integration Token" - this is your `NOTION_TOKEN`

### 2. NOTION_CRM_DATABASE_ID

1. Create a new database in Notion for your CRM
2. Set up the following properties in your database:

#### Required Database Properties:

| Property Name | Type | Description |
|---------------|------|-------------|
| Name | Title | Customer name |
| Email | Email | Customer email |
| Company Name | Text | Customer company |
| Phone | Phone | Customer phone number |
| Client ID | Text | Unique client identifier |
| E-mail | Email | Alternative email field |
| Company | Text | Alternative company field |
| Selected Package | Select | Service package selection |
| Interested Service | Select | Service interest |
| Service Price | Number | Service price |
| Estimated Budget | Number | Customer budget |
| Project Timeline | Text | Project timeline |
| Heard About | Text | How they heard about you |
| Description | Text | Project description |
| Status | Select | Lead status (New Lead, On Hold, Qualified, Won, Lost) |
| Priority | Select | Priority level (Low, Medium, High, Urgent) |
| Created Date | Date | Creation timestamp |
| Signature Date | Date | Contract signature date |
| Notes | Text | General notes |
| Internal Comments | Text | Internal team comments |
| Signature 1 | Text | Signature data part 1 |
| Signature 2 | Text | Signature data part 2 |
| Signature 3 | Text | Signature data part 3 |
| Proposal URL | URL | Proposal page URL |
| Contract URL | URL | Contract page URL |
| ProposalURL | URL | Alternative proposal URL |
| ContractURL | URL | Alternative contract URL |
| URL Password | Text | Access password |
| URLPassword | Text | Alternative password field |
| ClientID | Text | Alternative client ID |
| Package Price | Number | Package pricing |
| Project Description | Text | Detailed project description |
| Last Contact | Date | Last contact date |

3. Share the database with your integration:
   - Click "Share" on your database
   - Add your integration by name
   - Give it "Edit" permissions

4. Copy the database ID from the URL:
   - URL format: `https://notion.so/your-workspace/DATABASE_ID?v=...`
   - The DATABASE_ID is the long string between the last `/` and `?v=`

### 3. NEXT_PUBLIC_BASE_URL

- For development: `http://localhost:3000`
- For production: `https://yourdomain.com`

## Security Notes

- Never commit `.env.local` to version control
- Use different databases for development and production
- Rotate your integration tokens periodically
- Ensure your Notion workspace has proper access controls

## Testing the Integration

Once you've set up the environment variables, you can test the integration by:

1. Starting your development server: `npm run dev`
2. Submitting a contact form
3. Checking if a new entry appears in your Notion database

## Troubleshooting

### Common Issues:

1. **"Notion CRM database ID is not configured"**
   - Check that `NOTION_CRM_DATABASE_ID` is set correctly
   - Verify the database ID is correct

2. **"Object not found" error**
   - Ensure your integration has access to the database
   - Check that the database is shared with your integration

3. **"Property not found" errors**
   - Verify all required properties exist in your database
   - Check property names match exactly (case-sensitive)

4. **Authentication errors**
   - Verify your `NOTION_TOKEN` is correct
   - Check that the integration is active in your Notion workspace 