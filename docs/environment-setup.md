# Environment Setup Guide

## Required Environment Variables

Create a `.env.local` file in your project root with the following variables:

```bash
# Production Site URL (used for sitemap generation and SEO)
SITE_URL=https://yoursite.com

# Google Analytics Measurement ID
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Google Search Console Verification Code (for meta verification tag)
GOOGLE_SITE_VERIFICATION=your_google_verification_code

# Contact Email for forms
CONTACT_EMAIL=<EMAIL>

# Database URL (if using database)
DATABASE_URL=your_database_url

# Auth Secret (if using authentication)
NEXTAUTH_SECRET=your_secret_key
NEXTAUTH_URL=https://yoursite.com
```

## Setup Instructions

### 1. Google Analytics Setup
1. Go to [Google Analytics](https://analytics.google.com/)
2. Create a new property for your website
3. Copy the Measurement ID (starts with G-)
4. Add it to `NEXT_PUBLIC_GA_MEASUREMENT_ID`

### 2. Google Search Console Setup
1. Go to [Google Search Console](https://search.google.com/search-console)
2. Add your property
3. Choose "HTML tag" verification method
4. Copy the verification code from the meta tag
5. Add it to `GOOGLE_SITE_VERIFICATION`
6. Update the verification code in `src/components/Seo.tsx` and `src/lib/seo.ts`

### 3. Update Site URL
1. Replace `https://innovatio-pro.com` with your actual domain in:
   - `next-sitemap.config.js`
   - `src/components/Seo.tsx`
   - `src/lib/seo.ts`

### 4. Update Business Information
Update the organization data in all dictionary files (`src/dictionaries/*.json`):
- Company name
- Address
- Phone number
- Email
- Social media links

## SEO Configuration Files

The following files contain SEO-related configuration that you may need to update:

1. **next-sitemap.config.js** - Sitemap generation
2. **src/components/Seo.tsx** - SEO metadata component
3. **src/lib/seo.ts** - SEO utility functions
4. **src/dictionaries/*.json** - SEO metadata for each language
5. **app/layout.tsx** - Root layout with analytics
6. **app/[locale]/layout.tsx** - Locale-specific layout with metadata

## Important Notes

- The Google Analytics component only loads when `NEXT_PUBLIC_GA_MEASUREMENT_ID` is set
- Google Search Console verification requires the actual verification code
- Sitemap is automatically generated during build process
- All SEO metadata is internationalized for all 5 supported languages 