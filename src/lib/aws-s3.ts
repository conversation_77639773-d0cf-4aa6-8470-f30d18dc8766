import AWS from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';

// Configure AWS SDK
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || '';

if (!BUCKET_NAME) {
  console.warn('AWS_S3_BUCKET_NAME not set in environment variables');
}

/**
 * Uploads a signature image (base64) to AWS S3
 * @param clientId - The client ID for organizing files
 * @param signatureDataURL - The base64 data URL of the signature
 * @param companyName - Optional company name for the filename
 * @param customerName - Optional customer name for the filename
 * @returns Promise<string> - The S3 URL of the uploaded image
 */
export async function uploadSignatureToS3(
  clientId: string,
  signatureDataURL: string,
  companyName?: string,
  customerName?: string
): Promise<string> {
  try {
    // Extract base64 data from data URL (remove data:image/png;base64, prefix)
    const base64Data = signatureDataURL.replace(/^data:image\/\w+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');
    
    // Generate filename with innovatio-pro folder, company name, customer name, and date
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const safeName = (name: string) => name.replace(/[^a-zA-Z0-9]/g, '_');
    
    let fileNameParts = ['innovatio-pro', 'signatures', clientId];
    if (companyName) {
      fileNameParts.push(safeName(companyName));
    }
    if (customerName) {
      fileNameParts.push(safeName(customerName));
    }
    fileNameParts.push(`${date}-signature.png`);
    
    const fileName = fileNameParts.join('/');
    
    // Upload parameters
    const uploadParams = {
      Bucket: BUCKET_NAME,
      Key: fileName,
      Body: buffer,
      ContentType: 'image/png',
      ContentEncoding: 'base64',
      ACL: 'private', // Keep signatures private
    };

    // Upload to S3
    const result = await s3.upload(uploadParams).promise();
    
    console.log('✅ Signature uploaded to S3:', result.Location);
    return result.Location;
    
  } catch (error) {
    console.error('❌ Error uploading signature to S3:', error);
    throw new Error('Failed to upload signature to S3');
  }
}

/**
 * Generates a signed URL for accessing a private signature image
 * @param s3Url - The S3 URL of the signature image
 * @param expiresIn - Expiration time in seconds (default: 1 hour)
 * @returns Promise<string> - The signed URL
 */
export async function getSignedUrl(
  s3Url: string,
  expiresIn: number = 3600
): Promise<string> {
  try {
    // Extract bucket and key from S3 URL
    const url = new URL(s3Url);
    const pathParts = url.pathname.substring(1).split('/');
    const key = pathParts.join('/');
    
    const params = {
      Bucket: BUCKET_NAME,
      Key: key,
      Expires: expiresIn,
    };

    const signedUrl = await s3.getSignedUrlPromise('getObject', params);
    return signedUrl;
    
  } catch (error) {
    console.error('❌ Error generating signed URL:', error);
    throw new Error('Failed to generate signed URL');
  }
}

/**
 * Deletes a signature image from S3
 * @param s3Url - The S3 URL of the signature image to delete
 * @returns Promise<void>
 */
export async function deleteSignatureFromS3(s3Url: string): Promise<void> {
  try {
    // Extract key from S3 URL
    const url = new URL(s3Url);
    const key = url.pathname.substring(1);
    
    const deleteParams = {
      Bucket: BUCKET_NAME,
      Key: key,
    };

    await s3.deleteObject(deleteParams).promise();
    console.log('✅ Signature deleted from S3:', key);
    
  } catch (error) {
    console.error('❌ Error deleting signature from S3:', error);
    throw new Error('Failed to delete signature from S3');
  }
}