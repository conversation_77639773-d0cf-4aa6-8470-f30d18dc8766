// Notion CRM Integration für Innovatio-Pro Proposal & Contract System
import { Client } from '@notionhq/client';
import { CustomerProposal, CreateProposalRequest, UpdateProposalRequest } from '@/types/proposal';
import { formatPrice } from '@/lib/config/services';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';

// Initialize Notion client
const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const CRM_DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '';

if (!CRM_DATABASE_ID) {
    console.warn('NOTION_CRM_DATABASE_ID not set in environment variables');
}

// Helper function to generate secure URL password
export function generateUrlPassword(): string {
    // Define character sets
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const specialChars = '!@#$%&*';

    // Combine all character sets
    const allChars = lowercase + uppercase + numbers + specialChars;

    let password = '';

    // Ensure at least one character from each set
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += specialChars[Math.floor(Math.random() * specialChars.length)];

    // Fill remaining 12 characters randomly
    for (let i = 4; i < 16; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password to avoid predictable patterns
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

// Helper function to generate URLs
export function generateProposalUrls(clientId: string) {
    return {
        proposalUrl: `/create-proposal/${clientId}`,
        contractUrl: `/contract/${clientId}`,
    };
}

// Map Notion page to CustomerProposal
export function mapCrmLeadToCustomerProposal(page: any): CustomerProposal {
    const properties = page.properties;

    // Helper function to get property value safely
    const getPropertyValue = (propName: string, type: string) => {
        try {
            const prop = properties[propName];
            if (!prop) return null;

            switch (type) {
                case 'title':
                    return prop.title?.[0]?.plain_text || '';
                case 'rich_text':
                    return prop.rich_text?.[0]?.plain_text || '';
                case 'email':
                    return prop.email || '';
                case 'phone_number':
                    return prop.phone_number || '';
                case 'select':
                    return prop.select?.name || '';
                case 'multi_select':
                    return prop.multi_select?.map((item: any) => item.name) || [];
                case 'date':
                    return prop.date?.start || null;
                case 'url':
                    return prop.url || '';
                case 'number':
                    return prop.number || 0;
                default:
                    return null;
            }
        } catch (error) {
            console.error(`Error getting property ${propName}:`, error);
            return null;
        }
    };

    // Parse add-ons from notes field
    const notes = getPropertyValue('Notes', 'rich_text') || '';
    const selectedAddOns: string[] = [];

    // Extract add-ons from notes if formatted properly
    const addOnMatches = notes.match(/Add-ons?:\s*(.*?)(?:\n|$)/i);
    if (addOnMatches) {
        const addOnString = addOnMatches[1];
        // Parse add-on list (assumes format like "addon1, addon2, addon3")
        selectedAddOns.push(...addOnString.split(',').map((addon: string) => addon.trim()).filter(Boolean));
    }

    // Get signature URL
    const signatureUrl = getPropertyValue('SignatureURL', 'url') || '';

    return {
        clientId: getPropertyValue('ClientID', 'rich_text') || '',
        name: getPropertyValue('Name', 'title') || '',
        email: getPropertyValue('Email', 'email') || '',
        phone: getPropertyValue('Phone', 'phone_number') || '',
        companyName: getPropertyValue('CompanyName', 'rich_text') || undefined,

        // Address Information
        street: getPropertyValue('Street', 'rich_text') || undefined,
        houseNumber: getPropertyValue('HouseNumber', 'number')?.toString() || undefined,
        city: getPropertyValue('City', 'rich_text') || undefined,
        postalCode: getPropertyValue('PostalCode', 'number')?.toString() || undefined,
        country: getPropertyValue('Country', 'rich_text') || undefined,

        interestedService: getPropertyValue('InterestedService', 'select') || 'mvp',
        servicePrice: getPropertyValue('ServicePrice', 'number') || 0,
        estimatedBudget: getPropertyValue('EstimatedBudget', 'number') || undefined,
        projectTimeline: getPropertyValue('ProjectTimeline', 'rich_text') || undefined,
        heardAbout: getPropertyValue('HeardAbout', 'rich_text') || undefined,
        description: getPropertyValue('Description', 'rich_text') || undefined,
        selectedAddOns,
        totalPrice: getPropertyValue('ServicePrice', 'number') || 0,
        proposalUrl: getPropertyValue('ProposalURL', 'url') || '',
        contractUrl: getPropertyValue('ContractURL', 'url') || '',
        urlPassword: getPropertyValue('URLPassword', 'rich_text') || '',
        status: (getPropertyValue('Status', 'select') || 'New Lead') as CustomerProposal['status'],
        priority: (getPropertyValue('Priority', 'select') || 'Medium') as CustomerProposal['priority'],
        createdDate: getPropertyValue('Created Date', 'date') || new Date().toISOString(),
        signatureDate: getPropertyValue('Signature Date', 'date'),
        notes: getPropertyValue('Notes', 'rich_text') || undefined,
        internalComments: getPropertyValue('Internal Comments', 'rich_text') || undefined,
        signatureUrl: signatureUrl || undefined
    };
}

// Create a new customer proposal in Notion
export async function createCustomerProposal(data: CreateProposalRequest): Promise<CustomerProposal> {
    try {
        const clientId = data.clientId || uuidv4();
        const urlPassword = generateUrlPassword();
        const { proposalUrl, contractUrl } = generateProposalUrls(clientId);

        const response = await notion.pages.create({
            parent: { database_id: CRM_DATABASE_ID },
            properties: {
                // Basic Information
                'Name': {
                    title: [{ text: { content: data.name } }]
                },
                'Email': {
                    email: data.email
                },
                'CompanyName': {
                    rich_text: data.companyName ? [{ text: { content: data.companyName } }] : []
                },
                'Phone': {
                    phone_number: data.phone || null
                },

                // Address Information
                'Street': {
                    rich_text: data.street ? [{ text: { content: data.street } }] : []
                },
                'HouseNumber': {
                    number: data.houseNumber && !isNaN(parseFloat(data.houseNumber)) ? parseFloat(data.houseNumber) : null
                },
                'City': {
                    rich_text: data.city ? [{ text: { content: data.city } }] : []
                },
                'PostalCode': {
                    number: data.postalCode && !isNaN(parseFloat(data.postalCode)) ? parseFloat(data.postalCode) : null
                },
                'Country': {
                    rich_text: data.country ? [{ text: { content: data.country } }] : []
                },

                // URLs and Security
                'ProposalURL': {
                    url: proposalUrl
                },
                'ContractURL': {
                    url: contractUrl
                },
                'URLPassword': {
                    rich_text: [{ text: { content: urlPassword } }]
                },

                // Service Information
                'InterestedService': {
                    select: { name: data.interestedService }
                },
                'ServicePrice': {
                    number: data.estimatedBudget || 0
                },
                'EstimatedBudget': {
                    number: data.estimatedBudget || null
                },
                'ProjectTimeline': {
                    rich_text: data.projectTimeline ? [{ text: { content: data.projectTimeline } }] : []
                },
                'HeardAbout': {
                    rich_text: data.heardAbout ? [{ text: { content: data.heardAbout } }] : []
                },
                'Description': {
                    rich_text: data.description ? [{ text: { content: data.description } }] : []
                },

                // Status and Priority
                'Status': {
                    select: { name: 'New Lead' }
                },
                'Priority': {
                    select: { name: 'Medium' }
                },

                // Timestamps
                'Created Date': {
                    date: { start: new Date().toISOString() }
                },

                // Notes
                'Notes': {
                    rich_text: data.notes ? [{ text: { content: data.notes } }] : []
                },

                // Client ID for tracking
                'ClientID': {
                    rich_text: [{ text: { content: clientId } }]
                }
            }
        });

        return {
            clientId,
            name: data.name,
            email: data.email,
            companyName: data.companyName,
            phone: data.phone,

            // Address Information
            street: data.street,
            houseNumber: data.houseNumber,
            city: data.city,
            postalCode: data.postalCode,
            country: data.country,

            proposalUrl,
            contractUrl,
            urlPassword,
            interestedService: data.interestedService,
            servicePrice: data.estimatedBudget || 0,
            estimatedBudget: data.estimatedBudget,
            projectTimeline: data.projectTimeline,
            heardAbout: data.heardAbout,
            description: data.description,
            status: 'New Lead',
            priority: 'Medium',
            createdDate: new Date().toISOString(),
            notes: data.notes,
            selectedAddOns: data.selectedAddOns
        };
    } catch (error) {
        console.error('Error creating customer proposal:', error);
        throw new Error('Failed to create customer proposal');
    }
}

// Get customer proposal by Client ID
export async function getLeadByClientId(clientId: string): Promise<CustomerProposal | null> {
    try {
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            return null;
        }

        const page = response.results[0] as any;
        const props = page.properties;

        return {
            clientId,
            name: props['Name']?.title?.[0]?.text?.content || '',
            email: props['Email']?.email || '',
            companyName: props['CompanyName']?.rich_text?.[0]?.text?.content || undefined,
            phone: props['Phone']?.phone_number || undefined,

            // Address Information
            street: props['Street']?.rich_text?.[0]?.text?.content || undefined,
            houseNumber: props['HouseNumber']?.number?.toString() || undefined,
            city: props['City']?.rich_text?.[0]?.text?.content || undefined,
            postalCode: props['PostalCode']?.number?.toString() || undefined,
            country: props['Country']?.rich_text?.[0]?.text?.content || undefined,

            proposalUrl: props['ProposalURL']?.url || '',
            contractUrl: props['ContractURL']?.url || '',
            urlPassword: props['URLPassword']?.rich_text?.[0]?.text?.content || '',
            interestedService: props['InterestedService']?.select?.name || '',
            servicePrice: props['ServicePrice']?.number || 0,
            estimatedBudget: props['EstimatedBudget']?.number || undefined,
            projectTimeline: props['ProjectTimeline']?.rich_text?.[0]?.text?.content || undefined,
            heardAbout: props['HeardAbout']?.rich_text?.[0]?.text?.content || undefined,
            description: props['Description']?.rich_text?.[0]?.text?.content || undefined,
            status: props['Status']?.select?.name as CustomerProposal['status'] || 'New Lead',
            priority: props['Priority']?.select?.name as CustomerProposal['priority'] || 'Medium',
            createdDate: props['Created Date']?.date?.start || '',
            signatureDate: props['Signature Date']?.date?.start || undefined,
            notes: props['Notes']?.rich_text?.[0]?.text?.content || undefined,
            internalComments: props['Internal Comments']?.rich_text?.[0]?.text?.content || undefined,
            signatureUrl: props['SignatureURL']?.url || undefined
        };
    } catch (error) {
        console.error('Error getting lead by client ID:', error);
        throw new Error('Failed to retrieve lead');
    }
}

// Update customer proposal
export async function updateCustomerProposal(
    clientId: string,
    updates: UpdateProposalRequest
): Promise<CustomerProposal> {
    try {
        // First, get the page ID
        const lead = await getLeadByClientId(clientId);
        if (!lead) {
            throw new Error('Lead not found');
        }

        // Find the page ID
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            throw new Error('Lead not found');
        }

        const pageId = response.results[0].id;

        // Build update properties
        const updateProperties: any = {};

        if (updates.companyName !== undefined) {
            updateProperties['CompanyName'] = {
                rich_text: updates.companyName ? [{ text: { content: updates.companyName } }] : []
            };
        }

        if (updates.phone !== undefined) {
            updateProperties['Phone'] = {
                phone_number: updates.phone || null
            };
        }

        // Address Information Updates
        if (updates.street !== undefined) {
            updateProperties['Street'] = {
                rich_text: updates.street ? [{ text: { content: updates.street } }] : []
            };
        }

        if (updates.houseNumber !== undefined) {
            updateProperties['HouseNumber'] = {
                number: updates.houseNumber && !isNaN(parseFloat(updates.houseNumber)) ? parseFloat(updates.houseNumber) : null
            };
        }

        if (updates.city !== undefined) {
            updateProperties['City'] = {
                rich_text: updates.city ? [{ text: { content: updates.city } }] : []
            };
        }

        if (updates.postalCode !== undefined) {
            updateProperties['PostalCode'] = {
                number: updates.postalCode && !isNaN(parseFloat(updates.postalCode)) ? parseFloat(updates.postalCode) : null
            };
        }

        if (updates.country !== undefined) {
            updateProperties['Country'] = {
                rich_text: updates.country ? [{ text: { content: updates.country } }] : []
            };
        }

        if (updates.interestedService !== undefined) {
            updateProperties['InterestedService'] = {
                select: { name: updates.interestedService }
            };
        }

        if (updates.estimatedBudget !== undefined) {
            updateProperties['EstimatedBudget'] = {
                number: updates.estimatedBudget
            };
            updateProperties['ServicePrice'] = {
                number: updates.estimatedBudget
            };
        }

        if (updates.projectTimeline !== undefined) {
            updateProperties['ProjectTimeline'] = {
                rich_text: updates.projectTimeline ? [{ text: { content: updates.projectTimeline } }] : []
            };
        }

        if (updates.heardAbout !== undefined) {
            updateProperties['HeardAbout'] = {
                rich_text: updates.heardAbout ? [{ text: { content: updates.heardAbout } }] : []
            };
        }

        if (updates.description !== undefined) {
            updateProperties['Description'] = {
                rich_text: updates.description ? [{ text: { content: updates.description } }] : []
            };
        }

        if (updates.notes !== undefined) {
            updateProperties['Notes'] = {
                rich_text: updates.notes ? [{ text: { content: updates.notes } }] : []
            };
        }

        if (updates.internalComments !== undefined) {
            updateProperties['Internal Comments'] = {
                rich_text: updates.internalComments ? [{ text: { content: updates.internalComments } }] : []
            };
        }

        if (updates.status !== undefined) {
            updateProperties['Status'] = {
                select: { name: updates.status }
            };
        }

        if (updates.priority !== undefined) {
            updateProperties['Priority'] = {
                select: { name: updates.priority }
            };
        }

        // Update the page
        await notion.pages.update({
            page_id: pageId,
            properties: updateProperties
        });

        // Return updated lead
        return await getLeadByClientId(clientId) as CustomerProposal;
    } catch (error) {
        console.error('Error updating customer proposal:', error);
        throw new Error('Failed to update customer proposal');
    }
}

// Upload signature to S3 and store URL in Notion
export async function uploadSignatureToNotion(clientId: string, signature: string): Promise<void> {
    try {
        // Get customer information for filename
        const customer = await getLeadByClientId(clientId);
        
        // Import S3 upload function
        const { uploadSignatureToS3 } = await import('./aws-s3');
        
        // Upload signature image to S3 with customer info
        const signatureUrl = await uploadSignatureToS3(
            clientId, 
            signature, 
            customer?.companyName, 
            customer?.name
        );

        await updateCustomerProposal(clientId, {
            clientId,
            status: 'Won'
        });

        // Find the page ID and update signature URL
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            throw new Error('Lead not found');
        }

        const pageId = response.results[0].id;

        await notion.pages.update({
            page_id: pageId,
            properties: {
                'SignatureURL': {
                    rich_text: [{ text: { content: signatureUrl } }]
                },
                'Signature Date': {
                    date: { start: new Date().toISOString() }
                }
            }
        });
    } catch (error) {
        console.error('Error uploading signature:', error);
        throw new Error('Failed to upload signature');
    }
}

// Verify URL password
export async function verifyUrlPassword(clientId: string, password: string): Promise<boolean> {
    try {
        const lead = await getLeadByClientId(clientId);
        return lead?.urlPassword === password;
    } catch (error) {
        console.error('Error verifying password:', error);
        return false;
    }
}

// Session management for password protection
export function createSession(clientId: string, password: string): string {
    const sessionData = { clientId, password, timestamp: Date.now() };
    return Buffer.from(JSON.stringify(sessionData)).toString('base64');
}

export function validateSession(sessionToken: string, clientId: string): boolean {
    try {
        const sessionData = JSON.parse(Buffer.from(sessionToken, 'base64').toString());
        const isValid = sessionData.clientId === clientId &&
            (Date.now() - sessionData.timestamp) < 24 * 60 * 60 * 1000; // 24 hours
        return isValid;
    } catch {
        return false;
    }
}

// Get all customer proposals (for admin panel)
export async function getAllCustomerProposals(): Promise<CustomerProposal[]> {
    if (!CRM_DATABASE_ID) {
        throw new Error('Notion CRM database ID is not configured');
    }

    try {
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            sorts: [
                {
                    property: 'Created Date',
                    direction: 'descending',
                },
            ],
        });

        return response.results.map(mapCrmLeadToCustomerProposal);
    } catch (error) {
        console.error('Error getting all customer proposals:', error);
        throw new Error('Failed to retrieve customer proposals');
    }
}

// Update lead status (legacy support)
export async function updateLeadStatus(
    clientId: string,
    status: string,
    addOns?: string[],
    comments?: string,
    contractUrl?: string
): Promise<void> {
    const updates: Partial<CustomerProposal> = {
        status: status as CustomerProposal['status'],
    };

    if (addOns) {
        updates.selectedAddOns = addOns;
    }

    if (comments) {
        updates.internalComments = comments;
    }

    if (contractUrl) {
        updates.contractUrl = contractUrl;
    }

    await updateCustomerProposal(clientId, updates as UpdateProposalRequest);
}

// Calculate total price utility
export function calculateTotalPrice(_packageName: string, _selectedAddOns: string[]): number {
    // This will be imported from services config
    // For now, return a placeholder
    return 0;
}

// Verify password for client access
export async function verifyClientPassword(clientId: string, password: string): Promise<boolean> {
    try {
        const proposal = await getLeadByClientId(clientId);
        return proposal?.urlPassword === password;
    } catch (error) {
        console.error('Error verifying password:', error);
        return false;
    }
}

// Create lead from contact form (enhanced version)
export async function createLeadFromContact(formData: {
    name: string;
    email: string;
    phone?: string;
    company?: string;
    message: string;
    selectedService?: string;
    estimatedBudget?: string;
    estimatedBudgetNumeric?: number;
    projectTimeline?: string;
    additionalServices?: string[];
    source?: string;
}): Promise<CustomerProposal> {
    const clientId = uuidv4();
    const urlPassword = generateUrlPassword();
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    // Ensure proper URL formatting with www subdomain for production
    let formattedBaseUrl = baseUrl;
    if (!baseUrl.startsWith('http')) {
        formattedBaseUrl = `https://www.${baseUrl}`;
    } else if (baseUrl.includes('innovatio-pro.com') && !baseUrl.includes('www.')) {
        formattedBaseUrl = baseUrl.replace('https://innovatio-pro.com', 'https://www.innovatio-pro.com');
    }

    const proposalUrl = `${formattedBaseUrl}/de/proposal/${clientId}`;
    const contractUrl = `${formattedBaseUrl}/de/contract/${clientId}`;

    const notesText = [
        formData.message,
        formData.estimatedBudget ? `Budget: ${formData.estimatedBudget}` : '',
        formData.projectTimeline ? `Timeline: ${formData.projectTimeline}` : '',
        formData.additionalServices?.length ? `Additional Services: ${formData.additionalServices.join(', ')}` : '',
        formData.source ? `Source: ${formData.source}` : ''
    ].filter(Boolean).join('\n\n');

    if (!CRM_DATABASE_ID) {
        throw new Error('Notion CRM database ID is not configured');
    }

    try {
        const response = await notion.pages.create({
            parent: {
                database_id: CRM_DATABASE_ID,
            },
            properties: {
                'Name': {
                    title: [{ text: { content: formData.name } }],
                },
                'Email': {
                    email: formData.email,
                },
                'Phone': {
                    phone_number: formData.phone || null,
                },
                'CompanyName': {
                    rich_text: [{ text: { content: formData.company || '' } }],
                },
                'ClientID': {
                    rich_text: [{ text: { content: clientId } }],
                },
                'InterestedService': {
                    select: { name: formData.selectedService || 'mvp' },
                },
                'ServicePrice': {
                    number: formData.estimatedBudgetNumeric || 0,
                },
                'EstimatedBudget': {
                    number: formData.estimatedBudgetNumeric || null,
                },
                'ProjectTimeline': {
                    rich_text: formData.projectTimeline ? [{ text: { content: formData.projectTimeline } }] : [],
                },
                'ProposalURL': {
                    url: proposalUrl,
                },
                'ContractURL': {
                    url: contractUrl,
                },
                'URLPassword': {
                    rich_text: [{ text: { content: urlPassword } }],
                },
                'Status': {
                    select: { name: 'New Lead' },
                },
                'Notes': {
                    rich_text: [{ text: { content: notesText } }],
                },
                'Description': {
                    rich_text: [{ text: { content: formData.message } }],
                },
                'Created Date': {
                    date: { start: new Date().toISOString() },
                },
                'Priority': {
                    select: { name: 'Medium' },
                }
            },
        });

        return mapCrmLeadToCustomerProposal(response);
    } catch (error) {
        console.error('Error creating lead from contact:', error);
        throw new Error('Failed to create lead in CRM');
    }
}

// Create lead capture entry (simplified - no proposals/contracts)
export async function createLeadCapture(formData: {
    fullName: string;
    email: string;
    company?: string;
    mvpGoal: string;
    timeline: string;
    budget?: string;
    additionalNotes?: string;
}): Promise<{ success: boolean; leadId?: string }> {
    try {
        if (!CRM_DATABASE_ID) {
            throw new Error('Notion CRM database ID is not configured');
        }

        const leadId = uuidv4();

        const response = await notion.pages.create({
            parent: {
                database_id: CRM_DATABASE_ID,
            },
            properties: {
                'Name': {
                    title: [{ text: { content: formData.fullName } }],
                },
                'Email': {
                    email: formData.email,
                },
                'CompanyName': {
                    rich_text: [{ text: { content: formData.company || '' } }],
                },
                'ClientID': {
                    rich_text: [{ text: { content: leadId } }],
                },
                'InterestedService': {
                    select: { name: formData.mvpGoal || 'lead_capture' },
                },
                'ProjectTimeline': {
                    rich_text: [{ text: { content: formData.timeline || '' } }],
                },
                'EstimatedBudget': {
                    number: 0,
                },
                'Status': {
                    select: { name: 'Lead Capture' },
                },
                'Priority': {
                    select: { name: 'Medium' },
                },
                'Notes': {
                    rich_text: [{ text: { content: formData.additionalNotes || '' } }],
                },
                'Description': {
                    rich_text: [{ text: { content: `Lead Capture Form Submission\n\nGoal: ${formData.mvpGoal}\nTimeline: ${formData.timeline}\nBudget: ${formData.budget || 'Not specified'}\n\nNotes:\n${formData.additionalNotes || 'None provided'}` } }],
                },
                'Created Date': {
                    date: { start: new Date().toISOString() },
                },
                'HeardAbout': {
                    rich_text: [{ text: { content: 'Lead Capture Form - Pricing Section' } }],
                },
                // No ProposalURL, ContractURL, or URLPassword for lead capture
            },
        });

        console.log('✅ Lead Capture entry created successfully:', response.id);

        return {
            success: true,
            leadId: leadId
        };

    } catch (error) {
        console.error('❌ Error creating lead capture entry:', error);
        return {
            success: false
        };
    }
} 