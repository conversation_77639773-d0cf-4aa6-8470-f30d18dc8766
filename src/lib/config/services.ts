// Innovatio-Pro Service Configuration
import { ServicePackage, AddOn } from '@/types/proposal';

// Hauptservices basierend auf den aktuellen innovatio-pro Paketen
export const SERVICES = {
    prototype: {
        id: 'prototype',
        name: 'Rapid Prototype',
        description: 'Validate your app idea fast with investor-ready prototypes',
        basePrice: 3000,
        originalPrice: 3500,
        timeframe: '1-2 weeks',
        category: 'development',
        features: [
            'Interactive clickable prototypes',
            'UX/UI design workshops',
            'Feature prioritization sessions',
            'MVP roadmap preparation',
            'Investor pitch materials',
            'Clean code architecture planning',
            'Always in contact for feedback'
        ]
    },
    mvp: {
        id: 'mvp',
        name: 'MVP Development',
        description: 'Build your launch-ready MVP with scalable Flutter architecture',
        basePrice: 7500,
        originalPrice: 9000,
        timeframe: '4-6 weeks',
        category: 'development',
        features: [
            'Full Flutter app development',
            'Firebase backend setup',
            'Scalable architecture design',
            'AI integration pathways',
            'Authentication & security layers',
            'Clean code & best practices',
            'Always in contact during development',
            'Comprehensive testing & debugging'
        ]
    },
    'saas-growth': {
        id: 'saas-growth',
        name: 'SaaS Growth Package',
        description: 'Upgrade your SaaS product to full enterprise-grade scalability',
        basePrice: 15000,
        originalPrice: 18000,
        timeframe: '6-10 weeks',
        category: 'development',
        features: [
            'Flutter app expansion & enhancement',
            'Backend optimization (Firebase/Supabase)',
            'Admin panel integration',
            'SaaS-grade cloud infrastructure',
            'Full SaaS architecture consulting',
            'Clean code refactoring & optimization',
            'Always in contact for scaling decisions',
            'Performance monitoring & optimization'
        ]
    },
    consulting: {
        id: 'consulting',
        name: 'Technical Consulting',
        description: 'CTO-level strategy and technical leadership',
        basePrice: 150,
        originalPrice: 200,
        timeframe: 'Ongoing partnership',
        category: 'consulting',
        isHourly: true,
        features: [
            'CTO-level strategy at 80% less cost',
            'Technology roadmaps & reviews',
            'Direct expert decision-making',
            'Architecture design & optimization',
            'Team mentoring & guidance',
            'Technical due diligence',
            'Scalability planning',
            'Best practices implementation'
        ]
    }
} as const;

// Add-ons für innovatio-pro Services - updated for current services
export const ADD_ONS = [
    {
        id: 'aiIntegration',
        name: 'AI Integration & Features',
        description: 'Smart AI features and machine learning capabilities',
        price: 2500,
        category: 'technical' as const,
        compatibleServices: ['prototype', 'mvp', 'saas-growth', 'consulting']
    },
    {
        id: 'adminPanel',
        name: 'Admin Panel & Dashboard',
        description: 'Complete administrative interface for content management',
        price: 1800,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'saas-growth', 'consulting']
    },
    {
        id: 'paymentGateway',
        name: 'Payment Gateway Integration',
        description: 'Secure payment processing with multiple providers',
        price: 1200,
        category: 'integration' as const,
        compatibleServices: ['mvp', 'saas-growth', 'consulting']
    },
    {
        id: 'analyticsReporting',
        name: 'Advanced Analytics & Reporting',
        description: 'Comprehensive analytics dashboard and reporting system',
        price: 1500,
        category: 'technical' as const,
        compatibleServices: ['prototype', 'mvp', 'saas-growth', 'consulting']
    },
    {
        id: 'multiTenant',
        name: 'Multi-Tenant Architecture',
        description: 'Enterprise-grade multi-tenant SaaS infrastructure',
        price: 3000,
        category: 'technical' as const,
        compatibleServices: ['saas-growth', 'consulting']
    },
    {
        id: 'apiIntegration',
        name: 'Third-Party API Integrations',
        description: 'Integration with external services and APIs',
        price: 800,
        category: 'integration' as const,
        compatibleServices: ['prototype', 'mvp', 'saas-growth', 'consulting']
    },
    {
        id: 'securityAudit',
        name: 'Security Audit & Hardening',
        description: 'Comprehensive security assessment and implementation',
        price: 1000,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'saas-growth', 'consulting']
    },
    {
        id: 'performanceOptimization',
        name: 'Performance Optimization',
        description: 'Advanced performance tuning and optimization',
        price: 900,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'saas-growth', 'consulting']
    },
    {
        id: 'cloudInfrastructure',
        name: 'Cloud Infrastructure Setup',
        description: 'Production-ready cloud deployment and DevOps',
        price: 1400,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'saas-growth', 'consulting']
    },
    {
        id: 'mobileApp',
        name: 'Flutter Mobile App',
        description: 'Flutter cross-platform app development for iOS and Android',
        price: 2200,
        category: 'development' as const,
        compatibleServices: ['prototype', 'mvp', 'saas-growth', 'consulting']
    }
] as const;

// Budget ranges for contact forms
export const BUDGET_RANGES = [
    { id: 'below5k', label: '💰 Below €5,000', value: 'below5k' },
    { id: '5to15k', label: '💎 €5,000 - €15,000', value: '5to15k' },
    { id: '15to30k', label: '🚀 €15,000 - €30,000', value: '15to30k' },
    { id: 'above30k', label: '⭐ €30,000+', value: 'above30k' },
    { id: 'notSure', label: '❓ Not sure yet', value: 'notSure' }
] as const;

// Timeline options
export const TIMELINE_OPTIONS = [
    { id: 'asap', label: '🚀 As soon as possible', value: 'asap' },
    { id: '1-3months', label: '📅 1-3 months', value: '1-3months' },
    { id: '3-6months', label: '⏳ 3-6 months', value: '3-6months' },
    { id: 'flexible', label: '🔄 Timeline is flexible', value: 'flexible' }
] as const;

// Source options
export const SOURCE_OPTIONS = [
    { id: 'google', label: 'Google Search', value: 'google' },
    { id: 'linkedin', label: 'LinkedIn', value: 'linkedin' },
    { id: 'referral', label: 'Referral', value: 'referral' },
    { id: 'website', label: 'Direct Website', value: 'website' },
    { id: 'social', label: 'Social Media', value: 'social' },
    { id: 'other', label: 'Other', value: 'other' }
] as const;

// Helper functions
export const getServiceById = (serviceId: string) => {
    return SERVICES[serviceId as keyof typeof SERVICES];
};

export const getServicePrice = (serviceId: string, isHourly = false) => {
    const service = getServiceById(serviceId);
    return service ? service.basePrice : 0;
};

export const getAllServices = () => {
    return Object.values(SERVICES);
};

export const getServicesByCategory = (category: string) => {
    return Object.values(SERVICES).filter(service => service.category === category);
};

export const getAddOnById = (addOnId: string) => {
    return ADD_ONS.find(addOn => addOn.id === addOnId);
};

export const getCompatibleAddOns = (serviceId: string) => {
    return ADD_ONS.filter(addOn =>
        addOn.compatibleServices.includes(serviceId)
    );
};

export const calculateTotalPrice = (serviceId: string, selectedAddOnIds: string[]): number => {
    const service = getServiceById(serviceId);
    const basePrice = service ? service.basePrice : 0;

    const addOnsPrice = selectedAddOnIds.reduce((total, addOnId) => {
        const addOn = getAddOnById(addOnId);
        return total + (addOn ? addOn.price : 0);
    }, 0);

    return basePrice + addOnsPrice;
};

export const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('de-DE', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    }).format(price);
};

export const formatServiceForNotion = (serviceId: string) => {
    const service = getServiceById(serviceId);
    return service ? service.name : 'Custom Request';
};

export const getNotionServiceOptions = () => {
    return Object.values(SERVICES).map(service => ({
        id: service.id,
        name: service.name,
        price: service.basePrice
    }));
};

// Service validation
export const isValidServiceId = (serviceId: string): serviceId is keyof typeof SERVICES => {
    return serviceId in SERVICES;
};

export const getServiceDisplayName = (serviceId: string): string => {
    const service = getServiceById(serviceId);
    return service ? service.name : 'Unknown Service';
};

// Export service IDs for easy reference
export const SERVICE_IDS = {
    PROTOTYPE: 'prototype',
    MVP: 'mvp',
    SAAS_GROWTH: 'saas-growth',
    CONSULTING: 'consulting'
} as const;

// User-friendly German service mapping
export const GERMAN_SERVICE_MAPPING = {
    'app-neuentwicklung': 'mvp',
    'app-maintenance': 'saas-growth',
    'app-pruefung': 'consulting',
    'app-modernisierung': 'saas-growth',
    'beratung': 'consulting',
    'sonstiges': 'mvp'
} as const;

// Convert German service ID to technical service ID for Notion
export const mapGermanServiceToTechnical = (germanServiceId: string): string => {
    return GERMAN_SERVICE_MAPPING[germanServiceId as keyof typeof GERMAN_SERVICE_MAPPING] || 'mvp';
};

// Get display name for German services
export const getGermanServiceDisplayName = (germanServiceId: string): string => {
    const displayNames = {
        'app-neuentwicklung': 'App Neuentwicklung',
        'app-maintenance': 'App Maintenance',
        'app-pruefung': 'App Prüfung',
        'app-modernisierung': 'App Modernisierung',
        'beratung': 'Beratung',
        'sonstiges': 'Sonstiges'
    };
    return displayNames[germanServiceId as keyof typeof displayNames] || 'Unbekannter Service';
}; 