import 'server-only'

// Define Locale type directly here to avoid import issues
export type Locale = 'en' | 'de' | 'ru' | 'tr' | 'ar'

// Removed dictionaries object as it's now handled in dictionaries.ts

export interface Dictionary {
  locale: string;
  navigation: {
    home: string;
    about: string;
    services: string;
    prices: string;
    testimonials: string;
    blog?: string;
    contact: string;
    solutions?: string;
    login?: string;
    signUp?: string;
    dashboard?: string;
    account?: string;
    logout?: string;
  };
  blog?: {
    title: string;
    description: string;
    categoriesTitle: string;
    categoriesDescription: string;
    subscriptionTitle: string;
    subscriptionDescription: string;
    readMore: string;
    noArticles: string;
    articlesFound: string;
    publishedAt: string;
    author: string;
    readingTime: string;
    views: string;
    tags: string;
    categories: {
      all: string;
      flutter: string;
      mobile: string;
      ai: string;
      performance: string;
      caseStudies: string;
      trends: string;
    };
  };
  hero: {
    title: string;
    subtitle: string;
    rating?: string;
    responseTime?: string;
    trustVisual?: {
      satisfaction?: string;
      appsLaunched?: string;
      fromTo?: string;
      trust?: string;
      additional?: string;
      industries?: {
        doctors?: string;
        crafts?: string;
        startups?: string;
      };
    };
    painPoint?: {
      mobile: {
        line1: string;
        line2: string;
      };
      desktop: {
        trigger: string;
        problems: string;
        solution: string;
        mainMessage?: {
          text: string;
          parts: string[];
        };
      };
    };
    tagline?: string;
    description: string;
    cta: {
      primary?: string;
      secondary?: string;
      calculator?: string;
      startProject: string;
      freeConsultation: string;
      calculateCost: string;
      consultation?: string;
      consultationSubtext?: string;
    };
    trustedBy: {
      title: string;
    };
    typeAnimation?: (string | number)[];
    typing?: {
      sequence1?: string;
      sequence2?: string;
      sequence3?: string;
      businessGrowth?: string;
      digitalSolution?: string;
      userExperience?: string;
      technologyInnovation?: string;
    };
    metrics?: {
      development?: {
        label: string;
        value: string;
        description: string;
        howTitle: string;
        howExplanation: string;
      };
      timeToMarket?: {
        label: string;
        value: string;
        description: string;
        howTitle: string;
        howExplanation: string;
      };
      costSaving?: {
        label: string;
        value: string;
        description: string;
        howTitle: string;
        howExplanation: string;
      };
      lessBugs?: {
        label: string;
        value: string;
        description: string;
        howTitle: string;
        howExplanation: string;
      };
    };
    solutions?: {
      streamlinedOperations?: {
        title: string;
        description: string;
      };
      enhancedUserExperience?: {
        title: string;
        description: string;
      };
      dataInsights?: {
        title: string;
        description: string;
      };
      scalableArchitecture?: {
        title: string;
        description: string;
      };
    };
    businessValue?: {
      headline?: string;
      description?: string;
    };
    enterpriseFeatures?: {
      security?: {
        title?: string;
        description?: string;
      };
      architecture?: {
        title?: string;
        description?: string;
      };
      roi?: {
        title?: string;
        description?: string;
      };
      support?: {
        title?: string;
        description?: string;
      };
    };
    companyTrust?: {
      title?: string;
    };
  };
  painPointSection?: {
    familiarQuestion: string;
    valueProposition?: string;
    valueSubtitle?: string;
    infiniteScrollPainPoints?: string[];
    mainTitle: string;
    mainSubtitle: string;
    solutionsTitle: string;
    solutionsSubtitle: string;
    costComparisonTitle: string;
    traditionalLabel: string;
    ourSolutionLabel: string;
    savingsText: string;
    provenText?: string;
    ctaText: string;
    comparisonHeaders?: {
      criteria: string;
      traditional: string;
      ourSolution: string;
    };
    comparisonLabels?: {
      cost: string;
      time: string;
      architecture: string;
      architectureTraditional: string;
      architectureOurs: string;
      security: string;
      securityTraditional: string;
      securityOurs: string;
      ai: string;
      aiTraditional: string;
      aiOurs: string;
      team: string;
      teamTraditional: string;
      teamOurs: string;
      maintenance: string;
      maintenanceTraditional: string;
      maintenanceOurs: string;
      scalability: string;
      scalabilityTraditional: string;
      scalabilityOurs: string;
      risk: string;
      riskTraditional: string;
      riskOurs: string;
    };
    problems: {
      missedOpportunities: {
        title: string;
        points: string[];
      };
      burningBudgets: {
        title: string;
        points: string[];
      };
      stagnatingScaling: {
        title: string;
        points: string[];
      };
    };
    solutions: {
      readyArchitecture: {
        title: string;
        description: string;
        value: string;
      };
      seniorExpertise: {
        title: string;
        description: string;
        value: string;
      };
      aiDevelopment: {
        title: string;
        description: string;
        value: string;
      };
      enterpriseSecurity: {
        title: string;
        description: string;
        value: string;
      };
    };
    costComparison: {
      traditional: {
        amount: string;
        timeline: string;
      };
      ourSolution: {
        amount: string;
        timeline: string;
      };
    };
  };
  usp?: {
    badge?: string;
    title: string;
    subtitle: string;
    description: string;
    features: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
    cta: {
      primary: string;
      secondary: string;
    };
    trustIndicators?: {
      freeConsultation: string;
      nonBinding: string;
      thirtyMinutes: string;
    };
  };
  ourProcess?: {
    title: string;
    subtitle?: string;
    description: string;
    badge?: string;
    deliverables?: string;
    cta: {
      title: string;
      description: string;
      primaryButton: string;
      secondaryButton?: string;
    };
    steps: {
      planning: {
        title: string;
        description: string;
        duration: string;
        deliverables?: string[];
        features: string[];
      };
      design: {
        title: string;
        description: string;
        duration: string;
        deliverables?: string[];
        features: string[];
      };
      development: {
        title: string;
        description: string;
        duration: string;
        deliverables?: string[];
        features: string[];
      };
      launch: {
        title: string;
        description: string;
        duration: string;
        deliverables?: string[];
        features: string[];
      };
    };
  };
  leadCaptureForm?: {
    headline: string;
    introduction: string;
    steps: {
      personal: string;
      project: string;
      final: string;
    };
    stepDescriptions: {
      personal: string;
      project: string;
      final: string;
    };
    form: {
      fullName: string;
      email: string;
      company: string;
      mvpGoal: {
        label: string;
        placeholder: string;
        options: {
          prototyping: string;
          mvpLaunch: string;
          saasScaling: string;
          aiIntegration: string;
          consulting: string;
          notSure: string;
        };
      };
      timeline: {
        label: string;
        placeholder: string;
        options: {
          asap: string;
          fourToSix: string;
          threeMonths: string;
          undecided: string;
        };
      };
      budget: {
        label: string;
        placeholder: string;
        options: {
          below10k: string;
          "10to20k": string;
          "20to50k": string;
          above50k: string;
          notSure: string;
        };
      };
      additionalNotes: {
        label: string;
        placeholder: string;
      };
      whatHappensNext: {
        title: string;
        steps: string[];
      };
      validation: {
        nameRequired: string;
        emailRequired: string;
        emailInvalid: string;
        goalRequired: string;
        timelineRequired: string;
      };
    };
    navigation: {
      back: string;
      next: string;
      submit: string;
      submitting: string;
      stepOf: string;
    };
    successMessage: {
      title: string;
      description: string;
      scheduleCall: string;
      whatsapp: string;
      backToSite: string;
      nextSteps: {
        title: string;
        steps: string[];
      };
    };
    footer: {
      preferDirectContact: string;
      whatsapp: string;
      email: string;
    };
  };
  prices: {
    title: string;
    subtitle: string;
    description?: string;
    metaTitle?: string;
    metaDescription?: string;
    caseStudyTitle?: string;
    caseStudyDescription?: string;
    promotionTitle?: string;
    promotionDescription?: string;
    calculatorTitle?: string;
    calculatorDescription?: string;
    calculatorButton?: string;
    discussButton?: string;
    contactButton?: string;
    pricingDisclaimer?: string;
    priceVariesInfo?: string;
    packages?: {
      mvp?: {
        title: string;
        timeframe: string;
        benefitSentence?: string;
        coolFactor?: string;
        description: string;
        cta?: string;
        features: string[];
        price?: string;
        badge?: string;
        benefits?: string[];
        trustLine?: string;
        quickStart?: string;
      };
      prototype?: {
        title: string;
        timeframe: string;
        benefitSentence?: string;
        coolFactor?: string;
        description: string;
        cta?: string;
        features: string[];
        price?: string;
        badge?: string;
        benefits?: string[];
        trustLine?: string;
        quickStart?: string;
      };
      architecture?: {
        title: string;
        timeframe: string;
        description: string;
        cta?: string;
        features: string[];
        price?: string;
        badge?: string;
        benefits?: string[];
        trustLine?: string;
      };
      consulting?: {
        title: string;
        timeframe: string;
        description: string;
        cta?: string;
        features: string[];
        price?: string;
        badge?: string;
        benefits?: string[];
        trustLine?: string;
      };
      saasGrowth?: {
        title: string;
        timeframe: string;
        benefitSentence?: string;
        coolFactor?: string;
        description: string;
        cta?: string;
        features: string[];
        price?: string;
        badge?: string;
        benefits?: string[];
        trustLine?: string;
        quickStart?: string;
      };
      landingpage?: {
        title: string;
        timeframe: string;
        description: string;
        features: string[];
        price?: string;
        badge?: string;
        benefits?: string[];
        trustLine?: string;
      };
    };
    calculator?: {
      title?: string;
      subtitle?: string;
      back?: string;
      selectServiceType?: string;
      stepIndicator?: string;
      continueButton?: string;
      getQuoteButton?: string;
      startOver?: string;
      calculatorTypes?: {
        mvp?: {
          name?: string;
          description?: string;
          tagline?: string;
        };
        prototype?: {
          name?: string;
          description?: string;
          tagline?: string;
        };
        homepage?: {
          name?: string;
          description?: string;
          tagline?: string;
        };
        consulting?: {
          name?: string;
          description?: string;
          tagline?: string;
        };
      };
    };
    otherInquiries?: {
      title: string;
      description: string;
      bookCall: string;
      whatsappContact: string;
    };
    fullDetails?: string;
    allIncludedFeatures?: string;
    backendOptions?: string;
    showDetails?: string;
    valueProps?: {
      aiDevelopment?: {
        title: string;
        description: string;
      };
      backend?: {
        title: string;
        description: string;
      };
      quality?: {
        title: string;
        description: string;
      };
    };
  };
  solutionsPortfolio: {
    description: any;
    title: string;
    subtitle: string;
    clickInstruction: string;
    imageCounter: string; // Added for "Image {current} of {total}"
    keyFeatures: string;
    problemsSolved: string;
    categories: {
      all: string;
      aiAssistant: string;
      foodDelivery: string;
      hospitality: string;
      medical: string;
      lifestyle: string;
      automotive: string;
    };
    backButton?: string; // Added
    relatedProjects?: string; // Added
    noRelatedProjects?: string; // Added
    clickToExpand?: string; // Added
    noImagesAvailable?: string; // Added
    items: {
      spotzAiAssistant: {
        title: string;
        description: string;
        imageAlt: string;
      };
      foodDelivery: {
        title: string;
        description: string;
        imageAlt: string;
      };
      hostIQ: {
        title: string;
        description: string;
        imageAlt: string;
      };
      medicalApp: {
        title: string;
        description: string;
        imageAlt: string;
      };
      lifestyleApp: {
        title: string;
        description: string;
        imageAlt: string;
      };
      nearby: {
        title: string;
        description: string;
        imageAlt: string;
      };
      toggCarControl: {
        title: string;
        description: string;
        imageAlt: string;
      };
      lifestylePlatform: {
        title: string;
        description: string;
        imageAlt: string;
      };
      // Add optional default for fallback
      default?: {
        imageAlt: string;
      };
    };
    solutions: {
      aiAssistant: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      foodDelivery: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      hospitality: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      medical: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      lifestyle: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      automotive: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
    };
  };
  about: {
    title: string;
    subtitle: string;
    description?: string;
    motto?: string;
    founderName?: string;
    founderRole?: string;
    whyDifferentTitle?: string;
    whyDifferentText?: string;
    secretTitle?: string;
    secretText?: string;
    vision: string;
    visionDesc: string;
    mission: string;
    missionDesc: string;
    founderTitle: string;
    founderDesc: string;
    skills: string;
    projects: string;
    testimonials: string;
    experience: string;
    clients: string;
    transformBusiness: string;
    createSolutions: string;
    stayingAhead: string;
    exceptionalUX: string;
    highPerformance: string;
    solvingChallenges: string;
    flutterExpert: string;
    webDevAdvanced: string;
    aiIntegration: string;
    projectsCompleted: string;
    personalDedication: string;
    dedication: string;
    qualityFocused: string;
    personalService: string;
    focusedApproach: string;
    dedicatedService: string;
    clientReviews: string;
    focusedService: string;
    longTerm: string;
    partnerships: string;
    privacyFocused: string;
    secureServices: string;
    personalAttention: string;
    dedicatedDeveloper: string;
    securityFocused: string;
    privacyRespected: string;
    quality: string;
    averageDelivery: string;
    reasons?: {
      fastDelivery?: {
        title: string;
        description: string;
      };
      flutterSpecialization?: {
        title: string;
        description: string;
      };
      founderInvolved?: {
        title: string;
        description: string;
      };
      scalableArchitecture?: {
        title: string;
        description: string;
      };
      aiReady?: {
        title: string;
        description: string;
      };
      strategicGuidance?: {
        title: string;
        description: string;
      };
    };
    metrics?: {
      yearsExperience: string;
      projectsCompleted: string;
      happyClients: string;
      clientRating: string;
      deliveryTimeframe: string;
      personalValue: string;
      qualityValue: string;
      longTermValue: string;
    };
  };
  advantages: {
    title: string;
    subtitle: string;
    speed: string;
    speedDesc: string;
    stability: string;
    stabilityDesc: string;
    cost: string;
    costDesc: string;
    timeToMarket: string;
    timeToMarketDesc: string;
    aiIntegration: string;
    aiIntegrationDesc: string;
    development: string;
    developmentTime: string;
    developmentDesc: string;
    developmentEfficiency: string;
    mvp: string;
    mvpTime: string;
    mvpDesc: string;
    timeToMarketReduction: string;
    prototype: string;
    prototypeTime: string;
    prototypeDesc: string;
    conceptValidation: string;
    qa: string;
    qaTime: string;
    qaDesc: string;
    bugFreeRate: string;
    consulting: string;
    consultingTime: string;
    consultingDesc: string;
    technicalImprovement: string;
    homepage?: string;
    homepageTime?: string;
    homepageDesc?: string;
    developmentInfo: {
      title: string;
      simpleApp: {
        title: string;
        examples: string;
        features: string[];
        timeline: {
          total: string;
          frontend: string;
          backend: string;
          testing: string;
        };
      };
      mediumApp: {
        title: string;
        examples: string;
        features: string[];
        timeline: {
          total: string;
          frontend: string;
          backend: string;
          testing: string;
        };
      };
      complexApp: {
        title: string;
        examples: string;
        features: string[];
        timeline: {
          total: string;
          frontend: string;
          backend: string;
          testing: string;
        };
      };
      factors: {
        title: string;
        teamSize: string;
        technology: string;
        requirements: string;
        testing: string;
        design: string;
      };
      summary: string;
      aiComparison: string;
    };
  };
  serviceSection: {
    title: string;
    subtitle: string;
    description: string;
    viewAll: string;
    mobileApps?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    webDev?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    uiuxDesign?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    consulting?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    aiSolutions?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    prototype?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    mvp?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    fullstack?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    homepage?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    landingpage?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    qa?: {
      title: string;
      description: string;
      benefits?: string[];
    };
  };
  services: {
    title: string;
    subtitle: string;
    description: string;
    badge?: string;
    frontend: string;
    frontendDesc: string;
    backend: string;
    backendDesc: string;
    mobile: string;
    mobileDesc: string;
    ai: string;
    aiDesc: string;
    mobileApps: string;
    mobileAppsDesc: string;
    webDev: string;
    webDevDesc: string;
    uiuxDesign: string;
    uiuxDesignDesc: string;
    consulting: string;
    consultingDesc: string;
    aiSolutions: string;
    aiSolutionsDesc: string;
    viewAll: string;
    cta?: {
      button: string;
      description: string;
    };
  };
  packages: {
    prototype: {
      title: string;
      timeframe: string;
      price: string;
      badge: string;
      description: string;
      keyFeatures: string[];
      detailedFeatures: string[];
      cta: string;
      icon?: any; // For the icon component
      color?: string;
    };
    mvp: {
      title: string;
      timeframe: string;
      price: string;
      badge: string;
      description: string;
      keyFeatures: string[];
      detailedFeatures: string[];
      cta: string;
      icon?: any; // For the icon component
      color?: string;
    };
    professional: {
      title: string;
      timeframe: string;
      price: string;
      badge: string;
      description: string;
      keyFeatures: string[];
      detailedFeatures: string[];
      cta: string;
      icon?: any; // For the icon component
      color?: string;
    };
    enterprise: {
      title: string;
      timeframe: string;
      price: string;
      badge: string;
      description: string;
      keyFeatures: string[];
      detailedFeatures: string[];
      cta: string;
      icon?: any; // For the icon component
      color?: string;
    };
  };
  portfolio: {
    title: string;
    subtitle: string;
    description?: string;
    badge?: string;
    all: string;
    screenshot: string;
    screenshots: string;
    viewDetails: string;
    viewAllProjects: string;
    problemsWeSolve: string;
    noSectorsFound: string;
    labels?: {
      technologies?: string;
      keyFeatures?: string;
    };
    projects?: Array<{
      id: string;
      title: string;
      description: string;
      category: string;
      technologies?: string[];
      features?: string[];
    }>;
    categories: {
      aiAssistant: string;
      foodDelivery: string;
      hospitality: string;
      business: string;
      social: string;
      automotive: string;
    };
    sectors: {
      assistant: string;
      food: string;
      hospitality: string;
      lifestyle: string;
      social: string;
      automotive: string;
      medical: string;
      business: string;
    };
    sectorDescriptions: {
      assistant: string;
      food: string;
      hospitality: string;
      lifestyle: string;
      social: string;
      automotive: string;
      medical: string;
      business: string;
    };
    problems: {
      assistant: {
        1: string;
        2: string;
        3: string;
      };
      food: {
        1: string;
        2: string;
        3: string;
      };
      hospitality: {
        1: string;
        2: string;
        3: string;
      };
      lifestyle: {
        1: string;
        2: string;
        3: string;
      };
      social: {
        1: string;
        2: string;
        3: string;
      };
      automotive: {
        1: string;
        2: string;
        3: string;
      };
      medical: {
        1: string;
        2: string;
        3: string;
      };
      business: {
        1: string;
        2: string;
        3: string;
      };
    };
    cta?: {
      startProject?: string;
    };
  };
  clients: {
    title: string;
    subtitle: string;
    visitWebsite: string;
  };
  testimonials: {
    title: string;
    subtitle: string;
    description?: string;
    readMore: string;
    readLess: string;
  };
  contact: {
    title: string;
    subtitle: string;
    description?: string;
    name: string;
    email: string;
    phone: string;
    message: string;
    send: string;
    yourName: string;
    yourEmail: string;
    subject: string;
    howCanIHelp: string;
    yourMessageHere: string;
    messageSent: string;
    messageSentDesc: string;
    messageFailed: string;
    messageFailedDesc: string;
    sendingMessage: string;
    contactInfo: string;
    address: string;
    writeUs: string;
    callUs: string;
    orSchedule: string;
    // New properties for floating contact
    whatsapp: string;
    linkedin: string;
    github: string;
    // Service selection properties
    services?: {
      selectService?: string;
      prototype?: string;
      mvp?: string;
      saasGrowth?: string;
      custom?: string;
    };
    serviceLabels?: {
      selectService?: string;
      prototype?: string;
      mvp?: string;
      saasGrowth?: string;
      custom?: string;
    };
  };
  cookies: {
    title: string;
    description: string;
    acceptAll: string;
    decline: string;
    customize: string;
    necessary: string;
    necessaryDesc: string;
    analytics: string;
    analyticsDesc: string;
    marketing: string;
    marketingDesc: string;
    functional: string;
    functionalDesc: string;
    save: string;
    settings: string;
    close: string;
    cookiePolicy: string;
    privacyPolicy: string;
  };
  footer: {
    copyright: string;
    description: string;
    quickLinks: string;
    footerContact: string;
    newsletter: string;
    newsletterDesc: string;
    emailPlaceholder: string;
    subscribe: string;
    builtWith: string;
    and: string;
    downloadCV: string;
    englishCV: string;
    germanCV: string;
  };
  heroParallax?: {
    title: string;
    subtitle: string;
    products: {
      mobileApp: string;
      webDev: string;
      uiux: string;
      ecommerce: string;
      ai: string;
      cloud: string;
      devops: string;
      dataAnalytics: string;
      blockchain: string;
      arvr: string;
      customSoftware: string;
      mobileGame: string;
      iot: string;
      api: string;
      cybersecurity: string;
    };
  };
  featuresSection: {
    features: {
      title: string;
      description: string;
      icon:
      | "IconTerminal2"
      | "IconEaseInOut"
      | "IconCurrencyDollar"
      | "IconCloud"
      | "IconRouteAltLeft"
      | "IconHelp"
      | "IconAdjustmentsBolt"
      | "IconHeart"; 
    }[];
  };
  faq: {
    title: string;
    subtitle: string;
    description: string;
    showMore: string;
    showLess: string;
    items: Array<{
      id: string;
      question: string;
      answer: string;
    }>;
  };
  aiEnhanced?: {
    badge?: string;
    title: string;
    subtitle: string;
    businessBenefits: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
    processTitle: string;
    aiFeatures: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
  };
}

// getDictionary function moved to dictionaries.ts
