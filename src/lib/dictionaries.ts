import { Dictionary } from './dictionary';

// Use require for JSON imports to avoid module resolution issues
const dictionaries = {
  en: require('../dictionaries/en.json') as Dictionary,
  de: require('../dictionaries/de.json') as Dictionary,
  ru: require('../dictionaries/ru.json') as Dictionary,
  tr: require('../dictionaries/tr.json') as Dictionary,
  ar: require('../dictionaries/ar.json') as Dictionary,
} as const;

export const getDictionary = async (locale: string): Promise<Dictionary> => {
  // Fallback to English if the locale doesn't exist
  const dict = dictionaries[locale as keyof typeof dictionaries] || dictionaries.en;
  return dict;
};