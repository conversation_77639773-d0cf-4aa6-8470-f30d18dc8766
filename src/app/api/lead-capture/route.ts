// Lead Capture API Route - Innovatio-Pro
import { NextRequest, NextResponse } from 'next/server';
import { createLeadCapture } from '@/lib/notion';

export async function POST(request: NextRequest) {
    try {
        const formData = await request.json();

        // Basic validation
        if (!formData.fullName || !formData.email || !formData.mvpGoal || !formData.timeline) {
            return NextResponse.json(
                {
                    success: false,
                    error: 'Name, email, goal, and timeline are required'
                },
                { status: 400 }
            );
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
            return NextResponse.json(
                {
                    success: false,
                    error: 'Please provide a valid email address'
                },
                { status: 400 }
            );
        }

        // Create lead capture entry in Notion
        const result = await createLeadCapture({
            fullName: formData.fullName,
            email: formData.email,
            company: formData.company,
            mvpGoal: formData.mvpGoal,
            timeline: formData.timeline,
            budget: formData.budget,
            additionalNotes: formData.additionalNotes,
        });

        if (result.success) {
            return NextResponse.json({
                success: true,
                message: 'Lead captured successfully! We will reach out soon.',
                data: {
                    leadId: result.leadId,
                }
            });
        } else {
            // Fallback: Log for manual processing
            const tempId = 'temp-lead-' + Date.now();
            console.log('📝 MANUAL PROCESSING REQUIRED - Lead Capture Form:', {
                tempId,
                timestamp: new Date().toISOString(),
                data: formData,
                note: 'Notion CRM unavailable - manual entry required'
            });

            return NextResponse.json({
                success: true,
                message: 'Thank you for your interest! We will contact you soon.',
                data: {
                    leadId: tempId,
                    note: 'Your submission has been received and will be processed manually.'
                }
            });
        }

    } catch (error) {
        console.error('❌ Lead capture error:', error);

        return NextResponse.json(
            {
                success: false,
                error: 'Failed to submit form. Please try again later.',
                details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
            },
            { status: 500 }
        );
    }
} 