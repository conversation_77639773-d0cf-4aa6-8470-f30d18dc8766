{"nav": {"home": "الرئيسية", "about": "من نحن", "solutions": "الحلول", "process": "العملية", "services": "الخدمات", "techStack": "التقنيات", "portfolio": "معرض الأعمال", "pricing": "الأسعار", "contact": "اتصل بنا", "usp": "لماذا نحن مختلفون", "aienhanced": "معزز بالذكاء الاصطناعي", "chatWithUs": "الدردشة معنا", "bookConsultation": "اح<PERSON><PERSON> استشارة", "apps": "تطبيقات", "clients": "العملاء", "testimonials": "آراء العملاء", "blog": "المدونة", "aipowereddevelopment": "التطوير المدعوم بالذكاء الاصطناعي", "kundenerfolgsgeschichten": "قصص نجاح العملاء", "developmentprocess": "عملية التطوير"}, "hero": {"title": "تطبيقات تحوّل", "subtitle": "عملك", "painPoint": {"mobile": {"line1": "يبدو مألوفاً؟ لا نتائج، الكثير من الاجتماعات؟", "line2": "نستمع، نحل، ننجز، نلهم!"}, "desktop": {"trigger": "يبدو مألوفاً؟", "problems": "فوضى • اجتماعات لا نهائية • نتائج قليلة • إرهاق", "solution": "سريع. موثوق. بسيط. خالي من الهموم.", "mainMessage": {"text": "نستمع، نحل، ننجز، نلهم!", "parts": ["نستمع", "نحل", "ننجز", "نلهم!"]}}}, "tagline": "تطوير Flutter متخصص • بنية جاهزة للذكاء الاصطناعي • Firebase Backend", "description": "بينما ينتظر المنافسون لشهور، نحن نسلم MVP يعمل. مُختبر في المعركة من 15+ شركة ناشئة احتاجت تطبيقاتها بالأمس، وليس الربع القادم.", "typing": {"sequence1": "Flutter MVP → متجر التطبيقات → آراء المستخدمين ✓", "sequence2": "تسليم سريع. جاهز للإنتاج. بلا دراما.", "sequence3": "من SaaS MVP إلى نجاح Series A."}, "cta": {"consultation": "جدولة استشارة استراتيجية", "consultationSubtext": "مكالمة أولية غير ملزمة • 30 دقيقة • مجانية"}, "metrics": {"development": {"label": "تطوير أسرع", "value": "40%", "description": "تطوير أسرع", "howTitle": "كيف:", "howExplanation": "استخدام أحدث بيئات التطوير المدعومة بالذكاء الاصطناعي، وتوليد التعليمات البرمجية التلقائي، وإكمال الكود الذكي."}, "timeToMarket": {"label": "وصول أسرع للسوق", "value": "50%", "description": "نشر أسرع", "howTitle": "كيف:", "howExplanation": "خط أنابيب CI/CD سريع، واختبار آلي، ومراجعة الكود بالذكاء الاصطناعي، وعملية نشر محسنة."}, "costSaving": {"label": "توفير التكاليف", "value": "30%", "description": "توفير التكاليف", "howTitle": "كيف:", "howExplanation": "استخدام Flutter للتطوير عبر المنصات، وموارد سحابية محسنة، وممارسات تطوير فعالة."}, "lessBugs": {"label": "أخطاء أقل", "value": "90%", "description": "أخطاء أقل", "howTitle": "كيف:", "howExplanation": "مراجعات الكود المدعومة بالذكاء الاصطناعي والاختبار الآلي وممارسات التطوير المُثبتة تقلل الأخطاء بشكل كبير."}}, "trustedBy": {"title": "موثوق من قبل رواد الصناعة"}, "trustVisual": {"satisfaction": "100% رضا العملاء", "appsLaunched": "15+ تطبيق تم إطلاقه بنجاح", "fromTo": "من العيادات الطبية إلى الأعمال الحرفية", "trust": "لا ضغط!", "additional": "+47 <PERSON><PERSON>ر", "industries": {"doctors": "الأطباء", "crafts": "الحرف", "startups": "الشركات الناشئة"}}, "rating": "التقييم", "responseTime": "ر<PERSON> 24س", "typeAnimation": ["Flutter MVP قابل للتطوير - قاعدة كود واحدة، منصتان 📱", 2000, "بنية جاهزة للذكاء الاصطناعي - مبنية لميزات المستقبل 🤖", 2000, "Firebase Backend - بنية تحتية موثوقة وقابلة للتطوير ⚡", 2000, "تطوير مركز على SaaS - نجاحكم هو أولويتنا 🚀", 2000, "", 500], "businessValue": {"headline": "التحول الرقمي بدون تعقيدات تقنية", "description": "تطوير برمجيات احترافي للشركات. من الاستشارة الاستراتيجية إلى التطبيقات المنتجة - كل شيء من مصدر واحد."}, "enterpriseFeatures": {"security": {"title": "أمان المؤسسات", "description": "أعلى معايير الأمان لبيانات شركتك"}, "architecture": {"title": "بنية قابلة للتطوير", "description": "حلول مستقبلية تنمو مع أعمالك"}, "roi": {"title": "محسّن للعائد على الاستثمار", "description": "تحسينات كفاءة قابلة للقياس ووفورات في التكاليف"}, "support": {"title": "دعم مؤسسي 24/7", "description": "دعم مخصص للعمليات التجارية الحرجة"}}, "companyTrust": {"title": "ثقة قادة السوق"}}, "painPointSection": {"familiarQuestion": "مألوف؟", "valueProposition": "وفر أكثر من €45,000 في تكاليف التطوير", "valueSubtitle": "إليك كيف يحقق نهجنا المُثبت نتائج استثنائية مع تقليل التكاليف والوقت بأكثر من النصف", "infiniteScrollPainPoints": ["اجتماعات لا نهائية بدون نتائج", "وكالات مكلفة بدون شفافية", "أشهر من التطوير، لا شيء للعرض", "مطورون مبتدئون في المشاريع الحرجة", "تكاليف مخفية ومفاجآت", "حلول تقنية معقدة بشكل مفرط", "لا توجد جداول زمنية أو معالم واضحة", "تواصل سيء مع المطورين", "قيود البائع وعدم المرونة", "فوضى في المشروع وإدارة سيئة", "نقص في الخبرة التقنية", "أوقات استجابة بطيئة للطلبات"], "mainTitle": "هذه المشاكل تكلفك أموالاً يومياً", "mainSubtitle": "هل تعاني أيضاً من مشاكل التطوير التقليدية هذه؟", "solutionsTitle": "كيف نحل المشاكل", "solutionsSubtitle": "حلول جاهزة تعمل فوراً", "costComparisonTitle": "تحليل شامل للتكلفة والقيمة", "traditionalLabel": "النهج التقليدي", "ourSolutionLabel": "حلنا", "savingsText": "إجمالي التوفير: €45,000 + تسليم أسرع بـ 6 أسابيع", "provenText": "مُثبت مع 15+ مشروع ناجح عبر الصناعات", "ctaText": "ابدأ في توفير المال اليوم", "comparisonHeaders": {"criteria": "معايير المقارنة", "traditional": "التطوير التقليدي", "ourSolution": "حلنا المُثبت"}, "comparisonLabels": {"cost": "تكلفة التطوير", "time": "الوقت حتى الإطلاق", "architecture": "جودة الهيكل", "architectureTraditional": "بناء من الصفر", "architectureOurs": "مُثبت ومُختبر", "security": "تن<PERSON>يذ الأمان", "securityTraditional": "يجب تطويره", "securityOurs": "جاهز للمؤسسات", "ai": "تكامل الذكاء الاصطناعي", "aiTraditional": "<PERSON>ير متوفر", "aiOurs": "نصوص جاهزة للاستخدام", "team": "خبرة الفريق", "teamTraditional": "فرق تعتمد على المبتدئين", "teamOurs": "متخصصون كبار", "maintenance": "الصيانة طويلة الأمد", "maintenanceTraditional": "تكاليف مستمرة عالية", "maintenanceOurs": "محسّن وفعال", "scalability": "قابلية التوسع", "scalabilityTraditional": "إعادة بناء مطلوبة", "scalabilityOurs": "مبني للتوسع", "risk": "مخاطر المشروع", "riskTraditional": "معدل فشل عالي", "riskOurs": "معدل نجاح مُثبت"}, "problems": {"missedOpportunities": {"title": "فرص سوقية ضائعة", "points": ["أشهر من التخطيط بدون نتائج", "المنافسون يترسخون بشكل أسرع", "الأفكار تتوقف في الاجتماعات", "فقدان حصة السوق"]}, "burningBudgets": {"title": "ميزانيات محترقة", "points": ["فرق داخلية محملة بالأعباء", "المشاريع تنفجر من ناحية التكلفة", "لا يوجد عائد استثمار قابل للقياس", "استخدام غير فعال للموارد"]}, "stagnatingScaling": {"title": "توسيع راكد", "points": ["المنصة لا تواكب النمو", "مشاكل في الأداء عند التوسع", "الديون التقنية تعوق التقدم", "لا توجد هندسة مستقبلية"]}}, "solutions": {"readyArchitecture": {"title": "هندسة مشروع جاهزة", "description": "قابلة للتوسع وجاهزة للإنتاج", "value": "توفير ~€15,000"}, "seniorExpertise": {"title": "خبرة كبيرة عند الطلب", "description": "إنتاجية فورية", "value": "توفير ~€8,000"}, "aiDevelopment": {"title": "تطوير مدعوم بالذكاء الاصطناعي", "description": "نصوص AI جاهزة لجميع المجالات", "value": "توفير ~€12,000"}, "enterpriseSecurity": {"title": "أمان المؤسسات", "description": "معايير أمان متكاملة", "value": "توفير ~€10,000"}}, "costComparison": {"traditional": {"amount": "~€65,000", "timeline": "10-12 <PERSON>سب<PERSON>ع"}, "ourSolution": {"amount": "~€20,000", "timeline": "4-6 أسابيع"}}}, "about": {"title": "ما يميزني", "subtitle": "المطور الذي يصنع الفرق", "motto": "\"You never lose. Either you win, or you learn.\"", "founderName": "مرحباً، أنا فيكتور", "founderRole": "مطور موبايل وويب متخصص في الحلول الحديثة والفعالة", "whyDifferentTitle": "ما يميزني عن الآخرين", "whyDifferentText": "بينما الآخرون ما زالوا يخططون، أنا أسلم بالفعل. معاييري في الجودة وشغفي بالحلول المتميزة جعلني Lead Developer في كل مكان — في لوفتهانزا ويونيون إنفستمنت والعديد من الشركات الأخرى.", "secretTitle": "سري؟", "secretText": "إلى جانب التميز التقني، أجلب الطاقة والوضوح اللذين يحييان الفرق ويدفعان المشاريع المعقدة للأمام بشكل مستدام. لهذا السبب أبقى في ذاكرة عملائي ليس فقط كمطور، بل كشريك موثوق.", "description": "عندما يعتمد مستقبل شركتك الناشئة على الوصول إلى السوق بسرعة، تحتاج إلى شريك تقني يفهم المخاطر. نحن لسنا مجرد مطورين—نحن مستشاروك التقنيون الاستراتيجيون.", "vision": "لماذا نحن مختلفون", "visionDesc": "بينما تعد الوكالات بكل شيء وتسلم متأخرة، نحن متخصصون حصرياً في تطوير Flutter MVP. وثق بنا 15+ مؤسس SaaS لدخول السوق—كل واحد أطلق في الوقت المحدد وحصل على التمويل.", "mission": "نهجنا الأول لـ SaaS", "missionDesc": "نحن نفهم إلحاح الشركات الناشئة. لهذا نسلم Flutter MVP جاهز للإنتاج في 4-6 أسابيع، وليس شهور. بنيتك مصممة للتوسع من اليوم الأول—عندما تجمع جولة A، لن تحتاج تطبيقك لإعادة بناء كامل.", "founderTitle": "فيكتور هيرمان - الشريك التقني", "founderDesc": "مدير تقني سابق لشركة ناشئة، أصبح الآن متخصص Flutter. كنت حيث أنت الآن—أسابق ضد الوقت، أحتاج MVP أمس. الآن أساعد المؤسسين الآخرين على تجنب الكوابيس التقنية التي نجوت منها.", "whyChooseUs": "لماذا يختارنا مؤسسو SaaS", "reasons": {"fastDelivery": {"title": "تسليم MVP بسرعة البرق", "description": "4-6 أسابيع من البداية إلى App Store. بينما المنافسون يناقشون الجداول الزمنية، مستخدموك يعطون تغذية راجعة بالفعل."}, "flutterSpecialization": {"title": "إتقان Flutter الخالص", "description": "لا نفعل كل شيء—نتقن شيئاً واحداً. 5+ سنوات حصرياً في تطبيقات Flutter يعني منحنى تعلم صفر على حسابك."}, "founderInvolved": {"title": "قيادة تقنية بمستوى المؤسس", "description": "وصول مباشر لاتخاذ القرارات التقنية العليا. لا مديري مشاريع، لا مطورين مبتدئين—فقط خبرة محنكة في مسارك الحرج."}, "scalableArchitecture": {"title": "جاهز للتوسع من اليوم الأول", "description": "بنية MVP تتعامل مع 10 مستخدمين أو 10 ملايين. عندما ينفجر النمو، تبقى الأساسات صلبة."}, "aiReady": {"title": "تكامل AI-أصلي", "description": "كل تطبيق نبنيه مُهيكل لميزات AI. أضف قدرات ذكية كلما طلبت خارطة طريقك ذلك."}, "strategicGuidance": {"title": "استشارة تقنية استراتيجية", "description": "أكثر من كود—تحصل على قرارات بنية، خرائط طريق تقنية، واستراتيجيات تقنية مُختبرة من الشركات الناشئة من بداية المشروع."}}, "skills": "المهارات", "projects": "المشاريع", "testimonials": "آراء العملاء", "experience": "سنوات الخبرة", "clients": "عميل سعيد", "transformBusiness": "تحويل الأعمال من خلال التكنولوجيا", "createSolutions": "إنشاء حلول رقمية مستقبلية", "stayingAhead": "البقاء في طليعة التكنولوجيا", "exceptionalUX": "تقديم تجارب استخدام استثنائية", "highPerformance": "بناء تطبيقات عالية الأداء", "solvingChallenges": "حل تحديات الأعمال الحقيقية بالتكنولوجيا", "flutterExpert": "خ<PERSON><PERSON><PERSON>", "webDevAdvanced": "متقدم", "aiIntegration": "دمج", "projectsCompleted": "المشاريع المكتملة", "personalDedication": "شخصي", "dedication": "التفاني", "qualityFocused": "مركز على الجودة", "personalService": "خدمة شخصية", "focusedApproach": "نهج مركز", "dedicatedService": "خدمة مخصصة", "clientReviews": "مراجعات العملاء", "focusedService": "خدمة مركزة", "longTerm": "طويل الأمد", "partnerships": "الشراكات", "privacyFocused": "مركز على الخصوصية", "secureServices": "تطوير آمن", "personalAttention": "اهتمام شخصي", "dedicatedDeveloper": "مطور مخصص", "securityFocused": "مركز على الأمان", "privacyRespected": "الخصوصية محترمة", "quality": "الجودة", "averageDelivery": "متوسط التسليم", "metrics": {"yearsExperience": "8+", "saasLaunched": "15+", "fundingRaised": "€2.5M+", "clientRating": "5.0/5", "avgDeliveryTime": "4-6 أسابيع", "flutterFocus": "100%", "zeroFailures": "0", "onTimeDelivery": "100%"}}, "aiPoweredDevelopment": {"title": "تطوير المستقبل: البرمجة المعززة بالذكاء الاصطناعي", "subtitle": "عندما تلتقي الخبرة العليا مع أحدث أدوات الذكاء الاصطناعي", "description": "نجمع بين أكثر من 8 سنوات من خبرة التطوير مع أحدث أدوات الذكاء الاصطناعي لتقديم نتائج استثنائية. كل سطر من الكود محسّن ومختبر وجاهز للإنتاج.", "benefits": {"speed": {"title": "تطوير أسرع بنسبة 40%", "description": "توليد الكود بمساعدة الذكاء الاصطناعي يسرّع البرمجة بشكل كبير", "icon": "Zap"}, "quality": {"title": "أخطاء أقل بنسبة 90%", "description": "مراجعات الكود بالذكاء الاصطناعي تكتشف الأخطاء قبل أن تصبح مشاكل", "icon": "Shield"}, "innovation": {"title": "جودة كود ممتازة", "description": "أفضل الممارسات والأنماط مطبقة تلقائياً", "icon": "<PERSON><PERSON><PERSON>"}}, "features": [{"title": "تكامل GitHub Copilot", "description": "إكمال وتوليد كود ذكي", "detail": "تنفيذ أسرع للميزات المعقدة"}, {"title": "مراجعات كود بالذكاء الاصطناعي", "description": "فحوصات جودة تلقائية مع كل commit", "detail": "جودة كود ثابتة مضمونة"}, {"title": "اختبار ذكي", "description": "حالات اختبار مولّدة بالذكاء الاصطناعي للسيناريوهات الحدية", "detail": "تغطية اختبار أعلى، مفاجآت أقل"}, {"title": "تحسين الأداء", "description": "الذكاء الاصطناعي يحلل ويحسن مسارات الكود الحرجة", "detail": "تطبيقات أسرع وأكثر كفاءة"}], "stats": {"codeQuality": {"value": "99.9%", "label": "نقاط جودة الكود"}, "timeReduction": {"value": "40%", "label": "تقليل الوقت"}, "bugPrevention": {"value": "85%", "label": "الأخطاء المنعة"}, "satisfaction": {"value": "100%", "label": "رضا العملاء"}}, "cta": {"title": "هل أنت مستعد للتطوير المعزز بالذكاء الاصطناعي؟", "description": "اختبر مستقبل تطوير البرمجيات اليوم", "primaryButton": "شا<PERSON><PERSON> كيف يعمل", "secondaryButton": "عرض دراسات الحالة"}}, "usp": {"badge": "نقاط قوتنا", "title": "لماذا Innovatio-Pro مختلف", "subtitle": "لا اجتماعات. لا مصطلحات تقنية. فقط تطبيقك.", "description": "بينما تربك الوكالات الأخرى بالمصطلحات التقنية، نحن نتحدث بلغتك ونحقق النتائج.", "features": [{"title": "استشارة شخصية للمبتدئين التقنيين المطلقين", "description": "نشرح كل شيء بمصطلحات بسيطة. لا مصطلحات معقدة، لا التباس.", "icon": "👥"}, {"title": "تطبيقات لكل صناعة وميزانية", "description": "سواء كنت طبيب أو حرفي أو شركة ناشئة - نطور الحل المثالي.", "icon": "🏗️"}, {"title": "كل شيء من مصدر واحد: من التصميم إلى الإطلاق", "description": "شخص اتصال واحد لكل شيء. من الفكرة الأولى إلى متجر التطبيقات.", "icon": "🎯"}, {"title": "لا تكاليف مخفية، لا مفاجآت", "description": "أسعار شفافة من البداية. ما نقوله، نفعله.", "icon": "💰"}, {"title": "أشخاص اتصال ثابتون، تواصل واضح", "description": "لديك دائماً نفس شخص الاتصال وتعرف بالضبط ما يحدث.", "icon": "📞"}, {"title": "تسليم سريع بدون فقدان الجودة", "description": "بفضل العمليات المُثبتة والأدوات الحديثة، نسلم بسرعة وموثوقية.", "icon": "⚡"}, {"title": "تكنولوجيا جاهزة للمستقبل", "description": "نستخدم حلول حديثة وقابلة للتوسع تنمو مع نجاحك.", "icon": "🚀"}, {"title": "100% تركيز على نجاحك", "description": "نحن راضون فقط عندما تكون راضياً. نجاحك هو نجاحنا.", "icon": "🏆"}], "cta": {"primary": "احصل على استشارة مجانية الآن", "secondary": "احجز مكالمة أولية غير ملزمة"}, "trustIndicators": {"freeConsultation": "استشارة مجانية", "nonBinding": "غير ملزمة", "thirtyMinutes": "30 دقيقة"}}, "ourProcess": {"title": "عملية التطوير المُثبتة لدينا", "subtitle": "من المفهوم إلى تطبيق Flutter مثالي في أسابيع قليلة فقط", "description": "نهج منهجي يقدم تطبيقات Flutter استثنائية في الوقت المحدد. عمليتنا تضمن الجودة والشفافية والنتائج القابلة للقياس.", "badge": "عمليتنا المُثبتة", "deliverables": "المخرجات", "cta": {"title": "هل أنت مستعد لمشروعك؟", "description": "دعنا نناقش متطلباتك وننشئ خطة تطوير مخصصة.", "primaryButton": "ابدأ المشروع", "secondaryButton": "قصص النجاح"}, "steps": {"planning": {"title": "رغبتكم", "description": "نبدأ بفهم رؤيتك وأهدافك التجارية ومتطلباتك التقنية. تتضمن هذه المرحلة بحث السوق وتحليل المستخدمين وإنشاء خارطة طريق مفصلة.", "duration": "3-5 <PERSON><PERSON><PERSON><PERSON>", "deliverables": ["متطلبات المشروع والنطاق", "خطة المعمارية التقنية", "الجدول الزمني وخارطة طريق المعالم", "تقييم المخاطر والاستراتيجية"], "features": ["تحليل المتطلبات وتحديد نطاق المشروع", "الهيكل التقني واختيار التكنولوجيا", "تخطيط الجدول الزمني مع خارطة طريق المعالم"]}, "design": {"title": "نحن نطور", "description": "ننشئ تصاميم UI/UX مذهلة تتماشى مع علامتك التجارية. نبني نماذج أولية تفاعلية للتحقق من المفاهيم قبل بدء التطوير.", "duration": "أسبوع واحد", "deliverables": ["نظام تصميم UI/UX كامل", "نماذج أولية تفاعلية قابلة للنقر", "تكامل إرشادات العلامة التجارية", "مواصفات التصميم المتجاوب"], "features": ["نظام تصميم UI/UX مع تكامل العلامة التجارية", "نماذج أولية تفاعلية للتحقق", "تصميم متجاوب لجميع الأجهزة"]}, "development": {"title": "نحن نسلم", "description": "تطوير Flutter سريع بكود نظيف وقابل للتوسع. الاختبار المستمر يضمن الأداء المثالي عبر جميع الأجهزة والمنصات.", "duration": "2-4 أسابيع", "deliverables": ["تطبيق Flutter جاهز للإنتاج", "تكامل خلفية كامل", "مجموعة اختبار شاملة", "تحسين الأداء"], "features": ["كود Flutter نظيف لـ iOS و Android", "تكامل الخلفية وتطوير API", "اختبار شامل وتحسين"]}, "launch": {"title": "الإطلاق والدعم", "description": "نشر سلس في متاجر التطبيقات مع دعم مستمر. نضمن إطلاق تطبيقك بنجاح واستمراره في الأداء المثالي.", "duration": "مستمر", "deliverables": ["نشر في متجر التطبيقات", "استراتيجية الإطلاق والتسويق", "إعداد مراقبة الأداء", "الدعم والتحديثات المستمرة"], "features": ["إرسال متجر التطبيقات والنشر", "استراتيجية الإطلاق ومراقبة الأداء", "الصيانة المستمرة وتحديثات الميزات"]}}}, "advantages": {"title": "حلولنا", "subtitle": "كيف نساعدك في بناء تطبيقات جوال ناجحة", "speed": "تطوير سريع", "speedDesc": "نقدم حلولاً بسرعة دون المساومة على الجودة", "stability": "تطبيقات موثوقة", "stabilityDesc": "تطبيقاتنا مبنية للاستقرار والأداء", "cost": "كفاءة التكلفة", "costDesc": "عملية تطوير محسنة توفر وقتك ومالك", "timeToMarket": "وقت أسرع للوصول إلى السوق", "timeToMarketDesc": "أطلق منتجك بسرعة وتقدم على المنافسة", "aiIntegration": "دمج الذكاء الاصطناعي", "aiIntegrationDesc": "تعزيز عملك بقدرات ذكاء اصطناعي قوية", "development": "تطوير متكامل", "developmentTime": "6-12 أسب<PERSON>ع، يختلف حسب تعقيد المشروع", "developmentDesc": "تطوير تطبيق جوال كامل من المفهوم إلى النشر، مع تكامل كامل للواجهة الخلفية وميزات متقدمة.", "mvp": "تطوير MVP", "mvpTime": "4-8 أسا<PERSON><PERSON><PERSON>، يختلف حسب تعقيد المشروع", "mvpDesc": "إطلاق سريع بمنتج قابل للتطبيق بالحد الأدنى يتضمن وظائف أساسية للتحقق من مفهومك وجذب المستخدمين الأوائل أو المستثمرين.", "prototype": "بناء نماذج أولية سريعة", "prototypeTime": "1-2 أس<PERSON><PERSON><PERSON>، يختلف حسب تعقيد المشروع", "prototypeDesc": "اختبار المفاهيم بسرعة باستخدام نماذج أولية تفاعلية قبل الالتزام بالتطوير الكامل، مما يوفر الوقت والموارد.", "qa": "<PERSON><PERSON><PERSON> الجودة", "qaTime": "مستمر، يتناسب مع تعقيد المشروع", "qaDesc": "اختبار شامل عبر الأجهزة والمنصات لضمان أداء تطبيقك بشكل مثالي مع بروتوكولات اختبار آلية ويدوية.", "consulting": "استشارات تقنية", "consultingTime": "حسب الحاجة، يعتمد على تعقيد المشروع", "consultingDesc": "نصائح خبراء حول مجموعة التكنولوجيا وقرارات الهندسة واستراتيجيات التنفيذ لتحسين تطبيق الجوال الخاص بك.", "uiux": "تصميم واجهة المستخدم/تجربة المستخدم", "uiuxTime": "2-3 أ<PERSON><PERSON><PERSON><PERSON><PERSON>، يختلف حسب تعقيد المشروع", "uiuxDesc": "تصميم يركز على المستخدم يوازن بين الجماليات والوظائف، مما يخلق تجارب جوال بديهية وجذابة.", "maintenance": "الصيانة والدعم", "maintenanceTime": "مستمر، يتناسب مع تعقيد المشروع", "maintenanceDesc": "دعم طويل الأمد مع تحديثات منتظمة وتحسين الأداء وتعزيزات الميزات للحفاظ على تنافسية تطبيقك.", "analytics": "دم<PERSON> التحليلات", "analyticsTime": "1-2 أس<PERSON><PERSON><PERSON>، يختلف حسب تعقيد المشروع", "analyticsDesc": "تنفيذ تتبع البيانات للحصول على رؤى قابلة للتنفيذ حول سلوك المستخدم، مما يمكّن من اتخاذ قرارات قائمة على البيانات لتطبيقك.", "training": "تدريب الفريق", "trainingTime": "1-2 أس<PERSON><PERSON><PERSON>، يختلف حسب تعقيد المشروع", "trainingDesc": "تدريب شامل لفريقك على صيانة وتوسيع تطبيقك بعد التسليم.", "developmentEfficiency": "كفاءة التطوير", "timeToMarketReduction": "تقليل وقت الوصول إلى السوق", "conceptValidation": "تحقق من المفهوم", "bugFreeRate": "معدل خالٍ من الأخطاء", "technicalImprovement": "تحسين تقني", "userSatisfaction": "رضا المستخدم", "appUptime": "وقت تشغيل التطبيق", "dataAccuracy": "دقة البيانات", "knowledgeRetention": "الاحتفاظ بالمعرفة", "developmentInfo": {"title": "أوقات التطوير", "simpleApp": {"title": "تطبيق بسيط", "examples": "أمثلة: قوائم المهام، الحاسبة، تطبيقات معلومات بسيطة بدون خادم خلفي.", "features": ["شاشا<PERSON> قليلة (3-5)", "لا يوجد تكامل مع الخادم الخلفي أو الحد الأدنى منه", "مكونات واجهة مستخدم قياسية", "لا توجد رسوم متحركة أو وظائف معقدة"], "timeline": {"total": "وقت التطوير: 4-8 أسابيع", "frontend": "واجهة المستخدم: 2-4 أسابيع", "backend": "الخادم الخلفي (إذا لزم الأمر): 1-2 أسبوع", "testing": "الاختبار والنشر: 1-2 أسبوع"}}, "mediumApp": {"title": "تطبيق متوسط", "examples": "أمثلة: تطبيقات التجارة الإلكترونية، تطبيقات التواصل الاجتماعي مع الميزات الأساسية، تطبيقات مع تسجيل المستخدمين وتكامل قاعدة البيانات.", "features": ["6-15 شا<PERSON>ة", "تكامل مع الخادم الخلفي (مثل واجهات برمجة REST أو GraphQL)", "تسجيل المستخدمين والمصادقة", "قاعدة بيانات لمستخدمي وبيانات التطبيق", "بعض الرسوم المتحركة والعناصر التفاعلية", "إشعا<PERSON><PERSON><PERSON>ush"], "timeline": {"total": "وقت التطوير: 8-16 أسبوع", "frontend": "واجهة المستخدم: 4-6 أسابيع", "backend": "الخادم الخلفي: 3-5 أسابيع", "testing": "الاختبار والنشر: 2-3 أسابيع"}}, "complexApp": {"title": "تطبيق معقد", "examples": "أمثلة: تطبيقات مثل Uber، Instagram، أو تطبيقات البنوك مع ميزات متقدمة.", "features": ["15+ شا<PERSON>ة", "واجهة مستخدم تفاعلية للغاية", "ميزات في الوقت الفعلي (مثل التتبع المباشر، الدردشة)", "تكامل مع واجهات برمجة طرف ثالث (مثل بوابات الدفع، واجهات برمجة البطاقات)", "<PERSON>اد<PERSON> خلفي قابل للتطوير مع تكامل سحابي", "ميزات أمنية (مثل التشفير، المصادقة الثنائية)", "وظائف غير متصلة"], "timeline": {"total": "وقت التطوير: 16-32 أسبوع أو أطول", "frontend": "واجهة المستخدم: 6-10 أسابيع", "backend": "الخادم الخلفي: 6-12 أسبوع", "testing": "الاختبار والنشر: 4-6 أسابيع"}}, "factors": {"title": "العوامل المؤثرة على وقت التطوير", "teamSize": "حجم الفريق: يمكن لفريق أكبر (مثل مطورين منفصلين للواجهة الأمامية والخلفية وضمان الجودة) تسريع التطوير. يحتاج المطور الواحد إلى مزيد من الوقت.", "technology": "التكنولوجيا: يستغرق التطوير الأصلي (مثل Swift لنظام iOS، Kotlin لنظام Android) عادةً وقتاً أطول من الأساليب متعددة المنصات مثل Flutter. Flutter كأحدث التقنيات يمكن أن يقلل وقت التطوير بنسبة 40-60%.", "requirements": "المتطلبات والتغييرات: يمكن للتغييرات المتكررة أو المتطلبات غير الواضحة إطالة وقت التطوير.", "testing": "الاختبار وتصحيح الأخطاء: تتطلب التطبيقات المعقدة مزيداً من الوقت للاختبار، خاصة على منصات متعددة (iOS وAndroid).", "design": "التصميم: تتطلب التصاميم البسيطة وقتاً أقل، بينما تزيد التصاميم المخصصة المتحركة من وقت التطوير."}, "summary": "ملخص: تطبيق بسيط: 4-8 أسابيع. تطبيق متوسط: 8-16 أسبوع. تطبيق معقد: 16-32 أسبوع أو أطول.", "aiComparison": "مع تطويرنا المدعوم بالذكاء الاصطناعي وFlutter، يمكننا تقليل هذه الأوقات بنسبة 40-60% مع الحفاظ على جودة وأداء عاليين."}}, "services": {"badge": "خدماتنا", "title": "الابتكار يلتقي بالخبرة", "subtitle": "حلول رقمية تحوّل عملك", "description": "من الاستشارة الاستراتيجية إلى الدعم طويل الأمد - نقدم جميع الخدمات من مصدر واحد لنجاحكم الرقمي.", "cta": {"button": "تواصل معنا", "description": "استشارة مجانية • غير ملزمة • 30 دقيقة"}, "services": [{"title": "الاستراتيجية والمفهوم", "description": "تحليل أهدافكم التجارية وتطوير استراتيجية رقمية مخصصة.", "icon": "🎯"}, {"title": "تصميم واجهة المستخدم وتجربة المستخدم", "description": "تصميم واجهات بديهية ومركزة على المستخدم.", "icon": "🎨"}, {"title": "تطوير التطبيقات (iOS و Android)", "description": "التطوير الأصلي والهجين للحصول على أداء مثالي على جميع الأجهزة.", "icon": "📱"}, {"title": "تطوير الخادم الخلفي وواجهة برمجة التطبيقات", "description": "بناء هياكل خادم قوية وقابلة للتوسع للتطبيقات المعقدة.", "icon": "⚙️"}, {"title": "ضمان الجودة والاختبار", "description": "اختبارات يدوية وآلية لضمان التشغيل الخالي من الأخطاء والآمن.", "icon": "🛡️"}, {"title": "الصيانة والتطوير المستمر", "description": "الدعم طويل الأمد وتوسيع نطاق تطبيقكم بعد الإطلاق.", "icon": "🔧"}]}, "serviceSection": {"title": "خدماتنا", "subtitle": "حلول رقمية مخصصة لعملك", "description": "نقدم مجموعة شاملة من الخدمات الرقمية لمساعدة الشركات على الازدهار في المشهد التنافسي اليوم. تمتد خبرتنا عبر مجالات متعددة لتقديم حلول مبتكرة وفعالة.", "viewAll": "ناقش مشروعك", "comparisonTitle": "لماذا تختارنا؟", "comparisonSubtitle": "انظر كيف نقارن بأساليب التطوير التقليدية", "timeComparison": {"title": "وقت التطوير", "traditional": "16 أسبوع", "withUs": "10 أسابيع", "savings": "40%"}, "costComparison": {"title": "تكلفة المشروع", "traditional": "€50,000", "withUs": "€25,000", "savings": "50%"}, "qualityComparison": {"title": "الأداء", "traditional": "قياسي", "withUs": "محسّن", "savings": "أسرع بنسبة 35%"}, "mvp": {"title": "تطوير المنتج الأدنى القابل للتطبيق", "description": "أطلق بسرعة مع منتج أدنى قابل للتطبيق يتميز بالوظائف الأساسية للتحقق من مفهومك وجذب المستخدمين الأوائل.", "timeframe": "3-4 أسابيع", "benefits": ["دخول أسرع للسوق مع الميزات الأساسية", "نهج فعال من حيث التكلفة للتحقق من أفكار الأعمال", "تطوير تكراري بناءً على ملاحظات المستخدمين الحقيقية"], "features": ["تنفيذ الوظائف الأساسية", "تصميم واجهة مستخدم أساسية", "مصادقة المستخدم", "حل تخزين البيانات", "نشر على منصة واحدة"]}, "prototype": {"title": "نموذج أولي سريع", "description": "اختبر المفاهيم بسرعة باستخدام نماذج أولية تفاعلية قبل الالتزام بالتطوير الكامل، مما يوفر الوقت والموارد.", "timeframe": "1-2 أسبوع", "benefits": ["التحقق من الأفكار بأقل استثمار", "جمع ملاحظات المستخدمين في وقت مبكر من العملية", "تحسين المفا<PERSON><PERSON><PERSON> قبل التطوير الكامل"], "features": ["نماذج واجهة مستخدم تفاعلية", "وظائف أساسية", "تنفيذ تدفق المستخدم", "عرض للمساهمين"]}, "fullstack": {"title": "تطوير تطبيقات الجوال الشامل", "description": "تطوير تطبيقات الجوال الكامل من المفهوم إلى النشر مع حلول متكاملة للواجهة الأمامية والخلفية.", "timeframe": "4-12 أسبو<PERSON>", "benefits": ["خبرة تطوير من البداية إلى النهاية", "تكامل سلس بين جميع مكونات النظام", "اختبار شامل وضمان الجودة"], "features": ["تطوير الواجهة الأمامية والخلفية", "تكامل API", "إدارة قواعد البيانات", "مصادقة وتفويض المستخدم", "اختبار وتحسين الأداء"]}, "homepage": {"title": "الصفحات الرئيسية وصفحات الهبوط", "description": "مواقع ويب وصفحات هبوط احترافية تعرض عملك وتحول الزوار إلى عملاء.", "timeframe": "2-3 أسابيع", "benefits": ["تصميم محسن للتحويل", "بنية متوافقة مع محركات البحث", "تخطيطات متجاوبة للجوال"]}, "landingpage": {"title": "صفحات هبوط سهلة الوصول", "description": "صفحات هبوط متوافقة مع معايير WCAG 2.0 باستخدام أحدث تقنيات الويب لأقصى قدر من إمكانية الوصول والأداء.", "timeframe": "2-4 أسابيع", "benefits": ["توافق مع معايير WCAG 2.0 AA", "تصميم شامل لجميع المستخدمين", "توافق مع قارئات الشاشة", "مقاييس أداء عالية"]}, "qa": {"title": "<PERSON><PERSON><PERSON> الجودة", "description": "اختبار شامل عبر الأجهزة والمنصات المختلفة لضمان أداء تطبيقك بشكل مثالي مع الاختبارات الآلية واليدوية.", "timeframe": "مستمر", "benefits": ["تجربة مستخدم خالية من الأخطاء", "تحسين الأداء", "توافق عبر المنصات المختلفة"]}, "consulting": {"title": "استشارات تقنية", "description": "نصائح خبيرة حول استراتيجية التكنولوجيا والتنفيذ لمساعدتك على اتخاذ قرارات مستنيرة لمشاريعك الرقمية.", "timeframe": "<PERSON><PERSON><PERSON> الحاجة", "benefits": ["توصيات مجموعة التكنولوجيا", "تخطيط ومراجعة الهندسة", "تحسين الأداء والأمان"], "features": ["مراجعات الشفرة", "تحليل الأداء", "تقييم الأمان", "استشارات الهندسة", "تخطيط قابلية التوسع"]}, "architecture": {"title": "هندسة المشروع", "description": "أساس متين لنجاح مشروعك مع هندسة نظام مصممة جيدًا ومواصفات تقنية.", "timeframe": "1-2 أسبوع", "benefits": ["تصميم نظام قابل للتوسع", "اختيارات تكنولوجية مستقبلية", "خارطة طريق تطوير واضحة"], "features": ["المواصفات الفنية", "تصميم هندسة النظام", "مخطط قاعدة البيانات", "توثيق واجهة برمجة التطبيقات", "خارطة طريق التطوير"]}, "aiSolutions": {"title": "دمج الذكاء الاصطناعي", "description": "دمج قدرات الذكاء الاصطناعي لتعزيز تطبيقات أعمالك بميزات ذكية وأتمتة.", "timeframe": "2-4 أسابيع", "benefits": ["تجارب مستخدم مخصصة", "سير عمل وعمليات مؤتمتة", "رؤى وتنبؤات قائمة على البيانات"], "features": ["دمج نماذج الذكاء الاصطناعي", "معالجة اللغة الطبيعية", "أنظمة التوصية", "تحليل البيانات", "الرؤية الحاسوبية"]}}, "prices": {"title": "أسعارنا", "subtitle": "تسعير شفاف لكل مرحلة", "description": "اختر من باقاتنا المحددة مسبقًا أو خصصها لتناسب متطلبات مشروعك. اجمع الخدمات للحصول على خصم 15%.", "caseStudyTitle": "دراسة حالة: أسرع بنسبة 40% في الوصول إلى السوق", "caseStudyDescription": "أطلق عميلنا من شركة التكنولوجيا المالية في وايومنغ منتجه الأولي بسرعة أكبر بنسبة 40% من متوسط الصناعة، مما سمح له بتأمين تمويل إضافي وتسريع النمو.", "promotionTitle": "اجمع الخدمات ووفر 15%", "promotionDescription": "اجمع بين أي خدمتين أو أكثر واحصل على خصم 15% على إجمالي تكلفة مشروعك.", "leadCaptureTitle": "ابد<PERSON> رحلة Flutter MVP الخاصة بك", "leadCaptureDescription": "احجز مكالمة استكشاف مجانية لاستكشاف كيف يمكننا تحقيق رؤية تطبيقك.", "discussButton": "ناقش مشروعك", "contactButton": "اتصل بنا", "pricingDisclaimer": "* الأسعار قد تختلف بناءً على متطلبات المشروع والخدمات الإضافية. اتصل بنا للحصول على عرض أسعار مخصص لاحتياجاتك الخاصة.", "priceVariesInfo": "قد يختلف السعر بناءً على تعقيد المشروع والمتطلبات الإضافية وقيود الوقت. اتصل بنا للحصول على عرض أسعار مفصل.", "fullDetails": "التفاصيل الكاملة", "allIncludedFeatures": "جميع المزايا المدرجة", "backendOptions": "خيارات الباك إند", "showDetails": "إظهار التفاصيل", "valueProps": {"aiDevelopment": {"title": "التطوير المعزز بالذكاء الاصطناعي", "description": "تطوير أسرع بنسبة 70%"}, "backend": {"title": "Firebase و Supabase", "description": "حلول باك إند حديثة"}, "quality": {"title": "جودة المؤسسات", "description": "هندسة نظيفة"}}, "leadCapture": {"headline": "ابد<PERSON> رحلة Flutter MVP الخاصة بك", "subheadline": "احجز مكالمة الاستكشاف المجانية", "introduction": "لا تحتاج إلى معرفة جميع التفاصيل التقنية بعد. سيرشدك مؤسسونا خلال كل خطوة—من التحقق من المفهوم إلى إطلاق App Store. لنبدأ بمحادثة مريحة حول رؤيتك.", "trustStatement": "يبدأ كل مشروع بجلسة استراتيجية مجانية حيث نستكشف أهدافك والجدول الزمني وكيف يمكن لـ Flutter تسريع طريقك إلى السوق.", "form": {"fullName": {"label": "الاسم الكامل", "placeholder": "اسمك الكامل", "required": true}, "email": {"label": "عنوان البريد الإلكتروني", "placeholder": "بريدك.الإلكتروني@الشركة.com", "required": true}, "company": {"label": "اسم الشركة", "placeholder": "اسم شركتك أو الشركة الناشئة", "required": false, "optional": "(اختياري)"}, "mvpGoal": {"label": "ما هو هدفك الأساسي؟", "placeholder": "اختر هدفك الرئيسي", "required": true, "options": {"prototyping": "إنشاء نموذج أولي للتحقق من فكرتي", "mvpLaunch": "بناء وإطلاق MVP الخاص بي", "saasScaling": "توسيع منتج SaaS الحالي الخاص بي", "aiIntegration": "إضافة ميزات الذكاء الاصطناعي لتطبيقي", "consulting": "الحصول على إرشادات تقنية واستراتيجية", "notSure": "لست متأكدًا بعد - أح<PERSON>اج للإرشاد"}}, "timeline": {"label": "متى تود أن تبدأ؟", "placeholder": "اختر الجدول الزمني المفضل لديك", "required": true, "options": {"asap": "في أسرع وقت ممكن", "fourToSix": "خلال 4-6 أسابيع", "threeMonths": "خ<PERSON>ال 2-3 <PERSON><PERSON><PERSON><PERSON>", "undecided": "الجدول الزمني مرن"}}, "budget": {"label": "ما هو نطاق ميزانيتك المقدرة؟", "placeholder": "اختر نطاق ميزانيتك", "required": false, "options": {"below10k": "أقل من €10,000", "10to20k": "€10,000 - €20,000", "20to50k": "€20,000 - €50,000", "above50k": "€50,000+", "notSure": "لست متأكدًا بعد"}}, "additionalNotes": {"label": "أخبرنا عن مشروعك", "placeholder": "اوصف بإيجاز فكرة تطبيقك أو المستخدمين المستهدفين أو أي متطلبات محددة...", "required": false, "optional": "(اختياري)"}}, "ctaButton": "احجز مكالمة الاستكشاف المجانية", "disclaimer": "استشارة مجانية 100% • لا توجد التزامات • عادة 15-30 دقيقة", "alternativeContact": {"text": "تفضل الدردشة مباشرة؟", "whatsappText": "راسلنا على WhatsApp", "emailText": "أرسل بريد إلكتروني"}, "successMessage": {"title": "شكرًا لك على اهتمامك!", "subtitle": "لقد تلقينا معلوماتك", "description": "سيراجع فريقنا تفاصيل مشروعك وسيتواصل معك خلال 24 ساعة لجدولة مكالمة الاستكشاف المجانية. في غضون ذلك، لا تتردد في استكشاف دراسات الحالة وشهادات العملاء.", "nextSteps": {"title": "ما سيحدث بعد ذلك:", "steps": ["سنراجع تفاصيل مشروعك ومتطلباتك", "سيتواصل معك مؤسسنا شخصيًا خلال 24 ساعة", "سنجدول مكالمة استكشاف لمدة 15-30 دقيقة في الوقت المناسب لك", "خلال المكالمة، سنستكشف رؤيتك ونقدم إرشادات استراتيجية"]}, "backToSite": "العودة إلى الصفحة الرئيسية", "viewCaseStudies": "عرض دراسات الحالة"}}, "packages": {"prototype": {"title": "نموذج أولي", "timeframe": "2-4 أسابيع", "price": "€4,500", "badge": "التحقق", "description": "نموذج أولي قابل للنقر بدون منطق خلفي للاختبارات الأولية للمستخدمين وعروض المستثمرين", "cta": "ابد<PERSON> النموذج الأولي", "keyFeatures": ["ورش عمل تصميم UI/UX", "تصاميم مولدة بالذكاء الاصطناعي", "نماذج قابلة للنقر", "نموذج أولي جاهز للمستثمرين"], "detailedFeatures": ["نماذج أولية تفاعلية قابلة للنقر", "ورش عمل تصميم UX/UI بدعم الذكاء الاصطناعي", "تصاميم كاملة لجميع الشاشات", "جلسات تحديد أولويات الميزات", "<PERSON><PERSON><PERSON><PERSON> خارطة طريق MVP", "مواد عرض المستثمرين", "تصميم متجاوب لجميع الأجهزة", "بدون خلفية - نموذج أولي أمامي فقط", "تكامل التعليقات بعد الاختبار", "تقييم الجدوى التقنية"]}, "mvp": {"title": "MVP البداية", "timeframe": "6-8 أسا<PERSON><PERSON>ع", "price": "€8,500", "badge": "الخيار الأكثر شعبية", "description": "تطبيق كامل بالميزات الأساسية للإطلاق الأول", "cta": "<PERSON>بدأ MVP", "keyFeatures": ["مصادقة المستخدم", "5+ شاشات أساسية", "خلفية Firebase/Supabase", "تطوير بمساعدة الذكاء الاصطناعي"], "detailedFeatures": ["تطوير تطبيق Flutter كامل", "إعداد خلفية Firebase أو Supabase", "مصادقة وتسجيل المستخدمين", "5+ شاشات تطبيق أساسية", "تطوير بمساعدة الذكاء الاصطناعي", "هندسة كود نظيف", "تصميم متجاوب", "إشعارات دفع أساسية", "دعم نشر App Store", "30 يوم دعم مميز", "تكامل قاعدة بيانات أساسي", "تحسين الأداء"]}, "professional": {"title": "التطبيق المهني", "timeframe": "8-12 أسابيع", "price": "ابتداءً من €15,000", "badge": "مهني", "description": "تطبيق متقدم مع تكامل المدفوعات والهندسة المهنية", "cta": "احصل على عرض", "keyFeatures": ["10+ شاشات", "تكامل المدفوعات", "إشعارات الدفع", "هندسة قابلة للتوسع"], "detailedFeatures": ["تطوير تطبيق Flutter كامل", "خلفية Firebase/Supabase بميزات متقدمة", "تكامل المدفوعات (Stripe، PayPal، إلخ)", "إشعارات دفع متقدمة", "10+ شاشات تطبيق", "هندسة سحابية قابلة للتوسع", "مراقبة الأداء", "إدارة مستخدمين متقدمة", "تكامل تحليل البيانات", "تحسين App Store", "لوحة إدارة (عن<PERSON> الطلب)", "ميزات الذكاء الاصطناعي (حسب الحاجة)", "مجموعة اختبار شاملة", "60 يوم دعم مميز"]}, "enterprise": {"title": "الحل المؤسسي", "timeframe": "12+ أسابيع", "price": "<PERSON>ن<PERSON> الطلب", "badge": "مؤسسي", "description": "حل مخصص بالكامل للشركات ذات المتطلبات الخاصة", "cta": "ابد<PERSON> الاستشارة", "keyFeatures": ["<PERSON><PERSON> مخصص", "ميزات غير محدودة", "هندسة مؤسسية", "دعم 24/7 متاح"], "detailedFeatures": ["تطوير مخصص بالكامل", "هندسة خلفية على مستوى المؤسسات", "لوحة إدارة ولوحة تحكم مخصصة", "تكامل ذكاء اصطناعي فردي", "نشر متعدد المنصات", "ميزات أمان متقدمة", "بنية تحتية سحابية قابلة للتوسع", "مراقبة الأداء والتحليلات", "تطوير API مخصص", "تكاملات طرف ثالث", "عمليات تدقيق الامتثال والأمان", "دعم 24/7 متاح", "فريق تطوير مخصص", "تحديثات وصيانة مستمرة"]}, "landingpage": {"title": "تطوير صفحات الهبوط", "timeframe": "2-4 أسابيع", "description": "صفحات هبوط متوافقة مع معايير WCAG 2.0 باستخدام أحدث تقنيات الويب", "features": ["توافق مع معايير WCAG 2.0 AA", "تصميم شامل لجميع المستخدمين", "توافق مع قارئات الشاشة", "مقاييس أداء عالية", "هيكل محسن لمحركات البحث", "تصميم متجاوب"]}, "architecture": {"title": "هندسة المشروع", "timeframe": "1-2 أسابيع", "description": "أساس متين لنجاح مشروعك", "features": ["مواصفات تقنية", "تصميم هندسة النظام", "مخطط قاعدة البيانات", "توثيق واجهة برمجة التطبيقات", "خارطة طريق التطوير"]}, "consulting": {"title": "الاستشارات التقنية", "timeframe": "شراكة مستمرة", "benefitSentence": "👨‍💻 استراتيجية مستوى المدير التقني بتكلفة أقل بنسبة 80%", "coolFactor": "👨‍💻 خبرة مستوى المدير التقني + الإرشاد + القيادة التقنية", "description": "استراتيجية والقيادة التقنية على مستوى المدير التقني بجزء من التكلفة", "price": "ابتداءً من €150/ساعة", "badge": "خبرة مستوى المدير التقني", "cta": "ابد<PERSON> الاستشارة", "benefits": ["استراتيجية مستوى المدير التقني بتكلفة أقل بنسبة 80%", "مراجعات تقنية تمنع إعادة الكتابة المكلفة لاحقاً", "صنع قرارات خبيرة مباشرة", "تصميم وتحسين الهندسة المعمارية", "إرشاد الفريق والتوجيه", "العناية التقنية الواجبة", "تخطيط قابلية التوسع", "تطبيق أفضل الممارسات"], "trustLine": "خبرة مستوى المدير التقني • متوفر بالساعة • الإرشاد مدرج", "features": ["استراتيجية مستوى المدير التقني بتكلفة أقل بنسبة 80%", "خرائط طريق التكنولوجيا والمراجعات", "صنع قرارات خبيرة مباشرة", "تصميم وتحسين الهندسة المعمارية", "إرشاد الفريق والتوجيه", "العناية التقنية الواجبة", "تخطيط قابلية التوسع", "تطبيق أفضل الممارسات"]}, "saasGrowth": {"title": "الحل المؤسسي", "timeframe": "12+ أسابيع*", "price": "ابتداءً من €15,000", "badge": "مؤسسي", "description": "حل مؤسسي مخصص بالكامل مع جميع الميزات المطلوبة للمتطلبات الخاصة. تكامل الذكاء الاصطناعي المخصص والهندسة القابلة للتوسع والدعم الشامل مضمن.", "cta": "ابد<PERSON> الاستشارة", "features": ["توسيع وتحسين تطبيق Flutter", "تحسين الخلفية (Firebase/Supabase)", "تكامل لوحة الإدارة", "بنية تحتية سحابية بمستوى SaaS", "استشارة كاملة لهندسة SaaS", "إعادة هيكلة وتحسين الكود النظيف", "دائماً على تواصل لقرارات التوسع", "مراقبة وتحسين الأداء"]}, "custom": {"title": "ط<PERSON><PERSON> مخصص", "timeframe": "حس<PERSON> الاتفاق", "benefitSentence": "🎯 حل مخصص لمتطلباتك الخاصة", "coolFactor": "🎯 حل مخصص + هندسة مرنة + نهج استشاري", "description": "حل مخصص لمتطلباتك الخاصة", "price": "<PERSON>ن<PERSON> الطلب", "badge": "مخصص الصنع", "cta": "ناقش المشروع", "benefits": ["تحليل المشروع الفردي", "هندسة حل مخصص", "اختيار تكنولوجيا مرن", "عمليات تطوير مكيفة", "استشارة وتخطيط شخصي", "هيكل مشروع قابل للتوسع", "تواصل مستمر", "ضمان الجودة والاختبار"], "trustLine": "نهج فردي لكل مشروع", "features": ["تحليل المشروع الفردي", "هندسة حل مخصص", "اختيار تكنولوجيا مرن", "عمليات تطوير مكيفة", "استشارة وتخطيط شخصي", "هيكل مشروع قابل للتوسع", "تواصل مستمر", "ضمان الجودة والاختبار"]}}, "otherInquiries": {"title": "لديك استفسار مختلف؟", "description": "احجز مكالمة وسنتولى الأمر", "bookCall": "ا<PERSON><PERSON><PERSON> مكالمة", "whatsappContact": "واتساب"}}, "showLess": "إظهار أقل", "moreFeatures": "المزيد من الميزات", "packagesSection": {"title": "باقاتنا", "subtitle": "حلول مخصصة لاحتياجاتك", "mvp": {"title": "تطوير MVP", "timeframe": "أسبوعان", "description": "أطلق فكرتك بسرعة مع الحد الأدنى من المنتج القابل للتطبيق", "features": ["تنفيذ الوظائف الأساسية", "تصميم واجهة مستخدم أساسية", "مصادقة المستخدم", "حل تخزين البيانات", "نشر على منصة واحدة"]}, "prototype": {"title": "باقة النموذج الأولي", "timeframe": "أسبوع", "description": "اختبر مفهومك بنموذج أولي وظيفي", "features": ["نماذج واجهة مستخدم تفاعلية", "وظائف أساسية", "تنفيذ تدفق المستخدم", "عرض تقديمي للأطراف المعنية"]}, "architecture": {"title": "هندسة المشروع", "timeframe": "1-2 أسبوع", "description": "أساس متين لنجاح مشروعك", "features": ["المواصفات الفنية", "تصميم هندسة النظام", "مخطط قاعدة البيانات", "توثيق واجهة برمجة التطبيقات", "خارطة طريق التطوير"]}, "consulting": {"title": "استشارات تقنية", "timeframe": "مستمر", "description": "إرشاد خبير لقراراتك التقنية", "features": ["توصيات مجموعة التكنولوجيا", "مراجعات الكود", "تحسين الأداء", "تقييم الأمان", "تخطيط قابلية التوسع"]}}, "solutionsPortfolio": {"title": "قصص نجاح العملاء", "subtitle": "MVPs Flutter مُثبتة حصلت على تمويل السلسلة A", "description": "مؤسسو SaaS اختارونا لبناء MVPs دخولهم للسوق. كل مشروع تم إطلاقه في الوقت المحدد ومكَّن من جولات تمويل ناجحة.", "trustMetrics": {"title": "لماذا تثق بنا الشركات الناشئة", "experience": "8+", "experienceLabel": "سنوات التركيز على Flutter", "successRate": "98%", "successRateLabel": "معد<PERSON> نجاح MVP", "fundingSuccess": "15+", "fundingSuccessLabel": "شركات ناشئة ممولة"}, "technicalCapabilities": {"title": "التميز التقني", "flutterMastery": {"title": "إتقان Flutter", "description": "5+ سنوات حصريًا في بناء تطبيقات Flutter الإنتاجية. قاعدة كود واحدة، أداء أصلي على كلا المنصتين.", "metrics": ["تطوير أسرع بنسبة 40% مقارنة بالتطوير الأصلي", "قاعدة كود واحدة، كلا المنصتين", "أداء أصلي مضمون"]}, "aiIntegration": {"title": "معمارية جاهزة للذكاء الاصطناعي", "description": "اجعل MVP الخاص بك مقاومًا للمستقبل مع قدرات الذكاء الاصطناعي المدمجة من اليوم الأول. OpenAI وClaude ونماذج ML مخصصة.", "metrics": ["تكامل OpenAI وClaude", "معالجة الذكاء الاصطناعي في الوقت الفعلي", "معمارية ML قابلة للتوسع"]}, "mvpDelivery": {"title": "تسليم MVP سريع", "description": "MVPs جاهزة للإنتاج في 4-6 أسابيع. Firebase backend ومصادقة ومدفوعات - كل ما تحتاجه للإطلاق.", "metrics": ["تسليم 4-6 أسابيع", "كود جاهز للإنتاج", "مجموعة إطلاق كاملة"]}}, "clientCaseStudies": {"title": "موثوق من قادة الصناعة", "subtitle": "نتائج حقيقية من عملاء حقيقيين", "lufthansa": {"title": "لوفتهانزا", "industry": "الطيران", "projectType": "تطبيق Flutter للمؤسسات", "description": "تطوير منصة إدارة السفر الحرجة باستخدام تقنية Flutter", "businessContext": "المساهمة في تطوير هندسة الهاتف المحمول على مستوى المؤسسة لأكبر مجموعة طيران في أوروبا", "results": ["إزالة الديون التقنية وتقدم خارطة طريق الميزات", "تنفيذ هندسة متعددة المنصات", "تحسينات الأداء المنجزة"]}, "unionInvestment": {"title": "يونيون إنفستمنت", "industry": "الخدمات المالية", "projectType": "تطبيق Run This Place", "description": "تطوير تطبيق Run This Place - نظام حجز لإدارة المواقف وأماكن العمل", "businessContext": "تطوير نظام حجز مبتكر لأماكن وقوف السيارات وأماكن العمل باستخدام تقنيات Flutter الحديثة", "results": ["تنفيذ نظام حجز المواقف", "تطوير ميزات إدارة أماكن العمل", "إنشاء تطبيق محمول سهل الاستخدام"]}, "togg": {"title": "توغ", "industry": "السيارات", "projectType": "تطبيق Flutter لإنترنت الأشياء", "description": "تطوير نظام تحكم ذكي في المركبات للسيارات الكهربائية", "businessContext": "المساهمة في تطوير تطبيق الهاتف المحمول لأول مشروع سيارة كهربائية تركية", "results": ["تنفيذ تحكم IoT في المركبات", "تطوير الاتصال في الوقت الفعلي", "إنشاء تطبيق مركبة سهل الاستخدام"]}}, "cta": {"title": "جاهز لبناء MVP الخاص بك؟", "subtitle": "انضم إلى 15+ شركة ناشئة اختارت Flutter لدخولها السوق", "primaryButton": "احجز مكالمة الاكتشاف", "secondaryButton": "عرض عمليتنا", "guarantee": "<PERSON>م<PERSON> التسليم في 4-6 أسابيع أو استرداد أموالك"}}, "portfolio": {"title": "خبراتنا", "subtitle": "القطاعات وحل المشكلات", "all": "جميع القطاعات", "screenshot": "لقطة شاشة", "screenshots": "لقطات شاشة", "problemsWeSolve": "المشكلات التي نحلها", "noSectorsFound": "لم يتم العثور على قطاعات للفلتر المحدد.", "labels": {"technologies": "التقنيات", "keyFeatures": "الميزات الرئيسية"}, "projects": [{"id": "logistics-platform", "title": "منصة اللوجستيات المؤسسية", "description": "تطوير منصة B2B لتتبع وإدارة سلاسل التوريد في الوقت الفعلي. تشمل التكامل مع أنظمة ERP الحالية والإبلاغ التلقائي.", "category": "Business", "technologies": ["Flutter", "Google Cloud", "REST API", "ERP Integration"], "features": ["تتبع في الوقت الفعلي والتحليلات", "إبلاغ تلقائي", "تكامل نظام ERP", "إدارة الوصول القائمة على الأدوار"]}, {"id": "detoxme", "title": "DetoxMe - العافية الرقمية", "description": "تطبيق للتخلص من السموم الرقمية وتعزيز عادات الهاتف الذكي الصحية.", "category": "Lifestyle", "technologies": ["Flutter", "Firebase", "Analytics", "Push Notifications"], "features": ["تتبع وقت الشاشة", "حظر التطبيقات", "التأمل", "تقارير التقدم"]}, {"id": "reserv", "title": "Reserv - <PERSON><PERSON><PERSON> المطعم", "description": "منصة حجز حديثة للمطاعم مع التوفر في الوقت الفعلي.", "category": "Business", "technologies": ["Flutter", "Firebase", "Payment APIs", "Real-time DB"], "features": ["حجز فوري", "تكامل الدفع", "تقييمات", "إدارة الطاولات"]}, {"id": "togg", "title": "TOGG - تجربة السيارة المتصلة", "description": "تطبيق رسمي للمركبات الكهربائية TOGG مع التحكم عن بُعد والميزات الذكية.", "category": "Automotive", "technologies": ["Flutter", "IoT", "Cloud Services", "Real-time Communication"], "features": ["التحكم عن بُعد", "إدارة الشحن", "التنقل", "حالة المركبة"]}], "categories": {"aiAssistant": "مساعد الذكاء الاصطناعي", "foodDelivery": "توصيل الطعام", "hospitality": "الضيافة", "business": "الأعمال", "social": "التواصل الاجتماعي", "automotive": "السيارات"}, "sectors": {"assistant": "تطبيقات المساعد", "food": "طلب الطعام والتوصيل", "hospitality": "الضيافة", "lifestyle": "تطبيقات نمط الحياة", "social": "وسائل التواصل الاجتماعي", "automotive": "السيارات", "medical": "الطب والرعاية الصحية", "business": "حلول الأعمال"}, "sectorDescriptions": {"assistant": "مساعدين مدعومين بالذكاء الاصطناعي يعززون الإنتاجية ويقدمون توصيات شخصية", "food": "منصات سلسة لطلب الطعام وتوصيله مع تتبع في الوقت الحقيقي", "hospitality": "حلول رقمية للفنادق وشركات الضيافة لتعزيز تجربة الضيوف", "lifestyle": "تطبيقات تعزز الحياة اليومية والعافية والتطوير الشخصي", "social": "منصات تربط الناس والمجتمعات من خلال الاهتمامات المشتركة", "automotive": "حلول ذكية لإدارة المركبات والملاحة ومساعدة السائق", "medical": "حلول صحية رقمية تحسن رعاية المرضى والعمليات الطبية", "business": "تطبيقات المؤسسات التي تبسط العمليات وتعزز الإنتاجية"}, "problems": {"assistant": {"1": "تحميل المعلومات", "2": "إدارة المهام", "3": "دعم اتخاذ القرار"}, "food": {"1": "إدارة الطلبات", "2": "لوجستيات التوصيل", "3": "اكتشاف المطاعم"}, "hospitality": {"1": "إدارة الضيوف", "2": "تحسين الخدمة", "3": "أنظمة الحجز"}, "lifestyle": {"1": "تتبع الصحة", "2": "تكوين العادات", "3": "التنظيم الشخصي"}, "social": {"1": "مشاركة المستخدم", "2": "اكتشا<PERSON> الم<PERSON><PERSON>وى", "3": "بناء المجتمع"}, "automotive": {"1": "مراقبة المركبات", "2": "تحسين الملاحة", "3": "تجربة السائق"}, "medical": {"1": "إدارة المرضى", "2": "مراقبة الصحة", "3": "أنظمة السجلات الطبية"}, "business": {"1": "تحسين سير العمل", "2": "إدارة البيانات", "3": "تعاون الفريق"}}, "viewDetails": "عرض التفاصيل", "viewAllProjects": "عرض جميع المشاريع"}, "clients": {"title": "عملاؤنا", "subtitle": "الشركات التي عملنا معها", "visitWebsite": "زيارة الموقع"}, "testimonials": {"title": "ما يقوله عملاؤنا", "subtitle": "تجارب حقيقية من محترفي الصناعة", "badge": "قصص نجاح العملاء", "readMore": "الآراء الكاملة", "readLess": "قراءة أقل", "testimonials": [{"quote": "موثوق ومنظم! <PERSON> <PERSON> هو مطور تطبيقات جوال موثوق للغاية وودود، لا يترك أي رغبة غير محققة سواء في المكونات التقنية أو في تجربة المستخدم!", "name": "<PERSON>in <PERSON>.", "designation": "الرئيس التنفيذي في Ultimind", "src": "/images/testimonials/emin.jpeg", "company": "<PERSON><PERSON><PERSON><PERSON>", "companyLogo": "/images/companies/lumeus.png", "industry": "Technology", "rating": 5, "projectType": "تطوير تطبيقات الجوال", "deliveryTime": "تم التسليم في الوقت المحدد", "badge": "عميل موثّق", "results": ["تجربة مستخدم مثالية", "تنفيذ منظم", "لا رغبات غير محققة"]}, {"quote": "تعرفت على Viktor في مشروع كمطور جوال مؤهل جداً وأقدره. قدرته السريعة على الفهم والتزامه العالي في حل المشاكل أمر ملحوظ. من خلاله نما الفريق بشكل هائل في الأداء والتماسك فيما بينهم وفي تطوير الفريق بشكل عام.", "name": "<PERSON><PERSON>.", "designation": "مديرة المشاريع في Union Investment Real Estate GmbH", "src": "/images/testimonials/stefanie.jpeg", "company": "Union Investment", "companyLogo": "/images/companies/union-investment.png", "industry": "Real Estate", "rating": 5, "projectType": "التعاون الجماعي", "deliveryTime": "تجاوز التوقعات", "badge": "عميل مؤسسي", "results": ["تحسن أداء الفريق", "تماسك أفضل", "حل مؤهل للمشاكل"]}, {"quote": "أنا ممتن للتعاون المذهل الذي حصلنا عليه في مشروع التطبيق الجوال. خبرته في تطوير الواجهة الأمامية كانت ذات قيمة لا تقدر بثمن حقاً، وأنا متحمس للنتائج الممتازة التي حققناها معاً.", "name": "<PERSON>", "designation": "مصمم UX للمنتجات في Togg", "src": "/images/testimonials/mohammed.jpeg", "company": "<PERSON><PERSON>", "companyLogo": "/images/companies/togg.png", "industry": "Automotive", "rating": 5, "projectType": "تطوير UI/UX", "deliveryTime": "جودة استثنائية", "badge": "شريك مميز", "results": ["خبرة لا تقدر بثمن في الواجهة الأمامية", "نتائج ممتازة", "تعاون مثالي"]}, {"quote": "كرائدة أعمال في قطاع العقارات، كنت أبحث عن دعم مهني في الحلول القائمة على المواقع والتطبيقات. Viktor استطاع أن يشرح لي الأمور المعقدة ببساطة وفهم أهدافي على الفور.", "name": "<PERSON>.", "designation": "خبيرة العقارات العالمية في Walenwein Immobilien", "src": "/images/testimonials/natalia.jpeg", "company": "Lufthansa", "companyLogo": "/images/companies/lufthansa.png", "industry": "Aviation", "rating": 5, "projectType": "تطوير Full-Stack", "deliveryTime": "جدولة زمنية مثالية", "badge": "شريك طويل الأمد", "results": ["شرح الأمور المعقدة ببساطة", "فهم فوري", "تنفيذ مهني"]}, {"quote": "عمل رائع! التطبيق يعمل تماماً كما تصورناه. Viktor محترف جداً ويلتزم بجميع المواعيد.", "name": "<PERSON><PERSON>", "designation": "المدير العام", "src": "/images/testimonials/placeholder-male.svg", "company": "شركة محلية", "companyLogo": null, "industry": "Business", "rating": 5, "projectType": "تطبيق أعمال", "deliveryTime": "في الوقت المحدد", "badge": "عميل راضٍ", "results": ["يعمل بشكل مثالي", "ع<PERSON><PERSON> مهني", "التزام بجميع المواعيد"], "isAnonymous": true}, {"quote": "تواصل ممتاز ومهارات تقنية. <PERSON> قدم تماماً ما نحتاجه لشركتنا الناشئة. موصى به للغاية!", "name": "<PERSON><PERSON>", "designation": "مؤسسة شركة ناشئة", "src": "/images/testimonials/placeholder-female.svg", "company": "شركة تقنية ناشئة", "companyLogo": null, "industry": "Technology", "rating": 5, "projectType": "تطوير MVP", "deliveryTime": "ق<PERSON><PERSON> الموعد المحدد", "badge": "عميلة دولية", "results": ["تواصل ممتاز", "تنفيذ تقني مثالي", "حل مناسب للشركات الناشئة"], "isAnonymous": true}, {"quote": "راضٍ جداً عن تطوير تطبيق الصحة الخاص بنا. <PERSON> نفذ متطلباتنا بشكل مثالي وكان متاحاً دائماً للأسئلة.", "name": "<PERSON><PERSON> <PERSON><PERSON>", "designation": "المدير الطبي", "src": "/images/testimonials/placeholder-doctor.svg", "company": "عيادة طبية", "companyLogo": null, "industry": "Healthcare", "rating": 5, "projectType": "تطبيق صحي", "deliveryTime": "كما هو موعود", "badge": "خ<PERSON><PERSON>ر طبي", "results": ["تنفيذ مثالي", "متاح دائماً", "خبرة في التطبيقات الصحية"], "isAnonymous": true}], "stats": {"satisfiedCustomers": "عملاء راضون", "averageRating": "متوسط التقييم", "successfulApps": "تطبيقات ناجحة"}}, "aiEnhanced": {"badge": "ريادة التكنولوجيا", "title": "تطوير المستقبل: AI-Enhanced Coding", "subtitle": "أدوات الذكاء الاصطناعي المتطورة تلتقي مع أساليب التطوير المثبتة لتحقيق نتائج أعمال متفوقة.", "businessBenefits": [{"title": "وصول أسرع للسوق", "description": "الذكاء الاصطناعي يسرع التطوير للحصول على ميزة تنافسية وإيرادات مبكرة.", "icon": "rocket"}, {"title": "موثوقية عالية ورضا المستخدمين", "description": "التحليل الذكي يقلل الأخطاء للحصول على تطبيقات مستقرة.", "icon": "shield"}, {"title": "حلول مستقبلية وقابلة للتوسع", "description": "قاعدة كود نظيفة تنمو مع الأعمال، محمية الاستثمار.", "icon": "star"}], "processTitle": "عمليتنا لتحقيق أقصى قدر من الكفاءة والجودة", "aiFeatures": [{"title": "توليد الكود الآلي", "description": "تطوير مدعوم بالذكاء الاصطناعي لتنفيذ أسرع", "icon": "bot"}, {"title": "فحص الأخطاء الذكي", "description": "ضوابط جودة تلقائية مع كل التزام", "icon": "check"}, {"title": "اختبار ذكي", "description": "حالات اختبار يولدها الذكاء الاصطناعي للسيناريوهات الحرجة", "icon": "search"}, {"title": "تحسين الأداء", "description": "الذكاء الاصطناعي يحلل ويحسن مسارات الكود الحرجة", "icon": "gauge"}, {"title": "توثيق آلي", "description": "الذكاء الاصطناعي ينتج وثائق شاملة وتعليقات", "icon": "text"}, {"title": "إعادة تأهيل ذكية", "description": "الذكاء الاصطناعي يقترح وينفذ تحسينات الكود", "icon": "refresh"}, {"title": "إكمال واعي للسياق", "description": "إكمال كود ذكي بناءً على سياق المشروع", "icon": "brain"}, {"title": "تحليل تنبؤي", "description": "توقع ومنع المشاكل المحتملة", "icon": "zap"}]}, "contact": {"title": "اتصل بنا", "subtitle": "لنبدأ في تشكيل المستقبل", "description": "تواصل معنا لأي استفسارات أو مناقشات المشاريع أو لجدولة استشارة. نحن هنا لمساعدتك في تحقيق رؤيتك الرقمية.", "name": "الاسم", "email": "الب<PERSON>يد الإلكتروني", "phone": "الهاتف", "message": "الرسالة", "send": "إرسال رسالة", "yourName": "اسمك", "yourEmail": "بريدك الإلكتروني", "subject": "الموضوع", "howCanIHelp": "كيف يمكنني المساعدة؟", "yourMessageHere": "رسالتك هنا", "getInTouch": "تواصل معنا", "sendMessage": "أرسل رسالة", "schedule": "جدولة مكالمة", "freeConsultation": "احجز استشارة مجانية لمدة 15 دقيقة", "location": "الموقع", "submitButton": "إرسال", "sending": "جاري الإرسال...", "messageSent": "تم إرسال الرسالة!", "errorTryAgain": "خطأ، يرجى المحاولة مرة أخرى", "tryAgain": "حاول مرة أخرى", "orSchedule": "أو قم بجدولة اجتماع مباشرة باستخدام رابط التقويم", "formDescription": "املأ النموذج أدناه وسأتواصل معك قريباً", "yourRequest": "طلبك", "company": "الشركة", "yourCompanyName": "اسم شركتك", "timeline": "الجدول الزمني", "selectTimeline": "اختر الجدول الزمني...", "asap": "في أسرع وقت ممكن", "oneToThreeMonths": "1-3 <PERSON><PERSON><PERSON><PERSON>", "threeToSixMonths": "3-6 <PERSON><PERSON><PERSON><PERSON>", "flexible": "مرن", "estimatedBudget": "الميزانية المقدرة", "selectBudgetRange": "اختر نطاق الميزانية...", "below5k": "أقل من €5,000 - مشاريع صغيرة", "fiveToFifteenK": "€5,000 - €15,000 - مشاريع متوسطة", "fifteenToThirtyK": "€15,000 - €30,000 - مشاريع كبيرة", "above30k": "€30,000+ - مشاريع مؤسسية", "notSure": "لست متأكداً بعد - دعنا نناقش", "projectDescription": "وصف المشروع", "projectPlaceholder": "اوصف بإيجاز فكرة مشروعك وأهدافك ومتطلباتك...", "services": {"prototype": "📱 نموذج أولي سريع - ابتداءً من €3,000", "mvp": "🚀 تطوير MVP - ابتداءً من €7,500", "saasGrowth": "📈 حزمة نمو SaaS - ابتداءً من €15,000", "custom": "🎯 طلب مخصص - بالاستشارة"}, "serviceLabels": {"selectService": "اختر خدمة...", "prototype": "نموذج أولي سريع", "mvp": "تطوير MVP", "saasGrowth": "حزمة نمو SaaS", "custom": "ط<PERSON><PERSON> مخصص"}}, "footer": {"copyright": "© 2025 Innovatio. جميع الحقوق محفوظة.", "description": "نطور تطبيقات متطورة للجوال والويب تعمل على تحويل الأعمال من خلال التكنولوجيا المبتكرة.", "quickLinks": "روابط سريعة", "footerContact": "اتصل بنا", "legal": "قانوني", "newsletter": "النشرة الإخبارية", "newsletterDesc": "اشترك في نشرتنا الإخبارية لتلقي التحديثات والرؤى.", "emailPlaceholder": "أدخل بريدك الإلكتروني", "subscribe": "اشترك", "builtWith": "تم البناء باستخدام", "and": "و", "downloadCV": "سيرتي الذاتية", "englishCV": "الإنجليزية", "germanCV": "الألمانية"}, "cookies": {"title": "موافقة ملفات تعريف الارتباط", "description": "نستخدم ملفات تعريف الارتباط لتحسين تجربة التصفح لديك، وتقديم إعلانات أو محتوى مخصص، وتحليل حركة المرور لدينا. بالنقر على \"قبول الكل\"، فإنك توافق على استخدامنا لملفات تعريف الارتباط.", "acceptAll": "قبول الكل", "decline": "<PERSON><PERSON><PERSON>", "customize": "تخصيص", "necessary": "ملفات تعريف الارتباط الضرورية", "necessaryDesc": "ملفات تعريف الارتباط هذه ضرورية لتشغيل موقع الويب بشكل صحيح ولا يمكن تعطيلها.", "analytics": "ملفات تعريف الارتباط التحليلية", "analyticsDesc": "تساعدنا ملفات تعريف الارتباط هذه على فهم كيفية تفاعل الزوار مع موقعنا وتساعدنا على تحسين خدماتنا.", "marketing": "ملفات تعريف الارتباط التسويقية", "marketingDesc": "تُستخدم ملفات تعريف الارتباط هذه لتتبع الزوار عبر مواقع الويب لعرض الإعلانات ذات الصلة.", "functional": "ملفات تعريف الارتباط الوظيفية", "functionalDesc": "تمكن ملفات تعريف الارتباط هذه وظائف محسنة وتخصيص على موقعنا.", "save": "حفظ التفضيلات", "settings": "إعدادات ملفات تعريف الارتباط", "close": "إغلاق", "cookiePolicy": "سياسة ملفات تعريف الارتباط", "privacyPolicy": "سياسة الخصوصية"}, "heroParallax": {"title": "أفضل استوديو للتطوير", "subtitle": "نبني منتجات جميلة باستخدام أحدث التقنيات والأطر. نحن فريق من المطورين والمصممين المتحمسين الذين يحبون بناء منتجات مذهلة.", "products": {"mobileApp": "تطوير تطبيقات الجوال", "webDev": "تطوير الويب", "uiux": "تصميم واجهة المستخدم وتجربة المستخدم", "ecommerce": "حلول التجارة الإلكترونية", "ai": "دمج الذكاء الاصطناعي", "cloud": "حلول سحابية", "devops": "ديفوبس", "dataAnalytics": "تحليل البيانات", "blockchain": "تطوير البلوكتشين", "arvr": "حلول الواقع المعزز/الافتراضي", "customSoftware": "برمجيات مخصصة", "mobileGame": "تطوير ألعاب الجوال", "iot": "حلول إنترنت الأشياء", "api": "تطوير API", "cybersecurity": "الأمن السيبراني"}}, "featuresSection": {"features": [{"title": "مُصمم للمطورين", "description": "مُصمم للمهندسين والمطورين والحالمين والمفكرين والفاعلين.", "icon": "IconTerminal2"}, {"title": "سهولة الاستخدام", "description": "إنه سهل الاستخدام مثل استخدام جهاز آبل، وباهظ الثمن مثل شرائه.", "icon": "IconEaseInOut"}, {"title": "تسعير لا مثيل له", "description": "أسعارنا هي الأفضل في السوق. لا يوجد حد أقصى، لا يوجد التزام، لا حاجة لبطاقة ائتمان.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "ضمان وقت التشغيل 100٪", "description": "لا يمكن لأحد إيقافنا.", "icon": "IconCloud"}, {"title": "هندسة متعددة المستأجرين", "description": "يمكنك ببساطة مشاركة كلمات المرور بدلاً من شراء مقاعد جديدة.", "icon": "IconRouteAltLeft"}, {"title": "دعم عملاء 24/7", "description": "نحن متواجدون 100٪ من الوقت. على الأقل وكلاء الذكاء الاصطناعي لدينا.", "icon": "IconHelp"}, {"title": "ضمان استعادة الأموال", "description": "إذا لم يعجبك EveryAI، فسنقنعك بأن تحبنا.", "icon": "IconAdjustmentsBolt"}, {"title": "وكل شيء آخر", "description": "لقد نفدت أفكار النصوص لدي. تقبل اعتذاري الصادق.", "icon": "IconHeart"}]}, "seo": {"meta": {"home": {"title": "تطوير تطبيقات الجوال والحلول الرقمية | Innovatio-Pro", "description": "تطوير تطبيقات الجوال الاحترافية باستخدام Flutter، تكامل الذكاء الاصطناعي والحلول الرقمية. تطوير MVP، النماذج الأولية والتطوير الشامل من Innovatio-Pro.", "keywords": "تطوير تطبيقات الجوال، تطوير Flutter، تطوير MVP، تكامل الذكاء الاصطناعي، حلول رقمية، نماذج أولية، تطوير شامل، Innovatio-Pro"}, "templates": {"title": "حلول التطبيقات الجوال وتكامل الذكاء الاصطناعي - Innovatio-Pro", "description": "اكتشف حلول التطبيقات الجوال، تكامل الذكاء الاصطناعي والمنصات الرقمية. متخصصون في Flutter، تطوير MVP والتقنيات الجوال المبتكرة.", "keywords": "حلول التطبيقات الجوال، تكامل الذكاء الاصطناعي، تطبيقات Flutter، تطوير MVP، منصات رقمية، تقنيات الجوال، تطوير التطبيقات"}, "services": {"title": "خدمات تطوير التطبيقات الجوال - Flutter والذكاء الاصطناعي والحلول الرقمية", "description": "خدمات تطوير التطبيقات الجوال الاحترافية باستخدام Flutter، تكامل الذكاء الاصطناعي والتقنيات الحديثة. متخصصون في MVP، النماذج الأولية والحلول الجوال الشاملة.", "keywords": "خدمات التطبيقات الجوال، تطوير Flutter، خدمات MVP، تكامل الذكاء الاصطناعي، حلول جوال، خدمات تطوير التطبيقات، التحول الرقمي"}, "about": {"title": "حول Innovatio-Pro - تطوير التطبيقات الجوال وأخصائي الذكاء الاصطناعي", "description": "تعرف على Innovatio-Pro، أخصائيك في تطوير التطبيقات الجوال باستخدام Flutter، تكامل الذكاء الاصطناعي والحلول الرقمية المبتكرة.", "keywords": "Innovatio-Pro، مطور تطبيقات جوال، أخصائي Flutter، تكامل الذكاء الاصطناعي، حلول رقمية، شركة تطوير التطبيقات"}, "contact": {"title": "اتصل بنا - تطوير التطبيقات الجوال والحلول الرقمية | Innovatio-Pro", "description": "اتصل بـ Innovatio-Pro للحصول على تطوير التطبيقات الجوال الاحترافية، تكامل الذكاء الاصطناعي والحلول الرقمية. استشارة مجانية متاحة لمشروعك القادم.", "keywords": "اتصال تطوير التطبيقات الجوال، استشارة تطوير Flutter، خدمات تكامل الذكاء الاصطناعي، طلب حلول رقمية، اتصال Innovatio-Pro"}, "pricing": {"title": "أسعار تطوير التطبيقات الجوال - MVP، النماذج الأولية وحلول الذكاء الاصطناعي", "description": "أسعار شفافة لتطوير التطبيقات الجوال، تطوير MVP، النماذج الأولية وتكامل الذكاء الاصطناعي. من النماذج الأولية السريعة إلى الحلول الجوال الشاملة.", "keywords": "أسعار تطوير التطبيقات الجوال، تكلفة تطوير MVP، أسعار تطبيقات Flutter، تعرفة تكامل الذكاء الاصطناعي، أسعار النماذج الأولية، عروض تطوير التطبيقات"}, "faq": {"title": "الأسئلة الشائعة - تطوير التطبيقات الجوال والحلول الرقمية | Innovatio-Pro", "description": "أسئلة شائعة حول خدمات تطوير التطبيقات الجوال، تطوير Flutter، تكامل الذكاء الاصطناعي والحلول الرقمية. دعم وإجابات من Innovatio-Pro.", "keywords": "أسئلة شائعة تطوير التطبيقات الجوال، أسئلة تطوير Flutter، مساعدة تكامل الذكاء الاصطناعي، دعم الحلول الرقمية، إجابات تطوير التطبيقات"}}, "openGraph": {"siteName": "Innovatio-Pro - تطوير التطبيقات الجوال والحلول الرقمية", "defaultImage": "/images/og-innovatio-pro-ar.jpg", "defaultImageAlt": "Innovatio-Pro - تطوير التطبيقات الجوال باستخدام Flutter وتكامل الذكاء الاصطناعي"}, "jsonLd": {"organization": {"name": "Innovatio-Pro", "alternateName": "Innovatio-Pro تطوير الجوال", "description": "أخصائي في تطوير التطبيقات الجوال باستخدام Flutter، تكامل الذكاء الاصطناعي والحلول الرقمية المبتكرة للشركات الحديثة.", "url": "https://innovatio-pro.com", "telephone": "+49-175-9918357", "email": "<EMAIL>", "address": {"streetAddress": "Remote Office", "addressLocality": "Wyoming", "addressRegion": "Wyoming", "postalCode": "82001", "addressCountry": "US"}, "sameAs": ["https://github.com/innovatio-pro", "https://linkedin.com/company/innovatio-pro"]}}}, "leadCaptureForm": {"headline": "ابد<PERSON> رحلة Flutter في 3 خطوات", "introduction": "احجز مكالمة الاستكشاف المجانية في دقيقتين فقط", "steps": {"personal": "بياناتك", "project": "معلومات المشروع", "final": "التفاصيل النهائية"}, "stepDescriptions": {"personal": "أخبرنا من أنت", "project": "شارك رؤيتك", "final": "أض<PERSON> اللمسات الأخيرة"}, "form": {"fullName": "الاسم الكامل", "email": "عنوان البريد الإلكتروني", "company": "الشركة", "mvpGoal": {"label": "ما هو هدفك الأساسي؟", "placeholder": "اختر هدفك الرئيسي", "options": {"prototyping": "التحقق من فكرتي بنموذج أولي", "mvpLaunch": "بناء وإطلاق MVP الخاص بي", "saasScaling": "توسيع منتج SaaS الحالي", "aiIntegration": "إضافة ميزات الذكاء الاصطناعي لتطبيقي", "consulting": "الحصول على إرشادات واستراتيجية تقنية", "notSure": "<PERSON>ير متأكد بعد - أح<PERSON>اج إرشادات"}}, "timeline": {"label": "الجدول الزمني", "placeholder": "متى تبدأ؟", "options": {"asap": "في أقرب وقت ممكن", "fourToSix": "خلال 4-6 أسابيع", "threeMonths": "خ<PERSON>ال 2-3 <PERSON><PERSON><PERSON><PERSON>", "undecided": "الجدول الزمني مرن"}}, "budget": {"label": "الميزانية", "placeholder": "نطاق الميزانية", "options": {"below10k": "أقل من €10,000", "10to20k": "€10,000 - €20,000", "20to50k": "€20,000 - €50,000", "above50k": "€50,000+", "notSure": "<PERSON>ير متأكد بعد"}}, "additionalNotes": {"label": "أخبرنا عن مشروعك", "placeholder": "صف بإيجاز فكرة تطبيقك، المستخدمين المستهدفين، أو أي متطلبات محددة..."}, "whatHappensNext": {"title": "ما الذي يحدث بعد ذلك؟", "steps": ["سنراجع مشروعك خلال 24 ساعة", "جدولة مكالمة استكشاف 15-30 دقيقة", "الحصول على إرشادات مخصصة لـ MVP الخاص بك"]}, "validation": {"nameRequired": "الاسم مطلوب", "emailRequired": "البريد الإلكتروني مطلوب", "emailInvalid": "ير<PERSON>ى إدخال عنوان بريد إلكتروني صالح", "goalRequired": "يرجى اختيار هدفك الأساسي", "timelineRequired": "يرجى اختيار الجدول الزمني المفضل"}}, "navigation": {"back": "← السابق", "next": "التالي →", "submit": "احجز مكالمة الاستكشاف", "submitting": "جار الإرسال...", "stepOf": "خطوة"}, "successMessage": {"title": "شكراً لك!", "description": "سنتواصل معك خلال 24 ساعة لجدولة مكالمة الاستكشاف المجانية.", "scheduleCall": "جدول المكالمة الآن", "whatsapp": "واتساب", "backToSite": "← العودة للصفحة الرئيسية", "nextSteps": {"title": "ما الذي يحدث بعد ذلك:", "steps": ["مراجعة تفاصيل مشروعك", "تواصل شخصي خلال 24 ساعة", "جدولة مكالمة استكشاف 15-30 دقيقة", "استكشاف رؤيتك وتقديم الإرشادات"]}}, "footer": {"preferDirectContact": "تفضل التواصل المباشر؟", "whatsapp": "واتساب", "email": "بريد إلكتروني"}}, "blog": {"title": "رؤى تقنية", "description": "استكشف أحدث الاتجاهات والرؤى والابتكارات في تطوير الجوال. من أفضل ممارسات Flutter إلى استراتيجيات تكامل الذكاء الاصطناعي - ابق في المقدمة في الثورة التقنية.", "categoriesTitle": "الفئات", "categoriesDescription": "استكشف المقالات عبر مجالات تقنية مختلفة", "subscriptionTitle": "ابق على اطلاع", "subscriptionDescription": "احصل على أحدث رؤى Flutter واتجاهات الذكاء الاصطناعي والابتكارات التقنية في صندوق الوارد الخاص بك.", "readMore": "اقر<PERSON> المزيد", "noArticles": "لم يتم العثور على مقالات", "author": "المؤلف", "readingTime": "وقت القراءة", "views": "المشاهدات", "tags": "العلامات", "floatingButton": {"tooltip": "المدونة التقنية", "currentArticle": "المقال الحالي", "viewAll": "عرض جميع المقالات", "newPosts": "منشورات جديدة"}, "categories": {"all": "جميع المقالات", "company": "حول الشركة", "flutter": "تطوير Flutter", "mobile": "اتجاهات الجوال", "ai": "تكامل الذكاء الاصطناعي", "performance": "الأداء", "caseStudies": "دراسات الحالة", "trends": "اتجاهات الصناعة"}}}