{"nav": {"home": "<PERSON>", "about": "Hakkımızda", "solutions": "Çözümler", "process": "<PERSON><PERSON><PERSON><PERSON>", "services": "<PERSON><PERSON><PERSON><PERSON>", "techStack": "Teknoloji", "portfolio": "Portföy", "pricing": "Fiya<PERSON><PERSON>rma", "contact": "İletişim", "usp": "<PERSON><PERSON>", "aienhanced": "AI-Güçlendirilmiş", "chatWithUs": "Bizimle Sohbet Et", "bookConsultation": "Danışmanlı<PERSON>", "apps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clients": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testimonials": "Referanslar", "blog": "Blog", "aipowereddevelopment": "AI Destekli Geliştirme", "kundenerfolgsgeschichten": "Müşteri Başarı Hikayeleri", "developmentprocess": "Geliştirm<PERSON>"}, "hero": {"title": "İşinizi dönüştüren", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painPoint": {"mobile": {"line1": "Tanıdık geliyor mu? Sonuç yok, çok toplantı?", "line2": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, te<PERSON><PERSON>, il<PERSON> ve<PERSON>!"}, "desktop": {"trigger": "<PERSON><PERSON><PERSON><PERSON>k geliyor mu?", "problems": "Kaos • Sonsuz Toplantılar • <PERSON><PERSON> • <PERSON><PERSON><PERSON><PERSON><PERSON>", "solution": "Hızlı. Güvenilir. Kolay. Kaygısız.", "mainMessage": {"text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, te<PERSON><PERSON>, il<PERSON> ve<PERSON>!", "parts": ["Dinleriz", "Çözeriz", "tes<PERSON> ed<PERSON>z", "ilham veririz!"]}}}, "tagline": "Uzmanlaşmış Flutter Geliştirme • AI-Ha<PERSON><PERSON><PERSON> • Firebase Backend", "description": "Raki<PERSON> b<PERSON><PERSON> vaa<PERSON><PERSON> b<PERSON>, biz çalışan MVP'ler teslim ediyoruz. Uygulamalarına dün ihtiyaç duyan 15+ startup tarafından savaş testinden geçmiş.", "typing": {"sequence1": "Flutter MVP → App Store → Kullanıcı Geri Bildirimi ✓", "sequence2": "Hızlı Teslimat. Prodüksiyona Hazır. Sıfır Drama.", "sequence3": "SaaS MVP'den Series A Başarısına."}, "cta": {"consultation": "Strateji danışmanlığı planlayın", "consultationSubtext": "Bağlayıcı olmayan ilk görüşme • 30 dakika • Ücretsiz"}, "metrics": {"development": {"label": "Daha Hızlı Geliştirme", "value": "40%", "description": "daha hızlı geliştirme", "howTitle": "Nasıl:", "howExplanation": "Yapay zeka destekli en yeni IDE'leri, otomatik kod üretimini ve akıllı kod tamamlamayı kullanarak."}, "timeToMarket": {"label": "Daha Hızlı Pazara Giriş", "value": "50%", "description": "daha hızlı yayınlama", "howTitle": "Nasıl:", "howExplanation": "Fast lane CI/CD pipeline, otomatik test, yapay zeka destekli kod inceleme ve optimize edilmiş dağıtım süreci."}, "costSaving": {"label": "Maliyet Tasarrufu", "value": "30%", "description": "mali<PERSON>t ta<PERSON><PERSON><PERSON>", "howTitle": "Nasıl:", "howExplanation": "Çoklu platform geliştirme için Flutter kullanımı, optimize edilmiş bulut kaynakları ve verimli geliştirme uygulamaları."}, "lessBugs": {"label": "<PERSON><PERSON>", "value": "90%", "description": "daha az hata", "howTitle": "Nasıl:", "howExplanation": "AI destekli kod incel<PERSON><PERSON>ri, otomatik testler ve kanıtlanmış geliştirme uygulamaları bugları drastik şekilde azaltır."}}, "trustedBy": {"title": "Sektör liderleri tarafından güvenilir"}, "trustVisual": {"satisfaction": "100% Müşteri Memnuniyeti", "appsLaunched": "50+ Başarıyla Lansmanı Yapılmış Uygulama", "fromTo": "Tıp Merkezlerinden El Sanatları İşletmelerine", "trust": "Stres Yok!", "additional": "+47 di<PERSON><PERSON>", "industries": {"doctors": "Doktorlar", "crafts": "El Sanatları", "startups": "Startuplar"}}, "rating": "Değerlendirme", "responseTime": "24s <PERSON><PERSON><PERSON>", "typeAnimation": ["Ölçeklenebilir Flutter MVP'<PERSON><PERSON> <PERSON> <PERSON><PERSON> Tabanı, Her İki Platform 📱", 2000, "AI-<PERSON><PERSON><PERSON><PERSON> - G<PERSON><PERSON>ğin Özellikleri İçin İnşa Edildi 🤖", 2000, "Firebase Backend - Güvenilir, Ölçeklenebilir Altyapı ⚡", 2000, "SaaS-Odaklı Geliştirme - Başarınız Önceliğimiz 🚀", 2000, "", 500], "businessValue": {"headline": "BT karmaşıklığı olmadan dijital dönüşüm", "description": "İşletmeler için profesyonel yazılım geliştirme. Stratejik danışmanlıktan üretken uygulamalara kadar - hepsi tek kaynaktan."}, "enterpriseFeatures": {"security": {"title": "Kurumsal Güvenlik", "description": "İş verileriniz için en yüksek güvenlik standartları"}, "architecture": {"title": "Ölçeklenebilir <PERSON>", "description": "İşinizle birlikte büyüyen geleceğe dönük çözümler"}, "roi": {"title": "ROI-optimize", "description": "Ölçülebilir verimlilik artışları ve maliyet tasarrufları"}, "support": {"title": "24/7 Kurumsal Destek", "description": "Kritik iş süreçleri için özel destek"}}, "companyTrust": {"title": "Pazar liderlerinin gü<PERSON>i"}}, "painPointSection": {"familiarQuestion": "<PERSON><PERSON><PERSON><PERSON>k geliyor mu?", "valueProposition": "Geliştirme maliyetlerinde €45,000'den fazla tasarruf edin", "valueSubtitle": "Kanıtlanmış yaklaşımımız maliyet ve zamanı yarıdan fazla azaltarak olağanüstü sonuçlar nasıl sağlıyor", "infiniteScrollPainPoints": ["Sonuçsuz sonsuz toplantılar", "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "Aylarca geli<PERSON>rme, gösterilecek bir şey yok", "Kritik projelerde junior geliştiriciler", "<PERSON><PERSON><PERSON> maliyetler ve sürprizler", "Aşırı karmaşık teknik çözümler", "Net zaman çizelgeleri veya kilometre taşları yok", "Geliştiricilerle kötü iletişim", "Vendor lock-in ve esneklik yok", "<PERSON>je kaosu ve kötü yönetim", "Eksik teknik uzmanlık", "İsteklere yavaş yanıt süreleri"], "mainTitle": "Bu sorunlar size günlük para kaybettiriyor", "mainSubtitle": "Siz de bu tipik geliştirme sorunlarından muzdarip misiniz?", "solutionsTitle": "Sorunları nasıl çözüyoruz", "solutionsSubtitle": "<PERSON><PERSON> i<PERSON>e yarayan hazı<PERSON>", "costComparisonTitle": "Kapsamlı Maliyet ve Değer Analizi", "traditionalLabel": "Geleneksel Yaklaşım", "ourSolutionLabel": "Bizim <PERSON>ümüz", "savingsText": "Toplam tasarruf: €45,000 + 6 hafta daha hızlı teslimat", "provenText": "Farklı sektörlerden 15+ başarılı projeyle kanı<PERSON>", "ctaText": "Bugün para tasarrufu yapmaya başlayın", "comparisonHeaders": {"criteria": "Karşılaştırma Kriterleri", "traditional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ourSolution": "Kanıtlanmış Çözümümüz"}, "comparisonLabels": {"cost": "Geliştirm<PERSON>", "time": "<PERSON>ns<PERSON>", "architecture": "<PERSON><PERSON><PERSON>", "architectureTraditional": "Sıfırdan inşa", "architectureOurs": "Kanıtlanmış ve savaş testli", "security": "Güvenlik Uygulaması", "securityTraditional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "securityOurs": "Kurumsal düzeyde hazır", "ai": "AI Entegrasyonu", "aiTraditional": "<PERSON><PERSON><PERSON>", "aiOurs": "Kullanıma hazır komutlar", "team": "<PERSON><PERSON><PERSON><PERSON>", "teamTraditional": "Junior ağırlıklı takımlar", "teamOurs": "Senior uzmanlar", "maintenance": "Uzun Vadeli Bakım", "maintenanceTraditional": "<PERSON><PERSON><PERSON><PERSON> devam eden mali<PERSON>", "maintenanceOurs": "Optimize ve verimli", "scalability": "Ölçeklenebilirlik", "scalabilityTraditional": "Yeniden inşa gerekli", "scalabilityOurs": "Ölçek için inşa edilmiş", "risk": "<PERSON><PERSON>", "riskTraditional": "Yüksek başarısızlık oranı", "riskOurs": "Kanıtlanmış başarı oranı"}, "problems": {"missedOpportunities": {"title": "Kaçırılan Pazar Fırsatları", "points": ["Sonuçsuz aylarca süren planlama aşamaları", "<PERSON><PERSON><PERSON> daha hı<PERSON>", "Fikirler toplantılarda takılıyor", "Pazar payı kaybediliyor"]}, "burningBudgets": {"title": "<PERSON><PERSON>", "points": ["Aşırı yüklenmiş iç ekipler", "<PERSON><PERSON><PERSON> maliyet olarak patlıyor", "Ölçülebilir ROI yok", "Verimsiz kaynak kullanımı"]}, "stagnatingScaling": {"title": "<PERSON><PERSON><PERSON><PERSON>", "points": ["Platform büyümeye yetişemiyor", "Genişlemede performans sorunları", "Teknik borçlar ilerlemeyi engelliyor", "Geleceğe hazır mimari yok"]}}, "solutions": {"readyArchitecture": {"title": "Hazır <PERSON>", "description": "Ölçeklenebilir ve üretime hazır", "value": "~€15,000 tasarruf"}, "seniorExpertise": {"title": "İstek Üzerine Uzman Yetkinlik", "description": "Anında verimlilik", "value": "~€8,000 tasarruf"}, "aiDevelopment": {"title": "AI Destekli Geliştirme", "description": "<PERSON><PERSON><PERSON> alanlar için hazır AI komutları", "value": "~€12,000 tasarruf"}, "enterpriseSecurity": {"title": "Kurumsal Güvenlik", "description": "Entegre güvenlik standartları", "value": "~€10,000 tasarruf"}}, "costComparison": {"traditional": {"amount": "~€65,000", "timeline": "10-12 hafta"}, "ourSolution": {"amount": "~€20,000", "timeline": "4-6 hafta"}}}, "about": {"scalableArchitecture": {"title": "İlk Günden Ölçeklenmeye Hazır", "description": "MVP mimariniz 10 kullanıcıyı da 10 milyonu da kaldırır. <PERSON><PERSON><PERSON><PERSON><PERSON>, te<PERSON><PERSON> kalır."}, "aiReady": {"title": "AI-Do<PERSON>al Entegrasyon", "description": "<PERSON>n<PERSON><PERSON> et<PERSON>iz her uygulama AI özellikleri için mimarize edilmiş. Yol haritanız gerektirdiğinde akıllı yetenekleri ekleyin."}, "strategicGuidance": {"title": "Stratejik Teknoloji Danışmanlığı", "description": "<PERSON><PERSON><PERSON> faz<PERSON>—proje başından itibaren mimar<PERSON>, teknoloji yol haritaları ve startup-test edilmiş teknik stratejiler alırsınız."}, "skills": "<PERSON><PERSON><PERSON>", "projects": "<PERSON><PERSON><PERSON>", "testimonials": "Müşteri Yorumları", "experience": "<PERSON><PERSON><PERSON>", "clients": "<PERSON><PERSON><PERSON>", "transformBusiness": "Teknoloji ile işletmeleri dönüştürmek", "createSolutions": "Geleceğe dönük dijital çözümler yaratmak", "stayingAhead": "Teknolojinin ön saflarında kalmak", "exceptionalUX": "Olağanüstü kullanıcı deneyimleri sunmak", "highPerformance": "Yüksek performanslı uygulamalar geliştirmek", "solvingChallenges": "Teknoloji ile gerçek iş zorluklarını çözmek", "flutterExpert": "<PERSON><PERSON>", "webDevAdvanced": "İleri Seviye", "aiIntegration": "Entegrasyon", "projectsCompleted": "<PERSON><PERSON><PERSON><PERSON>", "personalDedication": "<PERSON><PERSON><PERSON><PERSON>", "dedication": "Adanmışlık", "qualityFocused": "<PERSON><PERSON>", "personalService": "<PERSON><PERSON><PERSON><PERSON>", "focusedApproach": "Odaklı Yaklaşım", "dedicatedService": "<PERSON>zel <PERSON>", "clientReviews": "Müşteri Yorumları", "focusedService": "Odaklı Hizmet", "longTerm": "<PERSON><PERSON><PERSON>", "partnerships": "Ortaklıklar", "privacyFocused": "Gizlilik Odaklı", "secureServices": "Gü<PERSON><PERSON>", "personalAttention": "<PERSON><PERSON>isel <PERSON>", "dedicatedDeveloper": "<PERSON><PERSON>", "securityFocused": "Güvenlik Odaklı", "privacyRespected": "Gizlilik Saygılı", "quality": "<PERSON><PERSON>", "averageDelivery": "Ortalama Teslimat", "metrics": {"yearsExperience": "8+", "saasLaunched": "15+", "fundingRaised": "€2,5M+", "clientRating": "5,0/5", "avgDeliveryTime": "4-6 hafta", "flutterFocus": "100%", "zeroFailures": "0", "onTimeDelivery": "100%"}}, "aiPoweredDevelopment": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>: AI-G<PERSON>ş<PERSON>ril<PERSON>ş <PERSON>", "subtitle": "Kıdemli Uzmanlık En Son AI Araçlarıyla Buluştuğunda", "description": "8+ y<PERSON><PERSON><PERSON><PERSON> geliştirme deneyimimizi en son AI araçlarıyla birleştirerek olağanüstü sonuçlar sunuyoruz. Her kod satırı optimize edilmiş, test edilmiş ve üretime hazır.", "benefits": {"speed": {"title": "%40 Daha Hızlı Geliştirme", "description": "AI destekli kod üretimi programlamayı önemli ölçüde hızlandırır", "icon": "Zap"}, "quality": {"title": "%99 Daha Az Hata", "description": "AI destekli kod incelemeleri hataları sorun olmadan önce yakalar", "icon": "Shield"}, "innovation": {"title": "Premium Kod Kalitesi", "description": "En iyi uygulamalar ve desenler otomatik olarak uygulanır", "icon": "<PERSON><PERSON><PERSON>"}}, "features": [{"title": "GitHub Copilot Entegrasyonu", "description": "Akıllı kod tamamlama ve üretim", "detail": "Karmaşık özelliklerin daha hızlı uygulanması"}, {"title": "AI Kod <PERSON>ri", "description": "Her commit ile otomatik kalite kontrolleri", "detail": "Tutarlı kod kalitesi garantisi"}, {"title": "Akıllı Testler", "description": "Edge senaryolar için AI tarafından oluşturulan test durumları", "detail": "<PERSON><PERSON> yüksek test kapsamı, daha az s<PERSON><PERSON>"}, {"title": "Performans Optimizasyonu", "description": "AI kritik kod yollarını analiz eder ve optimize eder", "detail": "<PERSON>ha hı<PERSON>l<PERSON>, daha ve<PERSON>li uygulamalar"}], "stats": {"codeQuality": {"value": "%99.9", "label": "<PERSON>d <PERSON>"}, "timeReduction": {"value": "%40", "label": "Zaman Azaltımı"}, "bugPrevention": {"value": "%85", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "satisfaction": {"value": "%100", "label": "Müşteri Memnuniyeti"}}, "cta": {"title": "AI-G<PERSON>ş<PERSON>ril<PERSON>ş Geliştirmeye Hazır mısınız?", "description": "Ya<PERSON><PERSON><PERSON>ım geliştirmenin geleceğini bugün deney<PERSON>in", "primaryButton": "Nasıl Çalıştığını Görün", "secondaryButton": "Vaka Çalışmalarını Görüntüle"}}, "usp": {"badge": "Güçlü Yanlarımız", "title": "Innovatio-Pro Neden Farklı", "subtitle": "Toplantı yok. Teknik jargon yok. Sadece uygulamanız.", "description": "<PERSON><PERSON>er ajanslar sizi teknik terimlerle karıştırırken, biz sizin dilinizi konuşuyor ve sonuç veriyoruz.", "features": [{"title": "Teknik acemiler için kiş<PERSON>l da<PERSON>ışmanlık", "description": "Her şeyi basit terimlerle açıklarız. Jargon yok, karışıklık yok.", "icon": "👥"}, {"title": "Her sektör ve bütçe i<PERSON>", "description": "<PERSON><PERSON><PERSON>, zanaat çalışanı ya da startup - mükemmel çözümü geliştiriyoruz.", "icon": "🏗️"}, {"title": "<PERSON><PERSON> kaynaktan her şey: <PERSON><PERSON><PERSON><PERSON><PERSON>a", "description": "Her şey için tek iletişim kişisi. İlk fikirden uygulama mağazasına.", "icon": "🎯"}, {"title": "G<PERSON><PERSON> maliyet yok, s<PERSON><PERSON><PERSON> yok", "description": "Baştan şeffaf fiyatlar. Ne söylüyorsak onu yaparız.", "icon": "💰"}, {"title": "Sabit iletişim k<PERSON>i, net iletişim", "description": "Her zaman aynı iletişim kiş<PERSON>z var ve ne olduğunu tam olarak biliyorsunuz.", "icon": "📞"}, {"title": "Kalite kaybı olmadan hızlı teslimat", "description": "Kanıtlanmış süreçler ve modern araçlar sayesinde hızlı ve güvenilir teslimat.", "icon": "⚡"}, {"title": "Geleceğe hazır teknoloji", "description": "Başarınızla birlikte büyüyen modern, ölçeklenebilir çözümler kullanıyoruz.", "icon": "🚀"}, {"title": "%100 başarınıza odaklanma", "description": "Sadece siz memnun olduğunuzda biz de memnunuz. <PERSON>zin başarınız bizim başarımız.", "icon": "🏆"}], "cta": {"primary": "Ücretsiz Danışmanlık Al", "secondary": "Bağlayıcı Olmayan Görüşme Ayarla"}, "trustIndicators": {"freeConsultation": "Ücretsiz Danışmanlık", "nonBinding": "Bağlayıcı Değil", "thirtyMinutes": "30 Dk"}}, "ourProcess": {"title": "Kanıtlanmış Geliştirme Sürecimiz", "subtitle": "Sadece Birkaç Haftada Konseptten Mükemmel Flutter Uygulamasına", "description": "Zamanında olağanüstü Flutter uygulamaları sunan sistematik bir yaklaşım. Sürecimiz ka<PERSON>, şeffaflık ve ölçülebilir sonuçları garanti eder.", "badge": "Kanıtlanmış Sürecimiz", "deliverables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cta": {"title": "Projeniz için hazır mısınız?", "description": "Gereksinimlerinizi tartışalım ve özel bir geliştirme planı oluşturalım.", "primaryButton": "<PERSON><PERSON><PERSON>", "secondaryButton": "Başarı Hikayeleri"}, "steps": {"planning": {"title": "İsteğiniz", "description": "Vizyonunuzu, iş hedeflerinizi ve teknik gereksinimlerinizi anlayarak başlıyoruz. Bu aşama pazar araştırması, kullanıcı analizi ve detaylı yol haritası oluşturmayı içerir.", "duration": "3-5 <PERSON><PERSON><PERSON>", "deliverables": ["Proje Gereksinimleri ve Kapsamı", "Teknik Mimari Planı", "Zaman Çizelgesi ve Kilometre Taşı Yol Haritası", "Risk <PERSON>dirmesi ve Strateji"], "features": ["Gereksinim analizi ve proje kapsamı tanımı", "Teknik mimari ve teknoloji seçimi", "Kilometre taşı yol haritası ile zaman planlaması"]}, "design": {"title": "Geliştiriyoruz", "description": "Markanızla uyumlu çarpıcı UI/UX tasarımları oluşturuyoruz. Geliştirme başlamadan önce konseptleri doğrulamak için etkileşimli prototipler oluşturuyoruz.", "duration": "1 Hafta", "deliverables": ["Tam UI/UX Tasarım Sistemi", "Etkileşimli Tıklanabilir Prototipler", "Marka Kılavuzları Entegrasyonu", "Duyarlı Tasarım Spesifikasyonları"], "features": ["Marka entegrasyonu ile UI/UX tasarım sistemi", "Doğrulama iç<PERSON> et<PERSON> prototipler", "Tüm cihazlar için du<PERSON>lı tasarım"]}, "development": {"title": "Teslim ediyoruz", "description": "<PERSON><PERSON><PERSON>, ölçeklenebilir kodla hızlı Flutter geliştirme. Sürekli test, tüm cihazlar ve platformlarda mükemmel performansı sağlar.", "duration": "2-4 Hafta", "deliverables": ["Üretime Hazır Flutter Uygulaması", "<PERSON> Entegrasyonu", "Kapsamlı Test Paketi", "Performans Optimizasyonu"], "features": ["iOS ve Android için temiz Flutter kodu", "Backend entegrasyonu ve API gel<PERSON>ştirme", "Kapsamlı test ve optimizasyon"]}, "launch": {"title": "Başlatma ve Destek", "description": "Sürekli destekle uygulama mağazalarına sorunsuz dağıtım. Uygulamanızın başarıyla başlatılmasını ve mükemmel performans göstermeye devam etmesini sağlıyoruz.", "duration": "<PERSON><PERSON><PERSON><PERSON>", "deliverables": ["App Store Dağıtımı", "Başlatma Stratejisi ve <PERSON>a", "Performans İzleme <PERSON>", "Sürekli Destek ve Güncellemeler"], "features": ["App store gönderimi ve dağıtım", "Başlatma stratejisi ve <PERSON>ans izleme", "Sürekli bakım ve özellik güncellemeleri"]}}}, "advantages": {"title": "ÇÖZÜMLERİMİZ", "subtitle": "Başarılı Mobil Uygulamalar Geliştirmenize Nasıl Yardımcı Oluyoruz", "speed": "Hızlı Geliştirme", "speedDesc": "Kaliteden ödün vermeden çözümleri hızla <PERSON>uz", "stability": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stabilityDesc": "Uygulamalarımız stabilite ve performans için ta<PERSON>lanmıştır", "cost": "Maliyet Verimliliği", "costDesc": "Optimize edilmiş geliştirme süreci zamanınızı ve paranızı tasarruf eder", "timeToMarket": "Daha Hızlı Pazara Sunum", "timeToMarketDesc": "Ürününüzü hızla piyasaya sürün ve rekabette öne geçin", "aiIntegration": "Yapay Zeka Entegrasyonu", "aiIntegrationDesc": "İşletmenizi güçlü yapay zeka yetenekleriyle geliştirin", "development": "Full-<PERSON><PERSON>", "developmentTime": "6-12 hafta, pro<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "developmentDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, tam backend entegrasyonu ve gelişmiş özelliklerle eksiksiz mobil uygulama geliştirme.", "mvp": "MVP <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mvpTime": "4-8 hafta, pro<PERSON><PERSON>ığ<PERSON><PERSON> gö<PERSON>", "mvpDesc": "Konseptinizi doğrulamak ve erken kullanıcıları veya yatırımcıları çekmek için temel işlevselliğe sahip bir Minimum Uygulanabilir Ürün ile hızla başlayın.", "prototype": "Hızlı Prototipleme", "prototypeTime": "1-2 hafta, pro<PERSON><PERSON>ığına gö<PERSON>", "prototypeDesc": "<PERSON>tirmeye başlamadan önce kavramları hızla test edin, zaman ve kaynak tasarrufu sağlayın.", "qa": "<PERSON><PERSON>", "qaTime": "<PERSON><PERSON><PERSON><PERSON>, proje karmaşıklığına göre ölçeklendirilir", "qaDesc": "Uygulamanızın otomatik ve manuel test protokolleriyle tüm cihazlarda ve platformlarda kusursuz çalışmasını sağlayan kapsamlı testler.", "consulting": "Teknik Danışmanlık", "consultingTime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pro<PERSON><PERSON>ığ<PERSON><PERSON> g<PERSON><PERSON>", "consultingDesc": "Mobil uygulamanızı optimize etmek için teknoloji yığını, mi<PERSON><PERSON> ka<PERSON>lar ve uygulama stratejileri hakkında uzman tavsiyeleri.", "uiux": "UI/UX Tasarımı", "uiuxTime": "2-3 hafta, pro<PERSON><PERSON>ığına gö<PERSON>", "uiuxDesc": "Estetiği işlevsellikle dengeleyen, sezgisel ve çekici mobil deneyimler yaratan kullanıcı merkezli tasarım.", "maintenance": "Bakım ve Destek", "maintenanceTime": "<PERSON><PERSON><PERSON><PERSON>, proje karmaşıklığına göre ölçeklendirilir", "maintenanceDesc": "Uygulamanızın rekabet gücünü korumak için dü<PERSON> g<PERSON>, performans optimizasyonu ve özellik geliştirmeleriyle uzun vadeli destek.", "analytics": "Analitik Entegrasyonu", "analyticsTime": "1-2 hafta, pro<PERSON><PERSON>ığına gö<PERSON>", "analyticsDesc": "Kullanıcı davranışı hakkında uygulanabilir bilgiler elde etmek için veri izleme uygulaması, uygulamanız için veri odaklı kararlar almanızı sağlar.", "training": "Ekip Eğitimi", "trainingTime": "1-2 hafta, pro<PERSON><PERSON>ığına gö<PERSON>", "trainingDesc": "Ekibiniz için teslim<PERSON>an sonra uygulamanızı yönetme ve geliştirme konusunda kapsamlı eğitim.", "developmentEfficiency": "Geliştirme Verimliliği", "timeToMarketReduction": "Pazara Çıkış Süresinde Azalma", "conceptValidation": "Konsept Doğrulama", "bugFreeRate": "Hatasız Oranı", "technicalImprovement": "Teknik İyileştirme", "userSatisfaction": "Kullanıcı Memnuniyeti", "appUptime": "Uygulama Çalışma Süresi", "dataAccuracy": "<PERSON><PERSON>", "knowledgeRetention": "<PERSON><PERSON><PERSON>", "developmentInfo": {"title": "Geliş<PERSON><PERSON><PERSON>", "simpleApp": {"title": "<PERSON><PERSON><PERSON>", "examples": "Örnekler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, backend o<PERSON><PERSON> basit bilgi uygulamaları.", "features": ["Birkaç ekran (3-5)", "Backend entegrasyonu yok veya minimal", "Standart UI bileşenleri", "Karmaşık animasyon veya fonksiyon yok"], "timeline": {"total": "Geliştirme süresi: 4-8 hafta", "frontend": "Frontend: 2-4 hafta", "backend": "Backend (gerekirse): 1-2 hafta", "testing": "Test ve dağıtım: 1-2 hafta"}}, "mediumApp": {"title": "Or<PERSON> Sevi<PERSON> Uygulama", "examples": "Örnekler: <PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>gu<PERSON>aları, temel özelliklere sahip sosyal medya uygulamaları, kullanıcı kaydı ve veritabanı entegrasyonu olan uygulamalar.", "features": ["6-15 ekran", "Backend entegrasyonu (örn. REST veya GraphQL API'leri)", "Kullanıcı kaydı ve kimlik doğrulama", "Kullanıcı ve uygulama verileri için veritabanı", "Bazı animasyonlar ve interaktif öğeler", "<PERSON><PERSON>"], "timeline": {"total": "Geliştirme süresi: 8-16 hafta", "frontend": "Frontend: 4-6 hafta", "backend": "Backend: 3-5 hafta", "testing": "Test ve dağıtım: 2-3 hafta"}}, "complexApp": {"title": "Karmaşık Uygulama", "examples": "Örnekler: <PERSON><PERSON>, Instagram gibi uygulamalar veya gelişmiş özelliklere sahip bankacılık uygulamaları.", "features": ["15+ ekran", "Yüksek düzeyde interaktif kullanıcı arayüzü", "Gerçek zamanlı özellikler (örn. canlı takip, sohbet)", "Üçüncü taraf API entegrasyonları (örn. ödeme geçitleri, kart API'leri)", "Bulut entegrasyonlu ölçeklenebilir backend", "Güvenlik özellikleri (örn<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, iki faktörlü kimlik doğrulama)", "Çevrimdışı işlevsellik"], "timeline": {"total": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sü<PERSON>i: 16-32 hafta veya daha uzun", "frontend": "Frontend: 6-10 hafta", "backend": "Backend: 6-12 hafta", "testing": "Test ve dağıtım: 4-6 hafta"}}, "factors": {"title": "Geliştirme Süresini Etkileyen Faktörler", "teamSize": "Ekip büyüklüğü: <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> bir ekip (örn. frontend, backend ve QA için ayrı geliştiriciler) geliştirmeyi hızlandırabilir. Tek bir geliştirici daha fazla zaman gerektirir.", "technology": "Teknoloji: Native geliştirme (örn. iOS için Swift, Android iç<PERSON>) genellikle Flutter gibi çapraz platform yaklaşımlarından daha uzun sürer. Flutter en modern teknoloji olarak geliştirme süresini %40-60 azaltabilir.", "requirements": "Gereksinimler ve değişiklikler: <PERSON><PERSON><PERSON>ğ<PERSON>şiklikler veya belirsiz gereksinimler geliştirme süresini uzatabilir.", "testing": "Test ve hata ayıklama: <PERSON><PERSON><PERSON><PERSON><PERSON>, özellikle birden fazla platformda (iOS ve Android) daha fazla test süresi gerektirir.", "design": "Tasarım: <PERSON><PERSON><PERSON> daha az zaman gerektiri<PERSON>en, <PERSON><PERSON>, animasyonlu tasarımlar geliştirme süresini artırır."}, "summary": "Özet: <PERSON><PERSON><PERSON>: 4-8 hafta. Orta Seviye Uygulama: 8-16 hafta. Karmaşık Uygulama: 16-32 hafta veya daha uzun.", "aiComparison": "6-10 hafta Yapay Zeka destekli geliştirme ile Flutter ile bu süreleri %40-60 or<PERSON><PERSON><PERSON> azaltabilir, y<PERSON><PERSON><PERSON> kalite ve performansı koruyarak."}}, "serviceSection": {"title": "İş Liderleri Neden Teknik Uzmanlığımızı Seçiyor", "subtitle": "Hızlı Teslim Eden ve Akıllı Ölçeklendiren Özelleşmiş Hizmetler", "description": "Sekiz temel hizmet. Sıfır dikkat dağıtıcı. İşinizin dijital temeli ve pazara giriş hızında maksimum etki.", "viewAll": "Ücretsiz Danışmanlık Planla", "comparisonTitle": "Geleneksel Geliştirmeye Karşı Hız", "comparisonSubtitle": "<PERSON><PERSON><PERSON><PERSON>, biz teslim ediyoruz", "timeComparison": {"title": "Uygulamadan <PERSON>", "traditional": "16-24 hafta", "withUs": "4-6 hafta", "savings": "%70 daha hızlı"}, "costComparison": {"title": "Teknik Liderlik", "traditional": "Tam zamanlı Teknik Direktör", "withUs": "Stratejik Danışmanlık", "savings": "%80 maliyet tasarrufu"}, "qualityComparison": {"title": "<PERSON><PERSON>", "traditional": "Uygulama yeniden inşa gerekli", "withUs": "İlk günden ölçeklenmeye hazır", "savings": "Geleceğe dayanıklı"}, "flutterApp": {"title": "Flutter Uygulama Geliştirme", "shortDescription": "iOS ve Android için tek kod tabanından native uygulamalar geliştiriyoruz. Uygulamanız her iki platformda da mükemmel hissettiriyor – performans ya da kullanıcı deneyiminden ödün vermeden.", "description": "200+ başarılı Flutter projesinden sonra neyin önemli olduğunu tam olarak biliyoruz. <PERSON><PERSON><PERSON> ajan<PERSON>r hala deneyim ya<PERSON>, biz tuzakları çoktan aştık ve optimize edilmiş iş akışları geliştirdik. Projeniz 100,000+ aktif kullanıcıda bile stabil performans gösteren kanıtlanmış mimarilerden faydalanır. Google'ın Flutter ekibinin dilini konuşuyoruz – ve bunu sizin için somut iş sonuçlarına çeviriyoruz.", "timeframe": "4-12 hafta", "badge": "Çoklu Platform", "metrics": ["⚡ %60 Daha Hızlı", "💰 %50 Maliyet Tasarrufu", "📱 Tek Kod Tabanı", "🎯 %98 Müşteri Onayı"], "benefits": ["Ayrı iOS ve Android uygulamaları geliştirmekten %60 daha hızlı", "Kullanıcıları et<PERSON> g<PERSON>, profesyonel tasarım", "Geliştirme sırasında anında güncellemeler—değişiklikleri gerçek zamanlı görün"], "details": {"architecture": "Maksimum test edilebilirlik ve sürdürülebilirlik için Clean Architecture ile BLoC pattern. <PERSON><PERSON><PERSON><PERSON> yapı, mevcut kodu bozmadan yeni özellikler eklemeyi kolaylaştırır.", "whyFaster": "Hazır şablonlarımız ve kanıtlanmış bileşenlerimiz geliştirme süresini %60 azaltır. Tekerleği yeniden icat etmiyoruz – yüzlerce projede mükemmelleştirilmiş en iyi uygulamaları kullanıyoruz.", "bestPractices": "Otomatik testler, CI/CD pipeline'ları, kod inceleme süreçleri ve kod dokümantasyonu. Her kod satırı yayınlanmadan önce kalite kontrolünden geçer.", "techStack": "Flutter 3.x, Dart, Firebase/Supabase, REST/GraphQL API'ler, State Management (BLoC/Riverpod), Otomatik Testler, GitHub Actions ile CI/CD."}}, "mvp": {"title": "İş Uygulaması Geliştirme ve Prototipleme", "description": "İş fikrinden çalışan uygulamaya rekor sürede. Konseptinizi doğrulayan uygulamalar ve finansman sağlayan prototipler inşa ediyoruz—çünkü pazara hız iş başarısını belirler.", "timeframe": "2-6 hafta", "benefits": ["Finansman turları için 1-2 haftada yatırımcı-hazır prototipler", "Kullanı<PERSON>ı hesapları, ödemeler ve analitik ile tam iş uygulamaları", "Sadece test i<PERSON><PERSON>, ilk günden büyüme için inşa edildi"]}, "aiAssistant": {"title": "Akıllı Özellikler ve Kişiselleştirme", "description": "Uygulamanızı kullanıcılara gerçekten yardım eden yapay zeka ile akıllı hale getirin. Akıllı asistan<PERSON>, kişiselleştirilmiş öneriler ve müşterileri meşgul tutan ve geri getiren otomatik özellikler.", "timeframe": "3-5 hafta", "benefits": ["Akıllı kişiselleştirme yoluyla %30 daha yüksek kullanıcı etkileşimi", "Kullanıcı tercihlerini öğrenen yerleşik öneri sistemleri", "Deneysel özellikler değil, kull<PERSON>ıma hazır akıllı bileşenler"]}, "consulting": {"title": "İş Teknolojisi Danışmanlığı ve Uzman Rehberliği", "description": "<PERSON> zamanlı taahhüt olmadan üst düzey teknik liderlik. Startup'tan başarıya işletmeleri yönlendirmiş uzmanlardan stratejik teknoloji kararları, b<PERSON>yüme yol haritaları ve ölçeklendirme içgörüleri.", "timeframe": "Sürekli ortaklık", "benefits": ["Tam zamanlı maliyetin çok küçük bir kısmında Teknik Direktör seviyesi teknik strateji", "Daha sonra pahalı yeniden inşaları önleyen teknoloji incelemeleri", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> ka<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "backend": {"title": "Bulut Altyapısı ve Veri Yönetimi", "description": "Startup'tan işletmeye kırılmadan ölçeklendiren sağlam temeller. Gerçek zamanlı veriler, g<PERSON><PERSON>li kullanıcı hesapları ve patlayıcı kullanıcı büyümesini idare eden sistemler—çünkü altyapınız hırslarınızı sınırlandırmamalı.", "timeframe": "2-4 hafta entegrasyon", "benefits": ["Uygulamanızı büyülü hissettiren gerçek zamanlı özellikler", "İlk günden ölçeklenebilir altyapı—acı verici göçler yok", "Yerleşik analitik, güvenlik ve ödemeler geliştirme süresini %40 azaltır"]}, "uiDesign": {"title": "Profesyonel Uygulama Tasarımı ve Kullanıcı Deneyimi", "description": "Kullanıcıların kullanmayı se<PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> odaklı uygulama tasarımları. İlk konseptlerden etkileşim ve iş sonuçlarını artıran piksel-mükemmel arayüzlere.", "timeframe": "1-3 hafta", "benefits": ["Profesyonel maketler ve etkileşimli prototipler", "Kullanıcı deneyimi araştırması ve optimizasyonu", "Tüm cihazlar için du<PERSON>lı tasarım", "<PERSON> tasarım sistemleri ve bileşen kütüphaneleri", "Dönüşüm için optimize edilmiş kullanıcı akışları"]}, "testing": {"title": "Kalite Güvencesi ve App Store Lansmanı", "description": "Kusursuz uygulama performansı için kapsamlı test. Otomatik testlerden App Store gönderimlerine—uygulamanızın her kullanıcı için mükemmel çalıştığını garanti ediyoruz.", "timeframe": "1-2 hafta", "benefits": ["Güvenilirlik için otomatik testler", "App Store ve Google Play gönderim des<PERSON>ği", "Performans ve uyu<PERSON> testleri", "Tüm platformlarda çapraz cihaz testleri", "Hata izleme ve kalite raporları"]}, "cicd": {"title": "Otomatik Dağıtım ve Güncellemeler", "description": "Optimize edilmiş uygulama güncellemeleri ve otomatik kalite kontrolleri. Her güncellemenin test edilmiş, güvenli ve verimli bir şekilde teslim edilmesini sağlayan otomatik dağıtım hatları.", "timeframe": "3-5 gün", "benefits": ["Otomatik dağıtım kurulumu", "Otomatik testler ve kalite incelemeleri", "Çoklu ortam dağıtımları", "Güvenlik ve performans izleme", "<PERSON><PERSON><PERSON><PERSON><PERSON> kontrolü ve kolay geri <PERSON>"]}}, "services": {"badge": "Hizmetlerimiz", "title": "İnovasyon Uzmanlıkla Buluşuyor", "subtitle": "İşinizi dönüştüren dijital çözümler", "description": "Stratejik danışmanlıktan uzun vadeli desteğe kadar - dijital başarınız için tek elden tüm hizmetleri sunuyoruz.", "cta": {"button": "İletişime Geçin", "description": "Ücretsiz Danışmanlık • Bağlayıcı Değil • 30 Dk"}, "services": [{"title": "Strateji ve Konsept", "description": "İş hedeflerinizin analizi ve özel dijital strateji geliştirme.", "icon": "🎯"}, {"title": "UI/UX Tasarım", "description": "Sezgisel ve kullanıcı odaklı arayüzlerin tasarımı.", "icon": "🎨"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (iOS ve Android)", "description": "<PERSON>üm cihazlarda optimal performans için native ve hibrit geliştirme.", "icon": "📱"}, {"title": "Backend ve <PERSON>", "description": "Karmaşık uygulamalar için sağlam ve ölçeklenebilir sunucu mimarilerinin kurulumu.", "icon": "⚙️"}, {"title": "<PERSON><PERSON>i ve <PERSON>", "description": "Hatasız ve güvenli çalışma için manuel ve otomatik testler.", "icon": "🛡️"}, {"title": "Bakım ve Geliştirme", "description": "Lansmandan sonra uygulamanızın uzun vadeli desteği ve ölçeklendirmesi.", "icon": "🔧"}]}, "prices": {"title": "Fiyatlarımız", "subtitle": "Her Aşama iç<PERSON>", "description": "Önceden tanımlanmış paketlerimizden seçim yapın veya proje gereksinimlerinize göre özelleştirin. Hizmetleri birleştirerek %15 indirim kazanın.", "caseStudyTitle": "Vaka Çalışması: %40 Daha Hızlı Pazara Çıkış", "caseStudyDescription": "Wyoming merkezli fintech müşterimiz, MVP'sini sektör ortalamasından %40 daha hızlı piyasaya sürerek ek finansman sağlama ve büyümeyi hızlandırma imkanı buldu.", "promotionTitle": "Hizmetleri Birleştirin ve %15 Tasarruf Edin", "promotionDescription": "Herhangi iki veya daha fazla hizmeti birleştirin ve toplam proje maliyetinizden %15 in<PERSON><PERSON> ka<PERSON>n.", "leadCaptureTitle": "Flutter MVP Yolculuğunuzu Başlatın", "leadCaptureDescription": "Uygulama vizyonunuzu hayata geçirme konusunda nasıl yardımcı olabileceğimizi keşfetmek için ücretsiz bir keşif çağrısı ayırtın.", "discussButton": "Projenizi <PERSON>", "contactButton": "Bizimle İletişime Geçin", "pricingDisclaimer": "* <PERSON><PERSON><PERSON><PERSON>, proje gereksinimleri ve ek hizmetlere göre değişiklik gösterebilir. Özel ihtiyaçlarınıza uygun özel bir teklif için bizimle iletişime geçin.", "priceVariesInfo": "<PERSON><PERSON><PERSON>, pro<PERSON><PERSON>, ek gereksinimlere ve zaman kısıtlamalarına bağlı olarak değişebilir. Ayrıntılı bir teklif için bizimle iletişime geçin.", "fullDetails": "<PERSON>", "allIncludedFeatures": "<PERSON><PERSON><PERSON>", "backendOptions": "Backend Seçenekleri", "showDetails": "Detayları Göster", "valueProps": {"aiDevelopment": {"title": "AI-Des<PERSON><PERSON><PERSON>", "description": "%70 daha hızlı geliştirme"}, "backend": {"title": "Firebase ve Supabase", "description": "Modern Backend Çözümleri"}, "quality": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>"}}, "leadCapture": {"headline": "Flutter MVP Yolculuğunuzu Başlatın", "subheadline": "Ücretsiz Keşif Çağrınızı Ayırtın", "introduction": "Henüz tüm teknik detayları çözmenize gerek yok. Kurucularımız sizi her adımda yönlendirecek—konsept doğrulamadan App Store lansmanına kadar. Vizyonunuz hakkında rahat bir sohbetle başlayalım.", "trustStatement": "Her proje, <PERSON><PERSON><PERSON><PERSON><PERSON>, zaman çerçevenizi ve Flutter'ın pazara çıkış yolunuzu nasıl hızlandırabileceğini keşfettiğimiz ücretsiz bir strateji oturumuyla başlar.", "form": {"fullName": {"label": "Ad Soyad", "placeholder": "Adınız ve soyadınız", "required": true}, "email": {"label": "E-posta Adresi", "placeholder": "<EMAIL>", "required": true}, "company": {"label": "Şirket Adı", "placeholder": "Şirketiniz veya startup adınız", "required": false, "optional": "(Opsiyonel)"}, "mvpGoal": {"label": "<PERSON><PERSON>ncil hedefiniz nedir?", "placeholder": "<PERSON> amacın<PERSON><PERSON><PERSON> seçin", "required": true, "options": {"prototyping": "Fikrime doğrulama için bir prototip oluşturmak", "mvpLaunch": "<PERSON>'<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "saasScaling": "Mevcut SaaS ürünümü ölçeklendirmek", "aiIntegration": "Uygulamamaiz AI özellikleri eklemek", "consulting": "Teknik rehberlik ve strateji almak", "notSure": "Hen<PERSON>z emin <PERSON> - rehberlik gerekiyor"}}, "timeline": {"label": "<PERSON>e zaman başlamak istersiniz?", "placeholder": "Tercih ettiğiniz zaman çerçevesini seçin", "required": true, "options": {"asap": "Mümkün olan en kısa zamanda", "fourToSix": "4-6 hafta içinde", "threeMonths": "2-3 ay içinde", "undecided": "Zaman çerçevesi esnek"}}, "budget": {"label": "<PERSON><PERSON><PERSON> bütçe aralığınız nedir?", "placeholder": "Bütçe aralığınızı seçin", "required": false, "options": {"below10k": "€10.000'in altında", "10to20k": "€10.000 - €20.000", "20to50k": "€20.000 - €50.000", "above50k": "€50.000+", "notSure": "<PERSON><PERSON><PERSON><PERSON> <PERSON>"}}, "additionalNotes": {"label": "Projeniz hakkında bize anlatın", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>, he<PERSON><PERSON>nızı veya özel gereksinimlerinizi kısaca açıklayın...", "required": false, "optional": "(Opsiyonel)"}}, "ctaButton": "Ücretsiz Keşif Çağrımı Ayırt", "disclaimer": "%100 ücretsiz danışmanlık • Hiçbir yükümlülük yok • Genellikle 15-30 dakika", "alternativeContact": {"text": "<PERSON><PERSON><PERSON><PERSON> sohbet etmeyi mi tercih ediyorsunuz?", "whatsappText": "WhatsApp ile yazın", "emailText": "E-posta g<PERSON>"}, "successMessage": {"title": "İlginiz için <PERSON>ekkürler!", "subtitle": "Bilgilerinizi aldık", "description": "Ekibimiz proje detaylarınızı inceleyecek ve ücretsiz keşif çağrınızı planlamak için 24 saat içinde sizinle iletişime geçecek. Bu arada, vaka çalışmalarımızı ve müşteri referanslarımızı incelemeye çekinmeyin.", "nextSteps": {"title": "Bundan sonra ne ola<PERSON>:", "steps": ["Proje detaylarınızı ve gereksinimlerinizi inceleyeceğiz", "Kurucumuz 24 saat içinde kişisel olarak sizinle iletişime geçecek", "Size uygun bir zamanda 15-30 dakikalık bir keşif çağrısı planlayacağız", "Çağrı sırasında vizyonunuzu keşfedeceğiz ve stratejik rehberlik sağlayacağız"]}, "backToSite": "<PERSON><PERSON>", "viewCaseStudies": "Vaka Çalışmalarını Görüntüle"}}, "packages": {"prototype": {"title": "Prototip", "timeframe": "2-4 hafta", "price": "€4.500", "badge": "Doğrulama", "description": "İlk kullanıcı testleri ve yatırımcı demoları için backend mantığı olmayan tıklanabilir prototip", "cta": "<PERSON><PERSON><PERSON>", "keyFeatures": ["UI/UX Tasarım Atölyeleri", "AI-<PERSON><PERSON><PERSON><PERSON>", "Tıklanabilir Mockuplar", "Yatırımcı hazır Prototip"], "detailedFeatures": ["Etkileşimli tıklanabilir prototipler", "AI desteği ile UX/UI tasarım atölyeleri", "<PERSON><PERSON>m ekranlar için tam tasarımlar", "Özellik önceliklendirme oturumları", "MVP yol haritası hazırlığı", "Yatırımcı sunum <PERSON>i", "Tüm cihazlar için du<PERSON>lı tasarım", "Backend yok - sadece frontend prototip", "Test sonrası geri bildirim entegrasyonu", "Teknik fizibilite değerlendirmesi"]}, "mvp": {"title": "Başlangıç MVP", "timeframe": "6-8 hafta", "price": "€8.500", "badge": "En Popüler Seçim", "description": "İlk lansman i<PERSON>in temel özelliklerle tam uygulama", "cta": "MVP <PERSON>", "keyFeatures": ["Kullanıcı Kimlik Doğrulama", "5+ <PERSON><PERSON>", "Firebase/Supabase Backend", "AI-<PERSON><PERSON><PERSON><PERSON>"], "detailedFeatures": ["Tam Flutter uygulama geliştirme", "Firebase veya Supabase backend kuru<PERSON><PERSON>", "Kullanıcı kimlik doğrulama ve kayıt", "5+ temel uygulama ekranı", "AI-deste<PERSON><PERSON>", "<PERSON><PERSON><PERSON> kod mimarisi", "Duyarlı tasarım", "Temel push bildirimleri", "App Store dağıtım desteği", "30 gün premium destek", "Temel veritabanı entegrasyonu", "Performans optimizasyonu"]}, "professional": {"title": "<PERSON><PERSON><PERSON><PERSON>", "timeframe": "8-12 hafta", "price": "€15.000'den başlayarak", "badge": "Profesyonel", "description": "Ödeme entegrasyonu ve profesyonel mimari ile gelişmiş uygulama", "cta": "Tek<PERSON><PERSON> Al", "keyFeatures": ["10+ <PERSON><PERSON><PERSON>", "Ödeme Entegrasyonu", "<PERSON><PERSON>", "Ölçeklenebilir <PERSON>"], "detailedFeatures": ["Tam Flutter uygulama geliştirme", "Gelişmiş özelliklerle Firebase/Supabase backend", "Ödeme entegrasyonu (Stripe, PayPal, vb.)", "Gelişmiş push bildirimleri", "10+ u<PERSON><PERSON><PERSON><PERSON> e<PERSON>ı", "Ölçeklenebilir bulut mimarisi", "Performans izleme", "Gelişmiş kullanıcı yönetimi", "Veri analitik entegrasyonu", "App Store optimizasyonu", "<PERSON><PERSON> paneli (talep üzerine)", "AI özellikleri (gerektiğinde)", "Kapsamlı test paketi", "60 gün premium destek"]}, "enterprise": {"title": "<PERSON>rumsal Çözüm", "timeframe": "12+ hafta", "price": "Talep Üzerine", "badge": "<PERSON><PERSON><PERSON>", "description": "Özel gereksinimleri olan şirketler için tamamen özelleştirilmiş çözü<PERSON>", "cta": "Danışmanlık Başlat", "keyFeatures": ["Özelleştirilmiş <PERSON>", "Sınırs<PERSON>z <PERSON>", "<PERSON><PERSON><PERSON>", "24/7 Destek Mevcut"], "detailedFeatures": ["Tamamen özelleştirilmiş geliştirme", "<PERSON><PERSON><PERSON> düzeyde backend mimar<PERSON>", "Özel admin paneli ve kontrol paneli", "Bireysel AI entegrasyonu", "Çoklu platform dağıtımı", "Gelişmiş güvenlik özellikleri", "Ölçeklenebilir bulut altyapısı", "Performans izleme ve analitik", "Özel API geliştirme", "Üçüncü taraf entegra<PERSON>ı", "Uyumluluk ve güvenlik denetimleri", "24/7 destek mevcut", "<PERSON><PERSON> gel<PERSON> ekibi", "S<PERSON><PERSON><PERSON> güncellemeler ve bakım"]}, "landingpage": {"title": "İniş Sayfası Geliştirme", "timeframe": "2-4 hafta", "description": "Maks<PERSON>um erişilebilirlik ve performans için en yeni web teknolojileriyle WCAG 2.0 uyumlu iniş sayfaları", "features": ["WCAG 2.0 AA uyumluluğu", "<PERSON><PERSON><PERSON> kullanıcılar için kapsayıcı tasarım", "Ekran ok<PERSON>u uyumluluğu", "Yüksek performans metrikleri", "SEO optimize edilmiş yapı", "Duyarlı tasarım"]}, "architecture": {"title": "<PERSON><PERSON>", "timeframe": "1-2 hafta", "description": "Projenizin başarısı için sağlam bir temel", "features": ["Teknik özellikler", "Sistem mimarisi tasarımı", "Veritabanı şeması", "API belgelendirmesi", "Geliştirme yol haritası"]}, "consulting": {"title": "Teknik Danışmanlık", "timeframe": "<PERSON><PERSON><PERSON><PERSON>", "description": "Teknik kararlarınız için uzman rehberliği", "features": ["Teknoloji yığını önerileri", "<PERSON><PERSON>", "Performans optimizasyonu", "Güvenlik değerlendirmesi", "Ölçeklenebilirlik planlaması", "BAE ödeme ağ geçidi entegrasyonu"]}}, "otherInquiries": {"title": "Farklı bir talebiniz mi var?", "description": "Bir görüşme rezervasyonu yapın, biz <PERSON><PERSON><PERSON>", "bookCall": "Görüşme Re<PERSON>vas<PERSON>u", "whatsappContact": "WhatsApp"}}, "leadCaptureForm": {"headline": "Flutter Yolculuğunuzu 3 Adımda Başlatın", "introduction": "Sadece 2 dakikada ücretsiz keşif görüşmenizi ayırtın", "steps": {"personal": "<PERSON>il<PERSON><PERSON><PERSON><PERSON>", "project": "<PERSON><PERSON>", "final": "<PERSON>"}, "stepDescriptions": {"personal": "Bize kim <PERSON> söyleyin", "project": "Vizyonunuzu pay<PERSON>şın", "final": "<PERSON> r<PERSON>tu<PERSON><PERSON> e<PERSON>in"}, "form": {"fullName": "Ad Soyad", "email": "E-posta Adresi", "company": "Şirket", "mvpGoal": {"label": "<PERSON><PERSON>ncil hedefiniz nedir?", "placeholder": "<PERSON> hedefinizi se<PERSON>", "options": {"prototyping": "Fikrime prototip ile doğrulama yapmak", "mvpLaunch": "<PERSON>'<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "saasScaling": "Mevcut SaaS ürünümü ölçeklendirmek", "aiIntegration": "Uygulamamıza AI özellikleri eklemek", "consulting": "Teknik rehberlik ve strateji almak", "notSure": "Hen<PERSON>z emin <PERSON> - rehberlik gerekiyor"}}, "timeline": {"label": "Zaman Çizelgesi", "placeholder": "<PERSON>e zaman ba<PERSON>lam<PERSON>?", "options": {"asap": "Mümkün olan en kısa zamanda", "fourToSix": "4-6 hafta içinde", "threeMonths": "2-3 ay içinde", "undecided": "Zaman çizelgesi esnek"}}, "budget": {"label": "Bütçe", "placeholder": "Bütçe aralığı", "options": {"below10k": "€10.000'in altında", "10to20k": "€10.000 - €20.000", "20to50k": "€20.000 - €50.000", "above50k": "€50.000+", "notSure": "<PERSON><PERSON><PERSON><PERSON> <PERSON>"}}, "additionalNotes": {"label": "Projeniz hakkında bize anlatın", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>, he<PERSON><PERSON> veya özel gereksinimlerinizi kısaca açıklayın..."}, "whatHappensNext": {"title": "<PERSON>undan sonra ne olur?", "steps": ["Projenizi 24 saat içinde inceleyeceğiz", "15-30 dakikalık keşif görüşmesi planlayacağız", "MVP'niz <PERSON><PERSON><PERSON> k<PERSON>tirilmiş rehberlik alacaksınız"]}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON>", "emailRequired": "E-posta gere<PERSON>li", "emailInvalid": "Lütfen geçerli bir e-posta adresi girin", "goalRequired": "Lütfen birincil hedefinizi seçin", "timelineRequired": "Lütfen tercih ettiğiniz zaman çizelgesini seçin"}}, "navigation": {"back": "← <PERSON>eri", "next": "İleri →", "submit": "<PERSON><PERSON><PERSON>mesi Ayırt", "submitting": "Gönderiliyor...", "stepOf": "<PERSON><PERSON><PERSON>"}, "successMessage": {"title": "Teşekkürler!", "description": "Ücretsiz keşif görüşmenizi planlamak için 24 saat içinde sizinle iletişime geçeceğiz.", "scheduleCall": "Şimdi Görüşme Planlayın", "whatsapp": "WhatsApp", "backToSite": "← Ana Sayfaya Dön", "nextSteps": {"title": "<PERSON><PERSON>n sonra ne olur:", "steps": ["Proje <PERSON>rınızı inceleyin", "24 saat içinde kişisel iletişim", "15-30 dakikalık keşif görüşmesi planlayın", "Vizyonunuzu keşfedin ve rehberlik sağlayın"]}}, "footer": {"preferDirectContact": "<PERSON><PERSON><PERSON><PERSON> iletişimi mi tercih ediyorsunuz?", "whatsapp": "WhatsApp", "email": "E-posta"}}, "showLess": "<PERSON><PERSON> a<PERSON> g<PERSON>", "moreFeatures": "<PERSON><PERSON> fazla <PERSON>", "solutionsPortfolio": {"title": "Müşteri Başarı Hikayeleri", "subtitle": "Sektör Liderlerinin Güvendiği", "description": "Gerçek müşterilerden gerçek sonuçlar", "trustMetrics": {"title": "<PERSON><PERSON>lar <PERSON>", "experience": "8+", "experienceLabel": "Yıl Flutter Odağı", "successRate": "98%", "successRateLabel": "MVP Başarı Oranı", "fundingSuccess": "15+", "fundingSuccessLabel": "Finanse Edilen <PERSON>"}, "technicalCapabilities": {"title": "Teknik Mükemmellik", "flutterMastery": {"title": "Flutter Ustalığı", "description": "5+ yıl sadece üretim Flutter uygulamaları geliştirme. <PERSON><PERSON> kod tabanı, her iki platformda da yerli performans.", "metrics": ["Yerli geliştirmeye karşı %40 daha hızlı", "Tek kod tabanı, iki platform", "<PERSON><PERSON><PERSON> garan<PERSON>i"]}, "aiIntegration": {"title": "AI<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "MVP'nizi ilk günden AI yetenekleriyle geleceğe hazırlayın. OpenAI, <PERSON> ve <PERSON> modelleri.", "metrics": ["OpenAI ve Claude entegrasyonu", "Gerçek zamanlı AI işleme", "Ölçeklenebilir ML mimarisi"]}, "mvpDelivery": {"title": "Hızlı MVP Teslimatı", "description": "4-6 haftada üretime hazır MVP'ler. Firebase backend, kim<PERSON>, ödemeler - başlatmak için ihtiyacınız olan her <PERSON>.", "metrics": ["4-6 hafta teslimat", "Üretime hazır kod", "Tam başlatma yığını"]}}, "clientCaseStudies": {"lufthansa": {"title": "Lufthansa", "industry": "Havacılık", "projectType": "Kurumsal Flutter Uygulaması", "description": "Flutter teknolojisi kullanarak kritik seyahat yönetim platformu geliştirme", "businessContext": "Avrupa'nın en büyük havayolu grubu için kurumsal düzeyde mobil mimari geliştirmeye katkı", "results": ["Teknik borcu ortadan kaldırdı ve özellik yol haritasını ileriye taşıdı", "Çapraz platform mimarisi uyguladı", "Performans optimizasyonları gerçekleştirdi"]}, "unionInvestment": {"title": "Union Investment", "industry": "Finansal Hizmetler", "projectType": "Run This Place App", "description": "Run This Place uygulaması geliştirme - otopark ve işyeri yönetimi için rezervasyon sistemi", "businessContext": "Modern Flutter teknolojileri kullanarak otopark alanları ve işyerleri için ye<PERSON>likçi rezervasyon sistemi gel<PERSON>ştirme", "results": ["Otopark rezervasyon sistemi uygulandı", "İşyeri yönetim özellikleri geliştirildi", "Kullanıcı dostu mobil uygulama oluşturuldu"]}, "togg": {"title": "<PERSON><PERSON>", "industry": "Otomotiv", "projectType": "IoT Flutter Uygulaması", "description": "Elektrikli araçlar için akıllı araç kontrol sistemi geliştirme", "businessContext": "Türkiye'nin ilk elektrikli araç projesi için mobil uygulama geliştirmeye katkı", "results": ["IoT araç kontrolü uygulandı", "Gerçek zamanlı iletişim geliştirildi", "Kullanıcı dostu araç uygulaması oluşturuldu"]}}, "cta": {"title": "MVP'nizi <PERSON>maya Hazır mısınız?", "subtitle": "Pazar girişi için Flutter'ı seçen 15+ startup'a katılın", "primaryButton": "<PERSON><PERSON><PERSON><PERSON>i Rezervasyonu", "secondaryButton": "Süreci<PERSON><PERSON>", "guarantee": "4-6 hafta teslimat garantisi veya paranızı geri alın"}}, "portfolio": {"title": "Uzmanlıklarımız", "subtitle": "Sektörler ve Çözüm Sunduğumuz Problemler", "all": "<PERSON><PERSON><PERSON>", "screenshot": "Ekran Görüntüsü", "screenshots": "Ekran Görüntüleri", "problemsWeSolve": "Çözdüğümüz Problemler", "noSectorsFound": "Seçilen filtre için sektör bulunamadı.", "labels": {"technologies": "Tek<PERSON>lojiler", "keyFeatures": "<PERSON><PERSON>"}, "projects": [{"id": "logistics-platform", "title": "Kurumsal Lojistik Platformu", "description": "Tedarik zincirlerinin gerçek zamanlı izlenmesi ve yönetimi için B2B platform geliştirimi. Mevcut ERP sistemleri ile entegrasyon ve otomatik raporlama dahil.", "category": "Business", "technologies": ["Flutter", "Google Cloud", "REST API", "ERP Integration"], "features": ["Gerçek Zamanlı İzleme ve Analitik", "Otomatik Raporlama", "ERP Sistem Entegrasyonu", "Rol Tabanlı Erişim Yönetimi"]}, {"id": "detoxme", "title": "DetoxMe - Dijital Sağlık", "description": "Dijital detoks ve sağlıklı akıllı telefon alışkanlıklarını teşvik eden uygulama.", "category": "Lifestyle", "technologies": ["Flutter", "Firebase", "Analytics", "Push Notifications"], "features": ["Ekran Süresi <PERSON>me", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Meditasyon", "İlerleme <PERSON>"]}, {"id": "reserv", "title": "Reserv - Restoran <PERSON>", "description": "Gerçek zamanlı müsaitlik ile restoranlar için modern rezervasyon platformu.", "category": "Business", "technologies": ["Flutter", "Firebase", "Payment APIs", "Real-time DB"], "features": ["Anında <PERSON>", "Ödeme Entegrasyonu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, {"id": "togg", "title": "TOGG - Bağlı Araç Deneyimi", "description": "Uzaktan kontrol ve akıllı özellikler ile TOGG elektrikli araçları için resmi uygulama.", "category": "Automotive", "technologies": ["Flutter", "IoT", "Cloud Services", "Real-time Communication"], "features": ["Uzaktan Kontrol", "<PERSON><PERSON><PERSON>", "Navigasyon", "<PERSON><PERSON>"]}], "categories": {"aiAssistant": "Yapay Zeka Asistanı", "foodDelivery": "Yemek Teslimatı", "hospitality": "<PERSON><PERSON><PERSON><PERSON>", "business": "İş", "social": "<PERSON><PERSON><PERSON>", "automotive": "Otomotiv"}, "sectors": {"assistant": "Asistan Uygulamaları", "food": "Yemek Siparişi ve Teslimatı", "hospitality": "<PERSON><PERSON><PERSON><PERSON>", "lifestyle": "Yaşam Tarzı Uygulamaları", "social": "<PERSON><PERSON><PERSON>", "automotive": "Otomotiv", "medical": "Tıp ve Sağlık Hizmetleri", "business": "İş Çözümleri"}, "sectorDescriptions": {"assistant": "Verimliliği artıran ve kişiselleştirilmiş öneriler sunan yapay zeka destekli asistanlar", "food": "Gerçek zamanlı takip özelliğine sahip kesintisiz yemek sipariş ve teslimat platformları", "hospitality": "<PERSON><PERSON><PERSON><PERSON> deneyimini geliştirmek için otel ve konaklama işletmelerine yönelik dijital çözümler", "lifestyle": "Günlük yaşamı, sağlığı ve kişisel gelişimi iyileştiren uygulamalar", "social": "İnsanları ve toplulukları ortak ilgi alanları üzerinden birbirine bağlayan platformlar", "automotive": "<PERSON><PERSON>, navigasyon ve sürücü asistanı için akıllı çözümler", "medical": "<PERSON>ta bakımını ve tıbbi operasyonları iyileştiren dijital sağlık çözümleri", "business": "İşlemleri kolaylaştıran ve üretkenliği artıran kurumsal uygulamalar"}, "problems": {"assistant": {"1": "<PERSON><PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON>"}, "food": {"1": "Sipariş yönetimi", "2": "Teslimat lojistiği", "3": "Restoran keşfi"}, "hospitality": {"1": "<PERSON><PERSON><PERSON><PERSON>", "2": "Hizmet optimizasyonu", "3": "Rezervasyon sistemleri"}, "lifestyle": {"1": "Sağlık takibi", "2": "Alışkanlık oluşturma", "3": "<PERSON><PERSON><PERSON><PERSON> organiza<PERSON>"}, "social": {"1": "Kullanıcı katılımı", "2": "İçerik keşfi", "3": "Topluluk oluşturma"}, "automotive": {"1": "<PERSON><PERSON>", "2": "Navigasyon optimizasyonu", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "medical": {"1": "<PERSON><PERSON>", "2": "Sağlık izleme", "3": "<PERSON><PERSON><PERSON><PERSON> kayıt <PERSON>"}, "business": {"1": "İş akışı optimizasyonu", "2": "<PERSON><PERSON>", "3": "Takım işbirliği"}}, "viewDetails": "Detayları Görüntüle", "viewAllProjects": "<PERSON><PERSON><PERSON> Projeleri Gö<PERSON>"}, "clients": {"title": "Müşterilerimiz", "subtitle": "Birlikte Çalıştığımız Şirketler", "visitWebsite": "Web Sitesini Ziyaret <PERSON>t"}, "testimonials": {"title": "Müşterilerimiz Ne Diyor", "subtitle": "Sektör Uzmanlarından Gerçek Deneyimler", "badge": "Müşteri Başarı Hikayeleri", "readMore": "<PERSON>", "readLess": "<PERSON><PERSON>", "testimonials": [{"quote": "Gü<PERSON><PERSON>r ve sistematik! <PERSON> son derece güvenilir ve sempatik bir Mobil Uygulama Geliştirici, hem teknik bileşenlerde hem de kullanıcı deneyiminde herhangi bir eksiklik bırakmıyor!", "name": "<PERSON>in <PERSON>.", "designation": "Ultimind CEO'su", "src": "/images/testimonials/emin.jpeg", "company": "<PERSON><PERSON><PERSON><PERSON>", "companyLogo": "/images/companies/lumeus.png", "industry": "Teknoloji", "rating": 5, "projectType": "<PERSON><PERSON>", "deliveryTime": "Zaman<PERSON><PERSON>", "badge": "Doğrulanmış Müşteri", "results": ["Mükemmel Kullanıcı Deneyimi", "Sistematik Uygulama", "<PERSON>ç Eksik Yok"]}, {"quote": "Viktor'u bir projede çok yetkin bir Mobil Geliştirici olarak tanıdım ve takdir ettim. Hızlı kavrayış gücü ve problem çözmedeki yüksek katılımı dikkat çekici. Onun sayesinde ekip performans, dayanışma ve genel ekip gelişimi açısından muazzam bir büyüme gösterdi.", "name": "<PERSON><PERSON>.", "designation": "Union Investment Real Estate GmbH Proje Yöneticisi", "src": "/images/testimonials/stefanie.jpeg", "company": "Union Investment", "companyLogo": "/images/companies/union-investment.png", "industry": "Gayrimenkul", "rating": 5, "projectType": "Ekip İş Birliği", "deliveryTime": "Beklentileri Aştı", "badge": "<PERSON><PERSON><PERSON>", "results": ["Ekip Performansı Artırıldı", "<PERSON><PERSON> <PERSON>", "Yetkin Problem Çözme"]}, {"quote": "Mobil Uygulama projesinde yaşadığımız inanılmaz iş birliği için minnettarım. Frontend geliştirmedeki uzmanlığı gerçekten çok değerliydi ve birlikte elde ettiğimiz mükemmel sonuçlardan çok mutluyum.", "name": "<PERSON>", "designation": "Togg Ürün UX Tasarımcısı", "src": "/images/testimonials/mohammed.jpeg", "company": "<PERSON><PERSON>", "companyLogo": "/images/companies/togg.png", "industry": "Otomotiv", "rating": 5, "projectType": "UI/UX Geliştirme", "deliveryTime": "Olağanüstü Kalite", "badge": "Premium Ortak", "results": ["Paha Biçilemez Frontend Uzmanlığı", "<PERSON><PERSON><PERSON><PERSON><PERSON>ç<PERSON>", "Kusursuz İş Birliği"]}, {"quote": "Gayrimenkul sektöründe bir girişimci olarak portal, web sitesi ve uygulama tabanlı çözümlerde profesyonel destek arıyordum. <PERSON> bana karmaşık ilişkileri basitçe açıklayabildi ve hedeflerimi hemen anladı.", "name": "<PERSON>.", "designation": "Walenwein Immobilien Global Gayrimenkul Uzmanı", "src": "/images/testimonials/natalia.jpeg", "company": "Lufthansa", "companyLogo": "/images/companies/lufthansa.png", "industry": "Havacılık", "rating": 5, "projectType": "Full-<PERSON><PERSON>", "deliveryTime": "Mü<PERSON><PERSON>l Zaman Çizelgesi", "badge": "Uzun Vadeli <PERSON>", "results": ["Karmaşık Konular Basitçe Açıklandı", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"quote": "Fantastik çalışma! Uygulama tam olarak hayal ettiğimiz gibi çalışıyor. <PERSON> çok profesyonel ve tüm tarihlere uyuyor.", "name": "<PERSON><PERSON>", "designation": "<PERSON><PERSON>", "src": "/images/testimonials/placeholder-male.svg", "company": "<PERSON><PERSON>", "companyLogo": null, "industry": "İş", "rating": 5, "projectType": "İş Uygulaması", "deliveryTime": "Zamanında", "badge": "<PERSON><PERSON><PERSON><PERSON>", "results": ["Mükemmel Çalışıyor", "Profesyonel Çalışma", "<PERSON><PERSON><PERSON>"], "isAnonymous": true}, {"quote": "Mükemmel iletişim ve teknik beceriler. Viktor startup'ımız için tam olarak ihtiyacımız olanı teslim etti. Kesinlikle tavsiye ederim!", "name": "<PERSON><PERSON>", "designation": "Startup <PERSON>", "src": "/images/testimonials/placeholder-female.svg", "company": "Teknoloji Startup'ı", "companyLogo": null, "industry": "Teknoloji", "rating": 5, "projectType": "MVP <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deliveryTime": "<PERSON><PERSON>", "badge": "Uluslararası Müşteri", "results": ["Mükemmel İletişim", "Kusursuz Teknik Uygulama", "Startup'a Uygun <PERSON>ü<PERSON>"], "isAnonymous": true}, {"quote": "Sağlık uygulamamızın geliştirilmesinden çok memnunum. Viktor gereksinimlerimizi mükemmel şekilde uyguladı ve sorular için her zaman ulaşılabilirdi.", "name": "<PERSON><PERSON> <PERSON><PERSON>", "designation": "<PERSON><PERSON><PERSON><PERSON>", "src": "/images/testimonials/placeholder-doctor.svg", "company": "<PERSON><PERSON><PERSON><PERSON>", "companyLogo": null, "industry": "Sağlık", "rating": 5, "projectType": "Sağlık Uygulaması", "deliveryTime": "Söz Verildiği Gibi", "badge": "<PERSON><PERSON><PERSON>", "results": ["Mükemmel Uygulama", "Her Zaman Ulaşılabilir", "Sağlık Uygulaması Uzmanlığı"], "isAnonymous": true}], "stats": {"satisfiedCustomers": "<PERSON><PERSON><PERSON><PERSON>", "averageRating": "Ortalama <PERSON>", "successfulApps": "Başarılı Uygulamalar"}}, "aiEnhanced": {"badge": "Teknoloji Liderliği", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>: AI-Enhanced Coding", "subtitle": "En son teknoloji AI araçları, kanıtlanmış geliştirme yöntemleriyle birleşerek üstün iş sonuçları sağlar.", "businessBenefits": [{"title": "Daha Hızlı Pazara Çıkış", "description": "AI, rekabet avantajı ve erken gelir için geliştirmeyi hızlandırır.", "icon": "rocket"}, {"title": "Yüksek Güvenilirlik ve Kullanıcı Memnuniyeti", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> analiz, stabil uygulamalar için hataları minimize eder.", "icon": "shield"}, {"title": "Gelecek Odaklı ve Ölçeklenebilir Çözümler", "description": "İşletmeyle birlikte büyüyen temiz kod tabanı, yatırımı korur.", "icon": "star"}], "processTitle": "Maksimum Verimlilik ve Kalite için <PERSON>ü<PERSON>z", "aiFeatures": [{"title": "Otomatik Kod Üretimi", "description": "Daha hızlı uygulama için AI destekli geliştirme", "icon": "bot"}, {"title": "Akıllı Hata Kontrolü", "description": "Her commit ile otomatik kalite kontrolleri", "icon": "check"}, {"title": "Akıllı Test", "description": "Kritik senaryolar için AI üretimi test vakaları", "icon": "search"}, {"title": "Performans Optimizasyonu", "description": "AI kritik kod yollarını analiz eder ve optimize eder", "icon": "gauge"}, {"title": "Otomatik Dokümantasyon", "description": "AI kapsamlı dokümantasyon ve yorumlar üretir", "icon": "text"}, {"title": "Akıllı Refaktoring", "description": "AI kod iyileştirmeleri önerir ve uygular", "icon": "refresh"}, {"title": "Bağlam Farkında Tamamlama", "description": "<PERSON>je bağlamına dayalı akıllı kod tamamlama", "icon": "brain"}, {"title": "<PERSON><PERSON><PERSON>", "description": "Po<PERSON><PERSON><PERSON><PERSON> so<PERSON>ın tahmini ve önlenmesi", "icon": "zap"}]}, "contact": {"title": "İletişim", "subtitle": "Geleceği şekillendirmeye başlayalım", "description": "<PERSON><PERSON><PERSON> bir soru, proje tartışması veya danışmanlık randevusu için bizimle iletişime geçin. Dijital vizyonunuzu hayata geçirmenize yardımcı olmak için buradayız.", "name": "İsim", "email": "E-posta", "phone": "Telefon", "message": "<PERSON><PERSON>", "send": "<PERSON><PERSON>", "yourName": "Adınız", "yourEmail": "E-posta adresiniz", "subject": "<PERSON><PERSON>", "howCanIHelp": "Nasıl yardımcı olabilirim?", "yourMessageHere": "Mesajınızı buraya yazın", "getInTouch": "İletişime Geçin", "sendMessage": "<PERSON><PERSON>", "schedule": "Görüşme <PERSON>", "freeConsultation": "Ücretsiz 15 dakikalık danışmanlık randevusu alın", "location": "<PERSON><PERSON>", "submitButton": "<PERSON><PERSON><PERSON>", "sending": "Gönderiliyor...", "messageSent": "Mesaj Gönderildi!", "errorTryAgain": "<PERSON><PERSON>, lütfen tekrar deneyin", "tryAgain": "<PERSON><PERSON><PERSON>", "orSchedule": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> takvim bağlantısını kullanarak toplantı planlayın", "formDescription": "Aşağıdaki formu doldurun, kısa süre içinde size geri <PERSON>", "yourRequest": "<PERSON><PERSON><PERSON>", "company": "Şirket", "yourCompanyName": "Şirket Adınız", "timeline": "Zaman Çizelgesi", "selectTimeline": "Zaman çizelgesi seçin...", "asap": "En kısa sürede", "oneToThreeMonths": "1-3 Ay", "threeToSixMonths": "3-6 <PERSON><PERSON>", "flexible": "Esnek", "estimatedBudget": "<PERSON><PERSON><PERSON>", "selectBudgetRange": "Bütçe aralığı seçin...", "below5k": "€5,000'nin altında - Küçük projeler", "fiveToFifteenK": "€5,000 - €15,000 - <PERSON><PERSON> pro<PERSON>", "fifteenToThirtyK": "€15,000 - €30,000 - Büyük projeler", "above30k": "€30,000+ - <PERSON><PERSON><PERSON> projeler", "notSure": "<PERSON><PERSON><PERSON>z <PERSON> - Konuşalım", "projectDescription": "<PERSON>je <PERSON>", "projectPlaceholder": "<PERSON><PERSON>, hedeflerinizi ve gereksinimlerinizi kısaca açıklayın...", "services": {"prototype": "📱 Hızlı Prototip - €3,000'den başlayarak", "mvp": "🚀 MVP <PERSON><PERSON><PERSON> - €7,500'den başlayarak", "saasGrowth": "📈 SaaS Büyüme Paketi - €15,000'den başlayarak", "custom": "🎯 Özel Talep - Danışmanlık ile"}, "serviceLabels": {"selectService": "Bir hizmet seçin...", "prototype": "Hızlı Prototip", "mvp": "MVP <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saasGrowth": "SaaS Büyüme Paketi", "custom": "<PERSON><PERSON>"}}, "footer": {"copyright": "© 2025 Innovatio. Tüm hakları saklıdır.", "description": "Yenilikçi teknoloji aracılığıyla işletmeleri dönüştüren son teknoloji mobil ve web uygulamaları geliştiriyoruz.", "quickLinks": "Hızlı Bağlantılar", "footerContact": "İletişim", "legal": "<PERSON><PERSON>", "newsletter": "<PERSON><PERSON><PERSON>", "newsletterDesc": "Güncellemeler ve içgörüler almak için bültenimize abone olun.", "emailPlaceholder": "E-posta adresinizi girin", "subscribe": "<PERSON><PERSON>", "builtWith": "<PERSON><PERSON>ıştır", "and": "ve", "downloadCV": "Özgeçmişim", "englishCV": "İngilizce", "germanCV": "Almanca"}, "cookies": {"title": "Çerez İzni", "description": "Gezinme deneyiminizi <PERSON>, kişiselleştirilmiş reklamlar veya içerik sunmak ve trafiğimizi analiz etmek için çerezleri kullanıyoruz. \"Tümünü Kabul Et\" düğmesine tıklayarak çerezleri kullanmamıza izin vermiş olursunuz.", "acceptAll": "Tümünü Kabul Et", "decline": "<PERSON><PERSON>", "customize": "Ö<PERSON>leş<PERSON>r", "necessary": "Gerek<PERSON>", "necessaryDesc": "<PERSON><PERSON> çerezler, web sitesinin düzgün çalışması için gereklidir ve devre dışı bırakılamaz.", "analytics": "<PERSON><PERSON><PERSON>zle<PERSON>", "analyticsDesc": "<PERSON><PERSON>zle<PERSON>, ziyaretçilerin web sitemizle nasıl etkileşimde bulunduğunu anlamamıza ve hizmetlerimizi geliştirmemize yardımcı olur.", "marketing": "<PERSON><PERSON><PERSON><PERSON>", "marketingDesc": "<PERSON><PERSON>, il<PERSON><PERSON> reklamları göstermek için ziyaretçileri web siteleri arasında takip etmek için kullanılır.", "functional": "İşlevsel Çerezler", "functionalDesc": "<PERSON><PERSON>, web sitemizde gelişmiş işlevsellik ve kişiselleştirme sağlar.", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "Çerez Ayarları", "close": "Ka<PERSON><PERSON>", "cookiePolicy": "Çerez Politikası", "privacyPolicy": "Gizlilik Politikası"}, "heroParallax": {"title": "En İyi Geliştirme Stüdyosu", "subtitle": "En son teknolojiler ve çerçevelerle güzel ürünler oluşturuyoruz. Harika ürünler oluşturmayı seven tutkulu geliştiriciler ve tasarımcılardan oluşan bir ekibiz.", "products": {"mobileApp": "<PERSON><PERSON>", "webDev": "Web Geliştirme", "uiux": "UI/UX Tasarımı", "ecommerce": "<PERSON><PERSON>t<PERSON><PERSON>", "ai": "Yapay Zeka Entegrasyonu", "cloud": "Bulut Çözümleri", "devops": "DevOps", "dataAnalytics": "<PERSON><PERSON>", "blockchain": "Blockchain <PERSON>", "arvr": "AR/VR Çözümleri", "customSoftware": "<PERSON>zel <PERSON>ı<PERSON>", "mobileGame": "<PERSON><PERSON>", "iot": "IoT Çözümleri", "api": "API Geliştirme", "cybersecurity": "Siber Güvenlik"}}, "featuresSection": {"features": [{"title": "Geliştiriciler için ta<PERSON>ı", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve yapanlar için tasarlandı.", "icon": "IconTerminal2"}, {"title": "Kullanım kolaylığı", "description": "Bir <PERSON> kullanmak kadar kolay ve bir tane almak kadar pahalı.", "icon": "IconEaseInOut"}, {"title": "<PERSON><PERSON>", "description": "Fiyatlarımız piyasadaki en iyisidir. Üst sınır yok, kilitlenme yok, kredi kartı gerekmez.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "%100 Çalışma süresi garantisi", "description": "Kimse bizi alaşağı edemez.", "icon": "IconCloud"}, {"title": "Çok kiracılı mimari", "description": "Yeni koltuklar almak yerine şifreleri paylaşabilirsiniz.", "icon": "IconRouteAltLeft"}, {"title": "7/24 Müşteri Desteği", "description": "Zamanın %100'ünde hizmetinizdeyiz. En azından yapay zeka temsilcilerimiz.", "icon": "IconHelp"}, {"title": "Para iade garantisi", "description": "<PERSON><PERSON><PERSON> EveryAI'y<PERSON>, sizi bizi beğenmeniz için ikna edeceğiz.", "icon": "IconAdjustmentsBolt"}, {"title": "<PERSON><PERSON> di<PERSON> her <PERSON>ey", "description": "Metin fikirlerim tükendi. İçten özürlerimi kabul edin.", "icon": "IconHeart"}]}, "seo": {"meta": {"home": {"title": "Mobil Uygulama Geliştirme ve Dijital Çözümler | Innovatio-Pro", "description": "Flutter ile profesyonel mobil uygulama geliştirme, yapay zeka entegrasyonu ve dijital çözümler. Innovatio-Pro'dan MVP geli<PERSON>e, prototipleme ve full-stack geliştirme.", "keywords": "mobil <PERSON><PERSON> gel<PERSON>, <PERSON><PERSON><PERSON>, MVP gel<PERSON>, ya<PERSON><PERSON> <PERSON> ente<PERSON>, di<PERSON><PERSON>, proto<PERSON><PERSON><PERSON>, full-stack gel<PERSON><PERSON><PERSON><PERSON><PERSON>, Innovatio-Pro"}, "templates": {"title": "Mobil Uygulama Çözümleri ve Yapay Zeka Entegrasyonu - Innovatio-Pro", "description": "<PERSON>bil uygulama çözümlerimizi, yapay zeka entegrasyonu ve dijital platformları keşfedin. <PERSON><PERSON><PERSON>, MVP geliştirm<PERSON> ve yenilikçi mobil teknolojilerde uzmanlaşmış.", "keywords": "mobil uygu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, MVP gel<PERSON>, di<PERSON><PERSON>, mobil <PERSON>, u<PERSON><PERSON><PERSON><PERSON> gel<PERSON>e"}, "services": {"title": "<PERSON><PERSON>a Geliştirme Hizmetleri - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ve Dijital Çözümler", "description": "<PERSON><PERSON><PERSON>, yapay zeka entegrasyonu ve modern teknolojiler kullanarak profesyonel mobil uygulama geliştirme hizmetleri. MVP, prototipleme ve full-stack mobil çözümlerde uzmanlaşmış.", "keywords": "mobil <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ya<PERSON><PERSON> ente<PERSON>, mobil <PERSON>, u<PERSON><PERSON><PERSON>a gel<PERSON> hi<PERSON>, di<PERSON><PERSON>"}, "about": {"title": "Innovatio-Pro Hakkında - Mobil Uygulama Geliştirme ve Yapay Zeka Uzmanı", "description": "Flutter ile mobil uygulama geliştirme, yapay zeka entegrasyonu ve yenilikçi dijital çözümler uzmanınız Innovatio-Pro hakkında bilgi edinin.", "keywords": "Innovatio-Pro, mobil uygulama gel<PERSON>isi, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON> zeka entegra<PERSON>u, di<PERSON><PERSON>, uygulama geliştirme şirketi"}, "contact": {"title": "İletişim - Mobil Uygulama Geliştirme ve Dijital Çözümler | Innovatio-Pro", "description": "Profesyonel mobil uygulama geliştirme, yapay zeka entegrasyonu ve dijital çözümler için Innovatio-Pro ile iletişime geçin. Bir sonraki projeniz için ücretsiz danışmanlık mevcut.", "keywords": "mobil uygulama geliştirme il<PERSON>im, <PERSON><PERSON><PERSON> gelişti<PERSON><PERSON>, yapay zeka entegrasyonu hizmetleri, dijital <PERSON>özümler talebi, Innovatio-Pro iletişim"}, "pricing": {"title": "Mobil <PERSON>a Geliştirme <PERSON> - MVP, Prototipleme ve Yapay Zeka Çözümleri", "description": "<PERSON><PERSON>, MVP <PERSON>, prototipleme ve yapay zeka entegrasyonu için şeffaf fiyatlandırma. Hızlı prototiplerden full-stack mobil çözümlere kadar.", "keywords": "mobil uygulama geli<PERSON><PERSON><PERSON><PERSON>, MVP gel<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> uygu<PERSON> fiya<PERSON>arı, yapay zeka entegrasyonu oranları, prototipleme fiyatları, uygulama geliştirme teklifleri"}, "faq": {"title": "SSS - Mobil Uygulama Geliştirme ve Dijital Çözümler | Innovatio-Pro", "description": "<PERSON><PERSON> uygulama gel<PERSON>e <PERSON>, <PERSON><PERSON><PERSON> gel<PERSON>, yapay zeka entegrasyonu ve dijital çözümler hakkında sık sorulan sorular. Innovatio-Pro'dan destek ve cevaplar.", "keywords": "mobil uygulama geliştirme SSS, Flutter geliştirme soruları, yapay zeka entegrasyonu yardımı, dijital çözümler desteği, uygulama geliştirme cevapları"}}, "openGraph": {"siteName": "Innovatio-Pro - Mobil Uygulama Geliştirme ve Dijital Çözümler", "defaultImage": "/images/og-innovatio-pro-tr.jpg", "defaultImageAlt": "Innovatio-Pro - Flutter ile Mobil Uygulama Geliştirme ve Yapay Zeka Entegrasyonu"}, "jsonLd": {"organization": {"name": "Innovatio-Pro", "alternateName": "Innovatio-Pro Mobil Geliştirme", "description": "Flutter ile mobil uygulama geliştirme, yapay zeka entegrasyonu ve modern işletmeler için yenilikçi dijital çözümler uzmanı.", "url": "https://innovatio-pro.com", "telephone": "+49-175-9918357", "email": "<EMAIL>", "address": {"streetAddress": "Remote Office", "addressLocality": "Wyoming", "addressRegion": "Wyoming", "postalCode": "82001", "addressCountry": "US"}, "sameAs": ["https://github.com/innovatio-pro", "https://linkedin.com/company/innovatio-pro"]}}}, "blog": {"title": "Teknoloji İçgörüleri", "description": "Mobil geliştirmedeki en son trendleri, içgörüleri ve yenilikleri keşfedin. Flutter en iyi uygulamalarından yapay zeka entegrasyon stratejilerine - teknoloji devriminde öncü kalın.", "categoriesTitle": "<PERSON><PERSON><PERSON>", "categoriesDescription": "Farklı teknoloji alanlarındaki makaleleri keşfedin", "subscriptionTitle": "Bilgili <PERSON>", "subscriptionDescription": "En son F<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yapay zeka trendleri ve teknoloji yeniliklerini doğrudan gelen kutunuza alın.", "readMore": "Devamını Oku", "noArticles": "Makale bulunamadı", "author": "<PERSON><PERSON>", "readingTime": "<PERSON><PERSON>ü<PERSON>", "views": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tags": "<PERSON><PERSON><PERSON><PERSON>", "floatingButton": {"tooltip": "Teknoloji Blogu", "currentArticle": "<PERSON><PERSON><PERSON><PERSON>", "viewAll": "<PERSON><PERSON><PERSON>", "newPosts": "<PERSON><PERSON>"}, "categories": {"all": "<PERSON><PERSON><PERSON>", "company": "Şirket Hakkında", "flutter": "Flutter Geliştirme", "mobile": "<PERSON><PERSON>", "ai": "Yapay Zeka Entegrasyonu", "performance": "Performans", "caseStudies": "Vaka Çalışmaları", "trends": "<PERSON><PERSON><PERSON><PERSON>"}}}