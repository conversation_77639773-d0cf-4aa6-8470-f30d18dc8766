{"nav": {"home": "Главная", "about": "О нас", "solutions": "Решения", "process": "Процесс", "services": "Услуги", "techStack": "Технологии", "portfolio": "Портфолио", "pricing": "Цены", "contact": "Контакты", "usp": "Почему мы другие", "aienhanced": "ИИ-интеграция", "chatWithUs": "Чат с нами", "bookConsultation": "Записаться на консультацию", "apps": "Приложения", "clients": "Клиенты", "testimonials": "Отзывы", "blog": "Блог", "aipowereddevelopment": "ИИ-разработка", "kundenerfolgsgeschichten": "Истории успеха клиентов", "developmentprocess": "Процесс разработки"}, "hero": {"title": "Приложения, которые трансформируют ваш", "subtitle": "Бизнес", "painPoint": {"mobile": {"line1": "Знакомо? Нет результатов, много встреч?", "line2": "Мы слушаем, Мы решаем, мы доставляем, мы вдохновляем!"}, "desktop": {"trigger": "Знакомо?", "problems": "Хаос • Бесконечные встречи • Мало результатов • Перегрузка", "solution": "Быстро. Надежно. Просто. Без забот.", "mainMessage": {"text": "Мы слушаем, Мы решаем, мы доставляем, мы вдохновляем!", "parts": ["Мы слушаем", "Мы решаем", "мы доставляем", "мы вдохновляем!"]}}}, "tagline": "Специализированная Flutter-разработка • AI-готовая архитектура • Firebase Backend", "description": "Пока конкуренты обещают золотые горы, мы создаем работающие MVP. Проверено 15+ стартапами, которым приложения нужны были вчера, а не в следующем квартале.", "typing": {"sequence1": "Flutter MVP → App Store → Пользовательский фидбек ✓", "sequence2": "Быстрая поставка. Готов к продакшену. Без драмы.", "sequence3": "От SaaS MVP до успеха Series A."}, "cta": {"consultation": "Запланировать стратегическую консультацию", "consultationSubtext": "Необязательный первичный звонок • 30 минут • Бесплатно"}, "metrics": {"development": {"label": "Быстрая разработка", "value": "40%", "description": "быстрее разработка", "howTitle": "Как:", "howExplanation": "Использование новейших IDE с поддержкой ИИ, автоматизированной генерацией кода и интеллектуальным автодополнением."}, "timeToMarket": {"label": "Быстрый выход на рынок", "value": "50%", "description": "быстрее выход на рынок", "howTitle": "Как:", "howExplanation": "Fast lane CI/CD конвейер, автоматизированное тестирование, проверка кода с помощью ИИ и оптимизированный процесс развертывания."}, "costSaving": {"label": "Экономия затрат", "value": "30%", "description": "снижение затрат", "howTitle": "Как:", "howExplanation": "Использование Flutter для кросс-платформенной разработки, оптимизированные облачные ресурсы и эффективные методы разработки."}, "lessBugs": {"label": "Мень<PERSON>е багов", "value": "90%", "description": "меньше ошибок", "howTitle": "Как:", "howExplanation": "ИИ-проверки кода, автоматизированное тестирование и проверенные методы разработки кардинально сокращают количество багов."}}, "trustedBy": {"title": "Проверено лидерами индустрии"}, "trustVisual": {"satisfaction": "100% Удовлетворенность клиентов", "appsLaunched": "15+ Успешно запущенных приложений", "fromTo": "От медицинских практик до ремесленных предприятий", "trust": "Никакого стресса!", "additional": "+47 других", "industries": {"doctors": "Вра<PERSON>и", "crafts": "Ремесла", "startups": "Старта<PERSON>ы"}}, "rating": "<PERSON>ей<PERSON>инг", "responseTime": "24ч Ответ", "typeAnimation": ["Flutter MVP масштабируемые - Одна кодовая база, обе платформы 📱", 2000, "AI-готовая архитектура - Построена для завтрашних функций 🤖", 2000, "Firebase Backend - Надежная, масштабируемая инфраструктура ⚡", 2000, "SaaS-ориентированная разработка - Ваш успех наш приоритет 🚀", 2000, "", 500], "businessValue": {"headline": "Цифровая трансформация без IT-сложностей", "description": "Профессиональная разработка программного обеспечения для бизнеса. От стратегического консалтинга до продуктивных приложений – все из одних рук."}, "enterpriseFeatures": {"security": {"title": "Корпоративная безопасность", "description": "Высочайшие стандарты безопасности для ваших бизнес-данных"}, "architecture": {"title": "Масштабируемая архитектура", "description": "Перспективные решения, которые растут вместе с вашим бизнесом"}, "roi": {"title": "ROI-оптимизированный", "description": "Измеримое повышение эффективности и экономия затрат"}, "support": {"title": "24/7 корпоративная поддержка", "description": "Выделенная поддержка для критически важных бизнес-процессов"}}, "companyTrust": {"title": "Доверие лидеров рынка"}}, "painPointSection": {"familiarQuestion": "Знакомо?", "valueProposition": "Сэкономьте более €45,000 на затратах разработки", "valueSubtitle": "Вот как наш проверенный подход обеспечивает исключительные результаты, сокращая затраты и время более чем наполовину", "infiniteScrollPainPoints": ["Бесконечные совещания без результатов", "Дорогие агентства без прозрачности", "Месяцы разработки, нечего показать", "Младшие разработчики на критических проектах", "Скрытые расходы и сюрпризы", "Чрезмерно сложные технические решения", "Отсутствие четких сроков или вех", "Плохая коммуникация с разработчиками", "Привязка к поставщику и отсутствие гибкости", "Хаос в проекте и плохой менеджмент", "Отсутствие технической экспертизы", "Медленное время отклика на запросы"], "mainTitle": "Эти проблемы ежедневно стоят вам денег", "mainSubtitle": "Страдаете ли вы от этих типичных проблем разработки?", "solutionsTitle": "Как мы решаем проблемы", "solutionsSubtitle": "Готовые решения, которые работают немедленно", "costComparisonTitle": "Комплексный анализ затрат и ценности", "traditionalLabel": "Традиционный подход", "ourSolutionLabel": "Наше решение", "savingsText": "Общая экономия: €45,000 + на 6 недель быстрее", "provenText": "Проверено на 15+ успешных проектах в разных отраслях", "ctaText": "Начните экономить деньги сегодня", "comparisonHeaders": {"criteria": "Критерии сравнения", "traditional": "Традиционная разработка", "ourSolution": "Наше проверенное решение"}, "comparisonLabels": {"cost": "Стоимость разработки", "time": "Время до запуска", "architecture": "Качество архитектуры", "architectureTraditional": "Строится с нуля", "architectureOurs": "Проверенная и боевая", "security": "Реализация безопасности", "securityTraditional": "Должна быть разработана", "securityOurs": "Готовая для предприятий", "ai": "Интеграция ИИ", "aiTraditional": "Недоступна", "aiOurs": "Готовые к использованию промпты", "team": "Экспертиза команды", "teamTraditional": "Команды с младшими разработчиками", "teamOurs": "Старшие специалисты", "maintenance": "Долгосрочное обслуживание", "maintenanceTraditional": "Высокие текущие затраты", "maintenanceOurs": "Оптимизированное и эффективное", "scalability": "Масштаб<PERSON>руемость", "scalabilityTraditional": "Требуется перестройка", "scalabilityOurs": "Построено для масштабирования", "risk": "Риск проекта", "riskTraditional": "Высокий уровень неудач", "riskOurs": "Проверенный уровень успеха"}, "problems": {"missedOpportunities": {"title": "Упущенные рыночные возможности", "points": ["Месяцы планирования без результатов", "Конкуренты укрепляются быстрее", "Идеи застревают на совещаниях", "Теряется доля рынка"]}, "burningBudgets": {"title": "Горящие бюджеты", "points": ["Перегруженные внутренние команды", "Проекты взрывают бюджет", "Нет измеримого ROI", "Неэффективное использование ресурсов"]}, "stagnatingScaling": {"title": "Стагнирующее масштабирование", "points": ["Платформа не поспевает за ростом", "Проблемы производительности при расширении", "Технический долг блокирует прогресс", "Нет архитектуры будущего"]}}, "solutions": {"readyArchitecture": {"title": "Готовая архитектура проекта", "description": "Масштабируемая и готовая к продакшену", "value": "~€15,000 экономии"}, "seniorExpertise": {"title": "Экспертиза по требованию", "description": "Немедленная продуктивность", "value": "~€8,000 экономии"}, "aiDevelopment": {"title": "ИИ-разработка", "description": "Готовые AI-промпты для всех областей", "value": "~€12,000 экономии"}, "enterpriseSecurity": {"title": "Корпоративная безопасность", "description": "Интегрированные стандарты безопасности", "value": "~€10,000 экономии"}}, "costComparison": {"traditional": {"amount": "~€65,000", "timeline": "10-12 недель"}, "ourSolution": {"amount": "~€20,000", "timeline": "4-6 недель"}}}, "aiPoweredDevelopment": {"title": "Разработка будущего: Кодирование с поддержкой ИИ", "subtitle": "Когда опыт старших разработчиков встречается с передовыми инструментами ИИ", "description": "Мы объединяем 8+ лет опыта разработки с новейшими инструментами ИИ для достижения исключительных результатов. Каждая строка кода оптимизирована, протестирована и готова к продакшену.", "benefits": {"speed": {"title": "На 40% быстрее разработка", "description": "Генерация кода с помощью ИИ значительно ускоряет программирование", "icon": "Zap"}, "quality": {"title": "На 90% меньше ошибок", "description": "Код-ревью с помощью ИИ выявляет ошибки до того, как они станут проблемами", "icon": "Shield"}, "innovation": {"title": "Премиальное качество кода", "description": "Лучшие практики и паттерны применяются автоматически", "icon": "<PERSON><PERSON><PERSON>"}}, "features": [{"title": "Интеграция GitHub Copilot", "description": "Интеллектуальное завершение и генерация кода", "detail": "Более быстрая реализация сложных функций"}, {"title": "ИИ код-ревью", "description": "Автоматические проверки качества с каждым коммитом", "detail": "Гарантированное постоянное качество кода"}, {"title": "Умное тестирование", "description": "ИИ-генерируемые тест-кейсы для граничных сценариев", "detail": "Более высокое покрытие тестами, меньше сюрпризов"}, {"title": "Оптимизация производительности", "description": "ИИ анализирует и оптимизирует критические пути кода", "detail": "Более быстрые и эффективные приложения"}], "stats": {"codeQuality": {"value": "99.9%", "label": "Оценка качества кода"}, "timeReduction": {"value": "40%", "label": "Сокращение времени"}, "bugPrevention": {"value": "85%", "label": "Предотвращено ошибок"}, "satisfaction": {"value": "100%", "label": "Удовлетворенность клиентов"}}, "cta": {"title": "Готовы к разработке с поддержкой ИИ?", "description": "Испытайте будущее разработки программного обеспечения уже сегодня", "primaryButton": "Посмотреть как это работает", "secondaryButton": "Посмотреть кейсы"}}, "usp": {"badge": "Наши преимущества", "title": "Почему Innovatio-Pro отличается", "subtitle": "Никаких встреч. Никакого технического жаргона. Только ваше приложение.", "description": "Пока другие агентства вас запутывают техническими терминами, мы говорим на вашем языке и доставляем результаты.", "features": [{"title": "Персональная консультация для абсолютных технических новичков", "description": "Мы объясняем все простыми словами. Никакого жаргона, никакой путаницы.", "icon": "👥"}, {"title": "Приложения для любой отрасли и бюджета", "description": "Будь то врач, ремесленник или стартап – мы разрабатываем идеальное решение.", "icon": "🏗️"}, {"title": "Все из одного источника: от дизайна до запуска", "description": "Один контактный человек для всего. От первой идеи до магазина приложений.", "icon": "🎯"}, {"title": "Никаких скрытых затрат, никаких сюрпризов", "description": "Прозрачные цены с самого начала. Что мы говорим, то и делаем.", "icon": "💰"}, {"title": "Фиксированные контактные лица, четкое общение", "description": "У вас всегда есть один и тот же контактный человек, и вы точно знаете, что происходит.", "icon": "📞"}, {"title": "Быстрая доставка без потери качества", "description": "Благодаря проверенным процессам и современным инструментам мы доставляем быстро и надежно.", "icon": "⚡"}, {"title": "Технологии будущего", "description": "Мы используем современные, масштабируемые решения, которые растут вместе с вашим успехом.", "icon": "🚀"}, {"title": "100% фокус на вашем успехе", "description": "Мы довольны только тогда, когда довольны вы. Ваш успех – это наш успех.", "icon": "🏆"}], "cta": {"primary": "Получить бесплатную консультацию", "secondary": "Забронировать необязательный звонок"}, "trustIndicators": {"freeConsultation": "Бесплатная консультация", "nonBinding": "Без обязательств", "thirtyMinutes": "30 мин"}}, "ourProcess": {"title": "Наш проверенный процесс разработки", "subtitle": "От концепции до идеального Flutter-приложения всего за несколько недель", "description": "Систематический подход, который обеспечивает исключительные Flutter-приложения в срок. Наш процесс гарантирует качество, прозрачность и измеримые результаты.", "badge": "Наш проверенный процесс", "deliverables": "Результаты", "cta": {"title": "Готовы к вашему проекту?", "description": "Давайте обсудим ваши требования и создадим индивидуальный план разработки.", "primaryButton": "Начать проект", "secondaryButton": "Истории успеха"}, "steps": {"planning": {"title": "Ваше желание", "description": "Мы начинаем с понимания вашего видения, бизнес-целей и технических требований. Эта фаза включает исследование рынка, анализ пользователей и создание детальной дорожной карты.", "duration": "3-5 д<PERSON><PERSON><PERSON>", "deliverables": ["Требования к проекту и объем", "План технической архитектуры", "Временные рамки и дорожная карта", "Оценка рисков и стратегия"], "features": ["Анализ требований и определение объема проекта", "Техническая архитектура и выбор технологий", "Планирование временных рамок с дорожной картой"]}, "design": {"title": "Мы разрабатываем", "description": "Создание потрясающих UI/UX дизайнов, которые соответствуют вашему бренду. Мы создаем интерактивные прототипы для валидации концепций перед началом разработки.", "duration": "1 неделя", "deliverables": ["Полная система UI/UX дизайна", "Интерактивные кликабельные прототипы", "Интеграция брендинга", "Спецификации адаптивного дизайна"], "features": ["Система UI/UX дизайна с интеграцией бренда", "Интерактивные прототипы для валидации", "Адаптивный дизайн для всех устройств"]}, "development": {"title": "Мы поставляем", "description": "Быстрая Flutter-разработка с чистым, масштабируемым кодом. Непрерывное тестирование обеспечивает идеальную производительность на всех устройствах и платформах.", "duration": "2-4 недели", "deliverables": ["Готовое к производству Flutter-приложение", "Полная интеграция с бэкендом", "Комплексный набор тестов", "Оптимизация производительности"], "features": ["Чистый Flutter код для iOS и Android", "Интеграция с бэкендом и разработка API", "Комплексное тестирование и оптимизация"]}, "launch": {"title": "Запуск и поддержка", "description": "Бесшовное развертывание в магазинах приложений с постоянной поддержкой. Мы обеспечиваем успешный запуск вашего приложения и его отличную работу.", "duration": "Постоянно", "deliverables": ["Развертывание в App Store", "Стратегия запуска и маркетинг", "Настройка мониторинга производительности", "Постоянная поддержка и обновления"], "features": ["Отправка в App Store и развертывание", "Стратегия запуска и мониторинг производительности", "Постоянная поддержка и обновления функций"]}}}, "about": {"title": "Что меня отличает", "subtitle": "Разрабо<PERSON><PERSON>ик, который делает разницу", "motto": "\"You never lose. Either you win, or you learn.\"", "founderName": "Привет, я Виктор", "founderRole": "Мобильный и веб-разработчик, специализирующийся на современных, эффективных решениях", "whyDifferentTitle": "Что отличает меня от других", "whyDifferentText": "Пока другие еще планируют, я уже поставляю. Мои требования к качеству и страсть к превосходным решениям сделали меня Lead Developer везде — в Lufthansa, Union Investment и многих других.", "secretTitle": "Мой секрет?", "secretText": "Помимо технического совершенства, я приношу энергию и ясность, которые оживляют команды и устойчиво продвигают сложные проекты вперед. Поэтому я остаюсь в памяти моих клиентов не только как разработчик, но и как надежный партнер.", "description": "Когда будущее вашего стартапа зависит от быстрого выхода на рынок, вам нужен технический партнер, который понимает ставки. Мы не просто разработчики—мы ваши стратегические техконсультанты.", "vision": "Почему мы отличаемся", "visionDesc": "Пока агентства обещают все и доставляют поздно, мы специализируемся исключительно на Flutter MVP-разработке. 15+ SaaS-основателей доверили нам свой выход на рынок—каждый запустился вовремя и получил финансирование.", "mission": "Наш SaaS-первый подход", "missionDesc": "Мы понимаем срочность стартапов. Поэтому мы доставляем готовые к продакшену Flutter MVP за 4-6 недель, а не месяцев. Ваша архитектура спроектирована для масштабирования с первого дня—когда вы привлечете Series A, ваше приложение не потребует полной перестройки.", "founderTitle": "Виктор Хе<PERSON>м<PERSON><PERSON>н - Технический партнер", "founderDesc": "Бывший CTO стартапа, став<PERSON><PERSON> Flutter-специалистом. Я был там, где вы сейчас—гонка против времени, нужен <PERSON> вчера. Теперь я помогаю другим основателям избежать технических кошмаров, которые я пережил.", "whyChooseUs": "Почему SaaS-основатели выбирают нас", "reasons": {"fastDelivery": {"title": "Молниеносная поставка MVP", "description": "4-6 недель от старта до App Store. Пока конкуренты спорят о сроках, ваши пользователи уже дают обратную связь."}, "flutterSpecialization": {"title": "Чистое Flutter-мастерство", "description": "Мы не делаем все—мы совершенствуем одно. 5+ лет исключительно Flutter-приложений означает нулевую кривую обучения за ваш счет."}, "founderInvolved": {"title": "Техническое лидерство уровня основателя", "description": "Прямой доступ к техническим решениям старшего уровня. Никаких проект-менеджеров, никаких junior-разработчиков—только опытная экспертиза на вашем критическом пути."}, "scalableArchitecture": {"title": "Готов к масштабированию с первого дня", "description": "Ваша MVP-архитектура справляется с 10 пользователями или 10 миллионами. Когда рост взрывается, ваш фундамент остается крепким."}, "aiReady": {"title": "AI-нативная интеграция", "description": "Каждое приложение, которое мы создаем, архитектурно подготовлено для AI-функций. Добавляйте интеллектуальные возможности, когда того требует ваш roadmap."}, "strategicGuidance": {"title": "Стратегическое техконсультирование", "description": "Больше чем код—вы получаете архитектурные решения, технологические roadmap и проверенные стартапами технические стратегии с начала проекта."}}, "skills": "Навыки", "projects": "Проекты", "testimonials": "Отзывы", "experience": "Лет опыта", "clients": "Довольных клиентов", "transformBusiness": "Преобразование бизнеса с помощью технологий", "createSolutions": "Создание перспективных цифровых решений", "stayingAhead": "Лидерство в области технологий", "exceptionalUX": "Предоставление исключительного пользовательского опыта", "highPerformance": "Разработка высокопроизводительных приложений", "solvingChallenges": "Решение реальных бизнес-задач с помощью технологий", "flutterExpert": "Эксперт", "webDevAdvanced": "Продвинутый", "aiIntegration": "Интеграция", "projectsCompleted": "Завершенные проекты", "personalDedication": "Личный", "dedication": "Преданность", "qualityFocused": "Ориентированный на качество", "personalService": "Персональный сервис", "focusedApproach": "Целенаправленный подход", "dedicatedService": "Преданный сервис", "clientReviews": "Отзывы клиентов", "focusedService": "Целенаправленный сервис", "longTerm": "Долгосрочный", "partnerships": "Партнерства", "privacyFocused": "Ориентированный на конфиденциальность", "secureServices": "Безопасная разработка", "personalAttention": "Личное внимание", "dedicatedDeveloper": "Преданный разработчик", "securityFocused": "Ориентированный на безопасность", "privacyRespected": "Конфиденциальность соблюдена", "quality": "Качество", "averageDelivery": "Среднее время доставки", "metrics": {"yearsExperience": "8+", "saasLaunched": "15+", "fundingRaised": "€2,5M+", "clientRating": "5,0/5", "avgDeliveryTime": "4-6 недель", "flutterFocus": "100%", "zeroFailures": "0", "onTimeDelivery": "100%"}}, "advantages": {"title": "НАШИ РЕШЕНИЯ", "subtitle": "Как мы помогаем вам создавать успешные мобильные приложения", "speed": "Быстрая разработка", "speedDesc": "Мы быстро предоставляем решения без ущерба для качества", "stability": "Надежные приложения", "stabilityDesc": "Наши приложения созданы для стабильной и высокой производительности", "cost": "Экономическая эффективность", "costDesc": "Оптимизированный процесс разработки экономит ваше время и деньги", "timeToMarket": "Быстрый выход на рынок", "timeToMarketDesc": "Запускайте свой продукт быстро и опережайте конкурентов", "development": "Полноценная разработка", "developmentTime": "6-12 недель, зависит от сложности", "developmentDesc": "Полная разработка мобильного приложения от концепции до развертывания с полной интеграцией бэкенда и расширенными функциями.", "mvp": "Разработка MVP", "mvpTime": "4-8 недель, зависит от сложности", "mvpDesc": "Быстрый запуск с минимально жизнеспособным продуктом, включающим основной функционал для проверки вашей концепции и привлечения первых пользователей или инвесторов.", "prototype": "Быстрое прототипирование", "prototypeTime": "1-2 недели, зависит от сложности", "prototypeDesc": "Быстрое тестирование концепций с интерактивными прототипами перед полноценной разработкой, экономя время и ресурсы.", "qa": "Контроль качества", "qaTime": "Постоянно, масштабируется в зависимости от сложности проекта", "qaDesc": "Комплексное тестирование на различных устройствах и платформах для обеспечения безупречной работы вашего приложения с автоматизированными и ручными протоколами тестирования.", "consulting": "Техническое консультирование", "consultingTime": "По необходимости, зависит от сложности проекта", "consultingDesc": "Экспертные рекомендации по технологическому стеку, архитектурным решениям и стратегиям реализации для оптимизации вашего мобильного приложения.", "uiux": "UI/UX-дизайн", "uiuxTime": "2-3 нед<PERSON><PERSON><PERSON>, зависит от сложности", "uiuxDesc": "Ориентированный на пользователя дизайн, который сочетает эстетику с функциональностью, создавая интуитивно понятный и привлекательный мобильный опыт.", "maintenance": "Поддержка и сопровождение", "maintenanceTime": "Постоянно, масштабируется в зависимости от сложности проекта", "maintenanceDesc": "Долгосрочная поддержка с регулярными обновлениями, оптимизацией производительности и улучшением функций для поддержания конкурентоспособности вашего приложения.", "analytics": "Интеграция аналитики", "analyticsTime": "1-2 недели, зависит от сложности", "analyticsDesc": "Внедрение отслеживания данных для получения практических сведений о поведении пользователей, что позволяет принимать обоснованные решения для вашего приложения.", "training": "Обучение команды", "trainingTime": "1-2 недели, зависит от сложности", "trainingDesc": "Комплексное обучение вашей команды по поддержке и расширению вашего приложения после передачи.", "developmentEfficiency": "Эффективность разработки", "timeToMarketReduction": "Сокращение времени выхода на рынок", "conceptValidation": "Проверка концепции", "bugFreeRate": "Показатель отсутствия ошибок", "technicalImprovement": "Техническое улучшение", "userSatisfaction": "Удовлетворенность пользователей", "appUptime": "Бесперебойная работа приложения", "dataAccuracy": "Точность данных", "knowledgeRetention": "Сохранение знаний", "developmentInfo": {"title": "Сроки разработки", "simpleApp": {"title": "Простое приложение", "examples": "Примеры: списки дел, калькуляторы, простые информационные приложения без бэкенда.", "features": ["Несколько экранов (3-5)", "Отсутствие или минимальная интеграция с бэкендом", "Стандартные компоненты интерфейса", "Отсутствие сложных анимаций или функций"], "timeline": {"total": "Время разработки: 4-8 недель", "frontend": "Фронтенд: несколько недель", "backend": "Бэкенд (если требуется): 1-2 недели", "testing": "Тестирование и развертывание: 1-2 недели"}}, "mediumApp": {"title": "Среднее приложение", "examples": "Примеры: приложения электронной коммерции, социальные сети с базовыми функциями, приложения с регистрацией пользователей и интеграцией с базой данных.", "features": ["6-15 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Интеграция с бэкендом (например, REST или GraphQL API)", "Регистрация и аутентификация пользователей", "База данных для пользователей и данных приложения", "Некоторые анимации и интерактивные элементы", "Push-уведомления"], "timeline": {"total": "Время разработки: 8-16 недель", "frontend": "Фронтенд: 4-6 недель", "backend": "Бэкенд: 3-5 недель", "testing": "Тестирование и развертывание: 2-3 недели"}}, "complexApp": {"title": "Сложное приложение", "examples": "Примеры: приложения типа Uber, Instagram или банковские приложения с расширенными функциями.", "features": ["15+ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Высокоинтерактивный пользовательский интерфейс", "Функции реального времени (например, отслеживание в реальном времени, чат)", "Интеграция сторонних API (например, платежные шлюзы, API карт)", "Масштабируемый бэкенд с облачной интеграцией", "Функции безопасности (например, шифрование, двухфакторная аутентификация)", "Офлайн-функциональность"], "timeline": {"total": "Время разработки: 16-32 недели или дольше", "frontend": "Фронтенд: 6-10 недель", "backend": "Бэкенд: 6-12 недель", "testing": "Тестирование и развертывание: 4-6 недель"}}, "factors": {"title": "Факторы, влияющие на время разработки", "teamSize": "Размер команды: большая команда (например, отдельные разработчики для фронтенда, бэкенда и QA) может ускорить разработку. Один разработчик требует больше времени.", "technology": "Технологии: нативная разработка (например, Swift для iOS, Kotlin для Android) часто занимает больше времени, чем кроссплатформенные подходы, такие как Flutter. Flutter как самая современная технология может сократить время разработки на 40-60%.", "requirements": "Требования и изменения: частые изменения или неясные требования могут продлить время разработки.", "testing": "Тестирование и отладка: сложные приложения требуют больше времени для тестирования, особенно на нескольких платформах (iOS и Android).", "design": "Дизайн: простые дизайны требуют меньше времени, в то время как пользовательские, анимированные дизайны увеличивают время разработки."}, "summary": "Итого: Простое приложение: 4-8 недель. Среднее приложение: 8-16 недель. Сложное приложение: 16-32 недели или дольше.", "aiComparison": "С нашей разработкой на основе ИИ и Flutter мы можем сократить эти сроки на 40-60%, сохраняя при этом высокое качество и производительность."}}, "serviceSection": {"title": "Почему руководители бизнеса выбирают нашу техническую экспертизу", "subtitle": "Специализированные услуги, которые быстро доставляют и умно масштабируются", "description": "Восемь основных услуг. Ноль отвлечений. Максимальное влияние на цифровую основу вашего бизнеса и скорость выхода на рынок.", "viewAll": "Запланировать бесплатную консультацию", "comparisonTitle": "Скорость против традиционной разработки", "comparisonSubtitle": "Пока агентства спорят, мы доставляем", "timeComparison": {"title": "Приложение на рынок", "traditional": "16-24 недели", "withUs": "4-6 недель", "savings": "на 70% быстрее"}, "costComparison": {"title": "Техническое руководство", "traditional": "Штатный технический директор", "withUs": "Стратегическое консультирование", "savings": "80% экономии затрат"}, "qualityComparison": {"title": "Качество кода", "traditional": "Нужна перестройка приложения", "withUs": "Готов к масштабированию с первого дня", "savings": "Проверено временем"}, "flutterApp": {"title": "Flutter разработка приложений", "shortDescription": "Мы разрабатываем нативные приложения для iOS и Android из единой кодовой базы. Ваше приложение идеально чувствует себя на обеих платформах – без компромиссов в производительности или пользовательском опыте.", "description": "После 200+ успешных Flutter-проектов мы точно знаем, что важно. Пока другие агентства еще экспериментируют, мы уже освоили подводные камни и разработали оптимизированные процессы. Ваш проект получает проверенную архитектуру, стабильно работающую даже при 100,000+ активных пользователей. Мы говорим на языке команды Flutter от Google – и переводим это в ощутимые бизнес-результаты для вас.", "timeframe": "4-12 недель", "badge": "Кросс-платформенный", "metrics": ["⚡ На 60% быстрее", "💰 50% экономии затрат", "📱 Единая кодовая база", "🎯 98% одобрения клиентов"], "benefits": ["На 60% быстрее, чем создавать отдельные iOS и Android приложения", "Красивый, профессиональный дизайн, который впечатляет пользователей", "Мгновенные обновления во время разработки—видите изменения в реальном времени"], "details": {"architecture": "Clean Architecture с BLoC паттерном для максимальной тестируемости и поддерживаемости. Модульная структура позволяет легко добавлять новые функции без нарушения существующего кода.", "whyFaster": "Наши готовые шаблоны и проверенные компоненты сокращают время разработки на 60%. Мы не изобретаем велосипед – мы используем лучшие практики, отточенные на сотнях проектов.", "bestPractices": "Автоматизированное тестирование, CI/CD пайплайны, code review процессы и документация кода. Каждая строка кода проходит проверку качества перед релизом.", "techStack": "Flutter 3.x, Dart, Firebase/Supabase, REST/GraphQL APIs, State Management (BLoC/Riverpod), Automated Testing, CI/CD с GitHub Actions."}}, "mvp": {"title": "Разработка бизнес-приложений и прототипирование", "description": "От бизнес-идеи до работающего приложения в рекордные сроки. Мы создаем приложения, которые валидируют ваш концепт, и прототипы, которые обеспечивают финансирование—потому что скорость выхода на рынок определяет успех бизнеса.", "timeframe": "2-6 недель", "benefits": ["Готовые для инвесторов прототипы за 1-2 недели для раундов финансирования", "Полные бизнес-приложения с пользовательскими аккаунтами, платежами и аналитикой", "Созданы для роста с первого дня, а не только для тестирования"]}, "aiAssistant": {"title": "Умные функции и персонализация", "description": "Сделайте ваше приложение умным с ИИ, который действительно помогает пользователям. Умные ассистенты, персонализированные рекомендации и автоматизированные функции, которые удерживают клиентов и возвращают их обратно.", "timeframe": "3-5 недель", "benefits": ["На 30% выше вовлеченность пользователей через умную персонализацию", "Встроенные системы рекомендаций, которые изучают предпочтения пользователей", "Готовые к использованию умные компоненты, а не экспериментальные функции"]}, "consulting": {"title": "Консультирование по бизнес-технологиям и экспертное сопровождение", "description": "Старшее техническое руководство без полной занятости. Стратегические технологические решения, дорожные карты роста и insights по масштабированию от экспертов, которые провели бизнес от стартапа к успеху.", "timeframe": "Постоянное партнерство", "benefits": ["Техническая стратегия уровня технического директора за долю от стоимости полной занятости", "Технологические обзоры, которые предотвращают дорогие перестройки позже", "Прямой доступ к экспертным решениям, без посредников"]}, "backend": {"title": "Облачная инфраструктура и управление данными", "description": "Надежные основы, которые масштабируются от стартапа до предприятия без поломок. Данные в реальном времени, безопасные пользовательские аккаунты и системы, которые справляются с взрывным ростом пользователей—потому что ваша инфраструктура не должна ограничивать ваши амбиции.", "timeframe": "2-4 недели интеграции", "benefits": ["Функции реального времени, которые делают ваше приложение волшебным", "Масштабируемая инфраструктура с первого дня—никаких болезненных миграций", "Встроенная аналитика, безопасность и платежи сокращают время разработки на 40%"]}, "uiDesign": {"title": "Профессиональный дизайн приложений и пользовательский опыт", "description": "Красивые, ориентированные на конверсию дизайны приложений, которые пользователи любят использовать. От первоначальных концепций до пиксельно-идеальных интерфейсов, которые стимулируют вовлеченность и бизнес-результаты.", "timeframe": "1-3 недели", "benefits": ["Профессиональные макеты и интерактивные прототипы", "Исследование и оптимизация пользовательского опыта", "Адаптивный дизайн для всех устройств", "Полные дизайн-системы и библиотеки компонентов", "Оптимизированные для конверсий пользовательские потоки"]}, "testing": {"title": "Обеспечение качества и запуск в App Store", "description": "Комплексное тестирование для безупречной производительности приложения. От автоматизированного тестирования до подачи в App Store—мы гарантируем, что ваше приложение работает идеально для каждого пользователя.", "timeframe": "1-2 недели", "benefits": ["Автоматизированное тестирование для надежности", "Поддержка подачи в App Store и Google Play", "Тестирование производительности и совместимости", "Кроссплатформенное тестирование на всех устройствах", "Отслеживание ошибок и отчеты о качестве"]}, "cicd": {"title": "Автоматизированное развертывание и обновления", "description": "Оптимизированные обновления приложений и автоматические проверки качества. Автоматические пайплайны развертывания, которые гарантируют, что каждое обновление протестировано, безопасно и эффективно доставлено.", "timeframe": "3-5 д<PERSON><PERSON><PERSON>", "benefits": ["Настройка автоматизированного развертывания", "Автоматическое тестирование и проверки качества", "Развертывание в нескольких средах", "Мониторинг безопасности и производительности", "Контроль версий и простые откаты"]}}, "services": {"badge": "Наши услуги", "title": "Инновации встречают экспертизу", "subtitle": "Цифровые решения, которые трансформируют ваш бизнес", "description": "От стратегического консультирования до долгосрочной поддержки — мы предоставляем все услуги из одних рук для вашего цифрового успеха.", "cta": {"button": "Связаться с нами", "description": "Бесплатная консультация • Без обязательств • 30 мин"}, "services": [{"title": "Стратегия и концепция", "description": "Анализ ваших бизнес-целей и разработка индивидуальной цифровой стратегии.", "icon": "🎯"}, {"title": "UI/UX дизайн", "description": "Создание интуитивно понятных и ориентированных на пользователя интерфейсов.", "icon": "🎨"}, {"title": "Разработка приложений (iOS и Android)", "description": "Нативная и гибридная разработка для оптимальной производительности на всех устройствах.", "icon": "📱"}, {"title": "Backend и API разработка", "description": "Построение надежных и масштабируемых серверных архитектур для сложных приложений.", "icon": "⚙️"}, {"title": "Обеспечение качества и тестирование", "description": "Ручное и автоматизированное тестирование для обеспечения безошибочной и безопасной работы.", "icon": "🛡️"}, {"title": "Сопровождение и дальнейшее развитие", "description": "Долгосрочная поддержка и масштабирование вашего приложения после запуска.", "icon": "🔧"}]}, "prices": {"title": "Наши цены", "subtitle": "Прозрачные цены для каждого этапа", "description": "Выберите один из наших готовых пакетов или настройте их в соответствии с требованиями вашего проекта. Объедините услуги и получите скидку 15%.", "caseStudyTitle": "Пример: На 40% быстрее выход на рынок", "caseStudyDescription": "Наш финтех-клиент из Вайоминга запустил свой MVP на 40% быстрее, чем в среднем по отрасли, что позволило ему получить дополнительное финансирование и ускорить рост.", "promotionTitle": "Объедините услуги и сэкономьте 15%", "promotionDescription": "Объедините любые две или более услуги и получите скидку 15% на общую стоимость вашего проекта.", "leadCaptureTitle": "Начните свой путь Flutter MVP", "leadCaptureDescription": "Забронируйте бесплатный звонок-открытие, чтобы узнать, как мы можем воплотить ваше видение приложения в жизнь.", "discussButton": "Обсудить ваш проект", "contactButton": "Свяжитесь с нами", "pricingDisclaimer": "* Цены могут варьироваться в зависимости от требований проекта и дополнительных услуг. Свяжитесь с нами для получения индивидуального предложения, адаптированного под ваши конкретные потребности.", "priceVariesInfo": "Цена может варьироваться в зависимости от сложности проекта, дополнительных требований и временных ограничений. Свяжитесь с нами для получения детальной сметы.", "fullDetails": "Полные детали", "allIncludedFeatures": "Все включенные функции", "backendOptions": "Варианты бэкенда", "showDetails": "Показать детали", "valueProps": {"aiDevelopment": {"title": "ИИ-разработка", "description": "На 70% быстрее разработка"}, "backend": {"title": "Firebase и Supabase", "description": "Современные бэкенд-решения"}, "quality": {"title": "Корпоративное качество", "description": "Чистая архитектура"}}, "leadCapture": {"headline": "Начните свой путь Flutter MVP", "subheadline": "Забронируйте ваш бесплатный звонок-открытие", "introduction": "Вам не нужно разбираться во всех технических деталях. Наши основатели проведут вас через каждый этап—от валидации концепции до запуска в App Store. Давайте начнем с непринужденного разговора о вашем видении.", "trustStatement": "Каждый проект начинается с бесплатной стратегической сессии, где мы исследуем ваши цели, временные рамки и то, как Flutter может ускорить ваш путь к рынку.", "form": {"fullName": {"label": "Полное имя", "placeholder": "Ваше полное имя", "required": true}, "email": {"label": "Адрес электронной почты", "placeholder": "ваш.email@компания.ру", "required": true}, "company": {"label": "Название компании", "placeholder": "Название вашей компании или стартапа", "required": false, "optional": "(Необязательно)"}, "mvpGoal": {"label": "Какова ваша основная цель?", "placeholder": "Выберите вашу основную цель", "required": true, "options": {"prototyping": "Создать прототип для валидации моей идеи", "mvpLaunch": "Построить и запустить мой MVP", "saasScaling": "Масштабировать мой существующий SaaS продукт", "aiIntegration": "Добавить AI функции в мое приложение", "consulting": "Получить техническое руководство и стратегию", "notSure": "Пока не уверен - нужно руководство"}}, "timeline": {"label": "Когда вы хотели бы начать?", "placeholder": "Выберите предпочтительные временные рамки", "required": true, "options": {"asap": "Как можно скорее", "fourToSix": "Через 4-6 недель", "threeMonths": "Через 2-3 месяца", "undecided": "Временные рамки гибкие"}}, "budget": {"label": "Каков ваш предполагаемый бюджет?", "placeholder": "Выберите ваш бюджетный диапазон", "required": false, "options": {"below10k": "Менее €10,000", "10to20k": "€10,000 - €20,000", "20to50k": "€20,000 - €50,000", "above50k": "€50,000+", "notSure": "Пока не уверен"}}, "additionalNotes": {"label": "Расскажите нам о вашем проекте", "placeholder": "Кратко опишите вашу идею приложения, целевых пользователей или любые специфические требования...", "required": false, "optional": "(Необязательно)"}}, "ctaButton": "Забронировать мой бесплатный звонок-открытие", "disclaimer": "100% бесплатная консультация • Никаких обязательств • Обычно 15-30 минут", "alternativeContact": {"text": "Предпочитаете прямой чат?", "whatsappText": "WhatsApp нам", "emailText": "Отправить email"}, "successMessage": {"title": "Спасибо за ваш интерес!", "subtitle": "Мы получили вашу информацию", "description": "Наша команда рассмотрит детали вашего проекта и свяжется с вами в течение 24 часов для планирования вашего бесплатного звонка-открытия. А пока не стесняйтесь изучить наши кейс-стади и отзывы клиентов.", "nextSteps": {"title": "Что будет дальше:", "steps": ["Мы рассмотрим детали вашего проекта и требования", "Наш основатель лично свяжется с вами в течение 24 часов", "Мы запланируем 15-30 минутный звонок-открытие в удобное для вас время", "Во время звонка мы исследуем ваше видение и предоставим стратегическое руководство"]}, "backToSite": "Вернуться на главную", "viewCaseStudies": "Посмотреть кейс-стади"}}, "packages": {"prototype": {"title": "Прототип", "timeframe": "2-4 недели", "price": "€4.500", "badge": "Валидация", "description": "Кликабельный прототип без бэкенд-логики для первых пользовательских тестов и демо для инвесторов", "cta": "Начать прототип", "keyFeatures": ["UI/UX Дизайн Воркшопы", "ИИ-генерируемые дизайны", "Кликабельные макеты", "Готовый для инвесторов прототип"], "detailedFeatures": ["Интерактивные кликабельные прототипы", "UX/UI дизайн воркшопы с поддержкой ИИ", "Полные дизайны для всех экранов", "Сессии приоритизации функций", "Подготовка дорожной карты MVP", "Материалы для презентации инвесторам", "Адаптивный дизайн для всех устройств", "Без бэкенда - только фронтенд прототип", "Интеграция обратной связи после тестов", "Оценка технической осуществимости"]}, "mvp": {"title": "Стартовый MVP", "timeframe": "6-8 недель", "price": "€8.500", "badge": "Самый популярный выбор", "description": "Полное приложение с базовыми функциями для первого запуска", "cta": "Начать MVP", "keyFeatures": ["Аутентификация пользователей", "5+ основных экранов", "Firebase/Supabase бэкенд", "ИИ-поддерживаемая разработка"], "detailedFeatures": ["Полная разработка Flutter приложения", "Настройка Firebase или Supabase бэкенда", "Аутентификация и регистрация пользователей", "5+ основных экранов приложения", "ИИ-поддерживаемая разработка", "Архитектура чистого кода", "Адаптивный дизайн", "Базовые push-уведомления", "Поддержка развертывания в App Store", "30 дней премиум поддержки", "Базовая интеграция базы данных", "Оптимизация производительности"]}, "professional": {"title": "Профессиональное приложение", "timeframe": "8-12 недель", "price": "От €15.000", "badge": "Профессиональный", "description": "Продвинутое приложение с интеграцией платежей и профессиональной архитектурой", "cta": "Получить предложение", "keyFeatures": ["10+ э<PERSON><PERSON><PERSON><PERSON><PERSON>", "Интеграция платежей", "Push-уведомления", "Масштабируемая архитектура"], "detailedFeatures": ["Полная разработка Flutter приложения", "Firebase/Supabase бэкенд с расширенными функциями", "Интеграция платежей (Stripe, PayPal и др.)", "Продвинутые push-уведомления", "10+ экранов приложения", "Масштабируемая облачная архитектура", "Мониторинг производительности", "Продвинутое управление пользователями", "Интеграция аналитики данных", "Оптимизация App Store", "Админ-панель (по запросу)", "ИИ-функции (по необходимости)", "Комплексный набор тестов", "60 дней премиум поддержки"]}, "enterprise": {"title": "Корпоративное решение", "timeframe": "12+ недель", "price": "По запросу", "badge": "Корпоративный", "description": "Полностью индивидуализированное решение для компаний со специфическими требованиями", "cta": "Начать консультацию", "keyFeatures": ["Индивидуальное решение", "Неограниченные функции", "Корпоративная архитектура", "24/7 поддержка доступна"], "detailedFeatures": ["Полностью индивидуальная разработка", "Корпоративная бэкенд-архитектура", "Индивидуальная админ-панель и дашборд", "Индивидуальная интеграция ИИ", "Мультиплатформенное развертывание", "Продвинутые функции безопасности", "Масштабируемая облачная инфраструктура", "Мониторинг производительности и аналитика", "Разработка индивидуального API", "Интеграции с третьими сторонами", "Аудиты соответствия и безопасности", "24/7 поддержка доступна", "Выделенная команда разработки", "Непрерывные обновления и обслуживание"]}, "landingpage": {"title": "Разработка лендинга", "timeframe": "2-4 недели", "description": "Лендинги, соответствующие стандарту WCAG 2.0, с использованием новейших веб-технологий", "features": ["Соответствие WCAG 2.0 AA", "Инклюзивный дизайн для всех пользователей", "Совместимость с программами чтения с экрана", "Высокие показатели производительности", "SEO-оптимизированная структура", "Адаптивный дизайн"]}, "architecture": {"title": "Архитектура проекта", "timeframe": "1-2 недели", "description": "Надежная основа для успеха вашего проекта", "features": ["Технические спецификации", "Проектирование системной архитектуры", "Схема базы данных", "Документация API", "Дорожная карта разработки"]}, "consulting": {"title": "Технический консалтинг", "timeframe": "Постоянно", "description": "Экспертное руководство для ваших технических решений", "features": ["Рекомендации по технологическому стеку", "Проверка кода", "Оптимизация производительности", "Оценка безопасности", "Планирование масштабируемости", "Интеграция платежных шлюзов ОАЭ"]}}, "otherInquiries": {"title": "У вас другой вопрос?", "description": "Забронируйте звонок, и мы позаботимся об этом", "bookCall": "Забронировать звонок", "whatsappContact": "WhatsApp"}}, "leadCaptureForm": {"headline": "Начните ваш Flutter путь в 3 шага", "introduction": "Забронируйте ваш бесплатный звонок-открытие всего за 2 минуты", "steps": {"personal": "Ваши данные", "project": "Информация о проекте", "final": "Финальные детали"}, "stepDescriptions": {"personal": "Расскажите нам, кто вы", "project": "Поделитесь вашим видением", "final": "Добавьте финальные штрихи"}, "form": {"fullName": "Полное имя", "email": "Адрес электронной почты", "company": "Компания", "mvpGoal": {"label": "Какова ваша основная цель?", "placeholder": "Выберите вашу основную цель", "options": {"prototyping": "Валидировать мою идею с помощью прототипа", "mvpLaunch": "Построить и запустить мой MVP", "saasScaling": "Масштабировать мой существующий SaaS продукт", "aiIntegration": "Добавить AI функции в мое приложение", "consulting": "Получить техническое руководство и стратегию", "notSure": "Пока не уверен - нужно руководство"}}, "timeline": {"label": "Временные рамки", "placeholder": "Когда начать?", "options": {"asap": "Как можно скорее", "fourToSix": "Через 4-6 недель", "threeMonths": "Через 2-3 месяца", "undecided": "Временные рамки гибкие"}}, "budget": {"label": "<PERSON>ю<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Диа<PERSON>азон бюджета", "options": {"below10k": "Менее €10,000", "10to20k": "€10,000 - €20,000", "20to50k": "€20,000 - €50,000", "above50k": "€50,000+", "notSure": "Пока не уверен"}}, "additionalNotes": {"label": "Расскажите нам о вашем проекте", "placeholder": "Кратко опишите вашу идею приложения, целевых пользователей или любые специфические требования..."}, "whatHappensNext": {"title": "Что происходит дальше?", "steps": ["Мы рассмотрим ваш проект в течение 24 часов", "Запланируем 15-30 минутный звонок-открытие", "Получите персонализированное руководство для вашего MVP"]}, "validation": {"nameRequired": "Имя обязательно", "emailRequired": "Email обязателен", "emailInvalid": "Пожалуйста, введите действительный email адрес", "goalRequired": "Пожалуйста, выберите вашу основную цель", "timelineRequired": "Пожалуйста, выберите ваши предпочтительные временные рамки"}}, "navigation": {"back": "← Назад", "next": "Далее →", "submit": "Забронировать звонок-открытие", "submitting": "Отправка...", "stepOf": "<PERSON>аг"}, "successMessage": {"title": "Спасибо!", "description": "Мы свяжемся с вами в течение 24 часов, чтобы запланировать ваш бесплатный звонок-открытие.", "scheduleCall": "Запланировать звонок сейчас", "whatsapp": "WhatsApp", "backToSite": "← Назад на главную", "nextSteps": {"title": "Что происходит дальше:", "steps": ["Рассмотрим детали вашего проекта", "Персональная связь в течение 24ч", "Заплан<PERSON>руем 15-30 мин звонок-открытие", "Изучим ваше видение и предоставим руководство"]}}, "footer": {"preferDirectContact": "Предпочитаете прямой контакт?", "whatsapp": "WhatsApp", "email": "Email"}}, "showLess": "Показать меньше", "moreFeatures": "больше функций", "solutionsPortfolio": {"title": "Истории успеха клиентов", "subtitle": "Доверие лидеров индустрии", "description": "Реальные результаты от реальных клиентов", "trustMetrics": {"title": "Почему стартапы нам доверяют", "experience": "8+", "experienceLabel": "лет специализации на Flutter", "successRate": "98%", "successRateLabel": "успешность MVP", "fundingSuccess": "15+", "fundingSuccessLabel": "финансированных стартапов"}, "technicalCapabilities": {"title": "Техническое превосходство", "flutterMastery": {"title": "Мастерство Flutter", "description": "5+ лет исключительно создания продакшн Flutter-приложений. Единая кодовая база, нативная производительность на обеих платформах.", "metrics": ["На 40% быстрее разработки против нативной", "Одна кодовая база, обе платформы", "Гарантированная нативная производительность"]}, "aiIntegration": {"title": "AI-готовая архитектура", "description": "Сделайте ваш MVP готовым к будущему с AI-возможностями, встроенными с первого дня. OpenAI, <PERSON> и кастомные ML-модели.", "metrics": ["Интеграция OpenAI и Claude", "Обработка AI в реальном времени", "Масштабируемая ML-архитектура"]}, "mvpDelivery": {"title": "Быстрая поставка MVP", "description": "Готовые к продакшену MVP за 4-6 недель. Firebase backend, аутентификация, платежи - всё что нужно для запуска.", "metrics": ["Поставка за 4-6 недель", "Код готовый к продакшену", "Полный стек для запуска"]}}, "clientCaseStudies": {"lufthansa": {"title": "Lufthansa", "industry": "Авиация", "projectType": "Корпоративное Flutter-приложение", "description": "Разработка критически важной платформы управления путешествиями с использованием технологии Flutter", "businessContext": "Участие в разработке корпоративной мобильной архитектуры для крупнейшей авиагруппы Европы", "results": ["Устранил технический долг и продвинул дорожную карту функций", "Реализовал кроссплатформенную архитектуру", "Выполнил оптимизацию производительности"]}, "unionInvestment": {"title": "Union Investment", "industry": "Финансовые услуги", "projectType": "Run This Place App", "description": "Разработка приложения Run This Place - системы бронирования для парковки и управления рабочими местами", "businessContext": "Разработка инновационной системы бронирования парковочных мест и рабочих мест с использованием современных технологий Flutter", "results": ["Реализована система бронирования парковки", "Разработаны функции управления рабочими местами", "Создано удобное мобильное приложение"]}, "togg": {"title": "<PERSON><PERSON>", "industry": "Автомобильная", "projectType": "IoT Flutter-приложение", "description": "Разработка интеллектуальной системы управления транспортными средствами для электромобилей", "businessContext": "Участие в разработке мобильного приложения для первого турецкого проекта электромобилей", "results": ["Реализовано IoT-управление транспортными средствами", "Разработана связь в реальном времени", "Создано удобное приложение для транспортных средств"]}}, "cta": {"title": "Готовы создать свой MVP?", "subtitle": "Присоединяйтесь к 15+ стартапам, которые выбрали Flutter для выхода на рынок", "primaryButton": "Забронировать Discovery Call", "secondaryButton": "Посмотреть наш процесс", "guarantee": "Гарантия поставки 4-6 недель или возврат денег"}}, "portfolio": {"title": "Наша экспертиза", "subtitle": "Отрасли и решения проблем", "all": "Все отрасли", "screenshot": "Снимок экрана", "screenshots": "Снимки экрана", "problemsWeSolve": "Проблемы, которые мы решаем", "noSectorsFound": "Не найдено отраслей для выбранного фильтра.", "labels": {"technologies": "Технологии", "keyFeatures": "Ключевые особенности"}, "projects": [{"id": "logistics-platform", "title": "Корпоративная логистическая платформа", "description": "Разработка B2B-платформы для отслеживания и управления цепочками поставок в реальном времени. Включая интеграцию с существующими ERP-системами и автоматизированную отчетность.", "category": "Бизнес", "technologies": ["Flutter", "Google Cloud", "REST API", "ERP Integration"], "features": ["Отслеживание и аналитика в реальном времени", "Автоматизированная отчетность", "Интеграция с ERP-системами", "Ролевое управление доступом"]}, {"id": "detoxme", "title": "DetoxMe - Цифровое благополучие", "description": "Приложение для цифровой детоксикации и формирования здоровых привычек использования смартфона.", "category": "Образ жизни", "technologies": ["Flutter", "Firebase", "Analytics", "Push Notifications"], "features": ["Отслеживание экранного времени", "Блокировка приложений", "Медитация", "Отчеты о прогрессе"]}, {"id": "reserv", "title": "Reserv - Бронирование ресторанов", "description": "Современная платформа бронирования для ресторанов с доступностью в реальном времени.", "category": "Бизнес", "technologies": ["Flutter", "Firebase", "Payment APIs", "Real-time DB"], "features": ["Мгновенное бронирование", "Интеграция платежей", "Отзывы", "Управление столами"]}, {"id": "togg", "title": "TOGG - Подключенный автомобильный опыт", "description": "Официальное приложение для электромобилей TOGG с дистанционным управлением и умными функциями.", "category": "Автомобильная", "technologies": ["Flutter", "IoT", "Cloud Services", "Real-time Communication"], "features": ["Дистанционное управление", "Управление зарядкой", "Навигация", "Статус автомобиля"]}], "categories": {"aiAssistant": "ИИ-ассистент", "foodDelivery": "Доставка еды", "hospitality": "Гостиничный бизнес", "business": "Бизнес", "social": "Социальные сети", "automotive": "Автомобильная индустрия"}, "sectors": {"assistant": "Приложения-ассистенты", "food": "Заказ еды и доставка", "hospitality": "Гостиничный бизнес", "lifestyle": "Лайфстайл-приложения", "social": "Социальные сети", "automotive": "Автомобильная индустрия", "medical": "Медицина и здравоохранение", "business": "Бизнес-решения"}, "sectorDescriptions": {"assistant": "ИИ-помощники, которые повышают продуктивность и предоставляют персональные рекомендации", "food": "Платформы для заказа и доставки еды с отслеживанием в реальном времени", "hospitality": "Цифровые решения для отелей и гостиничного бизнеса для улучшения впечатлений гостей", "lifestyle": "Приложения, которые улучшают повседневную жизнь, благополучие и личностное развитие", "social": "Платформы, соединяющие людей и сообщества через общие интересы", "automotive": "Умные решения для управления автомобилем, навигации и помощи водителю", "medical": "Цифровые решения в области здравоохранения, улучшающие уход за пациентами и медицинские операции", "business": "Корпоративные приложения, оптимизирующие операции и повышающие производительность"}, "problems": {"assistant": {"1": "Информационная перегрузка", "2": "Управление задачами", "3": "Поддержка принятия решений"}, "food": {"1": "Управление заказами", "2": "Логистика доставки", "3": "Поиск ресторанов"}, "hospitality": {"1": "Управление гостями", "2": "Оптимизация сервиса", "3": "Системы бронирования"}, "lifestyle": {"1": "Отслеживание здоровья", "2": "Формирование привычек", "3": "Личная организация"}, "social": {"1": "Вовлеченность пользователей", "2": "Поиск контента", "3": "Построение сообществ"}, "automotive": {"1": "Мониторинг транспортных средств", "2": "Оптимизация навигации", "3": "Опыт водителя"}, "medical": {"1": "Управление пациентами", "2": "Монитор<PERSON>нг здоровья", "3": "Системы медицинских записей"}, "business": {"1": "Оптимизация рабочих процессов", "2": "Управление данными", "3": "Командное сотрудничество"}}, "viewDetails": "Посмотреть детали", "viewAllProjects": "Посмотреть все проекты"}, "clients": {"title": "Наши клиенты", "subtitle": "Компании, с которыми мы работали", "visitWebsite": "Посетить сайт"}, "testimonials": {"title": "Что говорят наши клиенты", "subtitle": "Реальный опыт профессионалов отрасли", "badge": "Истории успеха клиентов", "readMore": "Полный отзыв", "readLess": "Свернуть", "testimonials": [{"quote": "Надежный и структурированный! <PERSON> - чрезвычайно надежный и симпатичный разработчик мобильных приложений, который не оставляет желать лучшего как в технических компонентах, так и в пользовательском опыте!", "name": "<PERSON>in <PERSON>.", "designation": "CEO в Ultimind", "src": "/images/testimonials/emin.jpeg", "company": "<PERSON><PERSON><PERSON><PERSON>", "companyLogo": "/images/companies/lumeus.png", "industry": "Technology", "rating": 5, "projectType": "Разработка мобильных приложений", "deliveryTime": "Доставлено в срок", "badge": "Проверенный клиент", "results": ["Идеальный пользовательский опыт", "Структурированная реализация", "Никаких замечаний"]}, {"quote": "Виктора я познакомился в проекте как очень компетентного мобильного разработчика и оценил его. Его быстрое понимание и высокая вовлеченность в решение проблем замечательны. Благодаря ему команда значительно выросла в производительности, сплоченности и общем развитии команды.", "name": "<PERSON><PERSON>.", "designation": "Менеджер проектов в Union Investment Real Estate GmbH", "src": "/images/testimonials/stefanie.jpeg", "company": "Union Investment", "companyLogo": "/images/companies/union-investment.png", "industry": "Real Estate", "rating": 5, "projectType": "Командная работа", "deliveryTime": "Превзошел ожидания", "badge": "Корпоративный клиент", "results": ["Повышение производительности команды", "Улучшение сплоченности", "Компетентное решение проблем"]}, {"quote": "Я благодарен за невероятное сотрудничество, которое у нас было в проекте мобильного приложения. Его опыт в разработке фронтенда был действительно бесценен, и я в восторге от отличных результатов, которых мы достигли вместе.", "name": "<PERSON>", "designation": "Product UX Designer в Togg", "src": "/images/testimonials/mohammed.jpeg", "company": "<PERSON><PERSON>", "companyLogo": "/images/companies/togg.png", "industry": "Automotive", "rating": 5, "projectType": "UI/UX разработка", "deliveryTime": "Исключительное качество", "badge": "Премиум партнер", "results": ["Бесценный опыт фронтенда", "Отличные результаты", "Идеальное сотрудничество"]}, {"quote": "Как предприниматель в сфере недвижимости, я искала профессиональную поддержку для решений на основе порталов, веб-сайтов и приложений. Виктор смог просто объяснить мне сложные взаимосвязи и сразу понял мои цели.", "name": "<PERSON>.", "designation": "Эксперт по глобальной недвижимости в Walenwein Immobilien", "src": "/images/testimonials/natalia.jpeg", "company": "Lufthansa", "companyLogo": "/images/companies/lufthansa.png", "industry": "Aviation", "rating": 5, "projectType": "Full-Stack разработка", "deliveryTime": "Идеальные сроки", "badge": "Долгосрочный партнер", "results": ["Сложные темы объяснены просто", "Мгновенное понимание", "Профессиональная реализация"]}, {"quote": "Фантастическая работа! Приложение работает именно так, как мы себе представляли. Виктор очень профессионален и соблюдает все сроки.", "name": "<PERSON><PERSON>", "designation": "Генеральный директор", "src": "/images/testimonials/placeholder-male.svg", "company": "Локальная компания", "companyLogo": null, "industry": "Business", "rating": 5, "projectType": "Бизнес-приложение", "deliveryTime": "В срок", "badge": "Довольный клиент", "results": ["Работает идеально", "Профессиональная работа", "Все сроки соблюдены"], "isAnonymous": true}, {"quote": "Отличная коммуникация и технические навыки. Виктор предоставил именно то, что нужно нашему стартапу. Очень рекомендую!", "name": "<PERSON><PERSON>", "designation": "Основатель стартапа", "src": "/images/testimonials/placeholder-female.svg", "company": "Tech Startup", "companyLogo": null, "industry": "Technology", "rating": 5, "projectType": "MVP разработка", "deliveryTime": "Раньше срока", "badge": "Международный клиент", "results": ["Отличная коммуникация", "Идеальная техническая реализация", "Решение для стартапа"], "isAnonymous": true}, {"quote": "Очень довольны разработкой нашего медицинского приложения. Виктор идеально выполнил наши требования и всегда был доступен для вопросов.", "name": "<PERSON><PERSON> <PERSON><PERSON>", "designation": "Медицинский директор", "src": "/images/testimonials/placeholder-doctor.svg", "company": "Медицинская практика", "companyLogo": null, "industry": "Healthcare", "rating": 5, "projectType": "Медицинское приложение", "deliveryTime": "Как обещано", "badge": "Медицинский эксперт", "results": ["Идеальная реализация", "Всегда доступен", "Экспертиза в медицинских приложениях"], "isAnonymous": true}], "stats": {"satisfiedCustomers": "Довольные клиенты", "averageRating": "Средний рейтинг", "successfulApps": "Успешные приложения"}}, "aiEnhanced": {"badge": "Технологическое лидерство", "title": "Разработка будущего: AI-Enhanced Coding", "subtitle": "Передовые инструменты ИИ сочетаются с проверенными методами разработки для достижения превосходных бизнес-результатов.", "businessBenefits": [{"title": "Быстрый выход на рынок", "description": "ИИ ускоряет разработку для получения конкурентного преимущества и более раннего получения дохода.", "icon": "rocket"}, {"title": "Высокая надежность и удовлетворенность пользователей", "description": "Интеллектуальный анализ минимизирует ошибки для создания стабильных приложений.", "icon": "shield"}, {"title": "Перспективные и масштабируемые решения", "description": "Чистая кодовая база, которая растет вместе с бизнесом, защищая инвестиции.", "icon": "star"}], "processTitle": "Наш процесс для максимальной эффективности и качества", "aiFeatures": [{"title": "Автоматизированная генерация кода", "description": "ИИ-поддерживаемая разработка для более быстрой реализации", "icon": "bot"}, {"title": "Интеллектуальная проверка ошибок", "description": "Автоматический контроль качества с каждым коммитом", "icon": "check"}, {"title": "Умное тестирование", "description": "ИИ-генерированные тест-кейсы для критических сценариев", "icon": "search"}, {"title": "Оптимизация производительности", "description": "ИИ анализирует и оптимизирует критические пути кода", "icon": "gauge"}, {"title": "Автоматизированная документация", "description": "ИИ генерирует всестороннюю документацию и комментарии", "icon": "text"}, {"title": "Интеллектуальный рефакторинг", "description": "ИИ предлагает и реализует улучшения кода", "icon": "refresh"}, {"title": "Контекстное автодополнение", "description": "Интеллектуальное дополнение кода на основе контекста проекта", "icon": "brain"}, {"title": "Прогнозный анализ", "description": "Предсказание и предотвращение потенциальных проблем", "icon": "zap"}]}, "contact": {"title": "Связаться с нами", "subtitle": "Давайте формировать будущее", "description": "Обращайтесь к нам по любым вопросам, обсуждению проектов или для планирования консультации. Мы здесь, чтобы помочь воплотить ваше цифровое видение в жизнь.", "name": "Имя", "email": "Email", "phone": "Телефон", "message": "Сообщение", "send": "Отправить сообщение", "yourName": "Ваше имя", "yourEmail": "Ваш email", "subject": "Тема", "howCanIHelp": "Чем я могу помочь?", "yourMessageHere": "Ваше сообщение здесь", "getInTouch": "Связаться с нами", "sendMessage": "Отправить сообщение", "schedule": "Запланировать звонок", "freeConsultation": "Забронировать бесплатную 15-минутную консультацию", "location": "Местоположение", "submitButton": "Отправить сообщение", "sending": "Отправка...", "messageSent": "Сообщение отправлено!", "errorTryAgain": "Ошибка, попробуйте снова", "tryAgain": "Попробовать снова", "orSchedule": "Или запланируйте встречу напрямую, используя ссылку календаря", "formDescription": "Заполните форму ниже, и я свяжусь с вами в ближайшее время", "yourRequest": "Ваш запрос", "company": "Компания", "yourCompanyName": "Название вашей компании", "timeline": "Временные рамки", "selectTimeline": "Выберите временные рамки...", "asap": "Как можно скорее", "oneToThreeMonths": "1-3 месяца", "threeToSixMonths": "3-6 меся<PERSON><PERSON>в", "flexible": "Г<PERSON>б<PERSON><PERSON>", "estimatedBudget": "Предполагаемый бюджет", "selectBudgetRange": "Выберите диапазон бюджета...", "below5k": "Менее €5,000 - Малые проекты", "fiveToFifteenK": "€5,000 - €15,000 - Средние проекты", "fifteenToThirtyK": "€15,000 - €30,000 - Крупные проекты", "above30k": "€30,000+ - Корпоративные проекты", "notSure": "Пока не уверен - Давайте обсудим", "projectDescription": "Описание проекта", "projectPlaceholder": "Кратко опишите идею вашего проекта, цели и требования...", "services": {"prototype": "📱 Быстрый Прототип - От €3,000", "mvp": "🚀 MVP-Разработка - От €7,500", "saasGrowth": "📈 SaaS Growth Package - От €15,000", "custom": "🎯 Индивидуальный Запрос - По консультации"}, "serviceLabels": {"selectService": "Выберите услугу...", "prototype": "Быстрый Прототип", "mvp": "MVP-Ра<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>а", "saasGrowth": "SaaS Growth Package", "custom": "Индивидуальный Запрос"}}, "footer": {"copyright": "© 2025 Innovatio. Все права защищены.", "description": "Мы разрабатываем передовые мобильные и веб-приложения, которые трансформируют бизнес с помощью инновационных технологий.", "quickLinks": "Быстрые ссылки", "footerContact": "Контакты", "legal": "Правовая информация", "newsletter": "Рассылка", "newsletterDesc": "Подпишитесь на нашу рассылку, чтобы получать обновления и информацию.", "emailPlaceholder": "Введите ваш email", "subscribe": "Подписаться", "builtWith": "Создано с помощью", "and": "и", "downloadCV": "Моё резюме", "englishCV": "Английский", "germanCV": "Немецкий"}, "cookies": {"title": "Согласие на использование cookie", "description": "Мы используем файлы cookie для улучшения вашего опыта просмотра, показа персонализированной рекламы или контента и анализа нашего трафика. Нажимая «Принять все», вы соглашаетесь с использованием нами файлов cookie.", "acceptAll": "Принять все", "decline": "Отклонить", "customize": "Настроить", "necessary": "Необходимые cookies", "necessaryDesc": "Эти файлы cookie необходимы для правильного функционирования веб-сайта и не могут быть отключены.", "analytics": "Аналитические cookies", "analyticsDesc": "Эти файлы cookie помогают нам понять, как посетители взаимодействуют с нашим веб-сайтом, и помогают улучшить наши услуги.", "marketing": "Маркетинговые cookies", "marketingDesc": "Эти файлы cookie используются для отслеживания посетителей на веб-сайтах для отображения релевантной рекламы.", "functional": "Функциональные cookies", "functionalDesc": "Эти файлы cookie обеспечивают расширенную функциональность и персонализацию на нашем веб-сайте.", "save": "Сохранить настройки", "settings": "Настройки cookie", "close": "Закрыть", "cookiePolicy": "Политика использования cookie", "privacyPolicy": "Политика конфиденциальности"}, "heroParallax": {"title": "Лучшая студия разработки", "subtitle": "Мы создаем прекрасные продукты с использованием новейших технологий и фреймворков. Мы - команда увлеченных разработчиков и дизайнеров, которые любят создавать удивительные продукты.", "products": {"mobileApp": "Разработка мобильных приложений", "webDev": "Веб-разработка", "uiux": "UI/UX дизайн", "ecommerce": "Решения для электронной коммерции", "ai": "Интеграция ИИ", "cloud": "Облачные решения", "devops": "DevOps", "dataAnalytics": "Анализ данных", "blockchain": "Разработка блокчейн", "arvr": "AR/VR решения", "customSoftware": "Индивидуальное ПО", "mobileGame": "Разработка мобильных игр", "iot": "IoT решения", "api": "Разработка API", "cybersecurity": "Кибербезопасность"}}, "featuresSection": {"features": [{"title": "Создано для разработчиков", "description": "Создано для инженеров, разра<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, мечтателе<PERSON>, мыслителей и деятелей.", "icon": "IconTerminal2"}, {"title": "Простота использования", "description": "Это так же просто, как использовать Apple, и так же дорого, как купить его.", "icon": "IconEaseInOut"}, {"title": "Ценообразование, как ни у кого другого", "description": "Наши цены лучшие на рынке. Без ограничений, без блокировки, кредитная карта не требуется.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "Гарантия 100% времени безотказной работы", "description": "Нас просто невозможно вывести из строя.", "icon": "IconCloud"}, {"title": "Мультитенантная архитектура", "description": "Вы можете просто делиться паролями вместо покупки новых мест.", "icon": "IconRouteAltLeft"}, {"title": "Круглосуточная поддержка клиентов", "description": "Мы доступны 100% времени. По крайней мере, наши ИИ-агенты.", "icon": "IconHelp"}, {"title": "Гарантия возврата денег", "description": "Если вам не понравится EveryAI, мы убедим вас полюбить нас.", "icon": "IconAdjustmentsBolt"}, {"title": "И все остальное", "description": "У меня просто закончились идеи для текста. Примите мои искренние извинения.", "icon": "IconHeart"}]}, "seo": {"meta": {"home": {"title": "Разработка Мобильных Приложений и Цифровые Решения | Innovatio-Pro", "description": "Профессиональная разработка мобильных приложений на Flutter, интеграция ИИ и цифровые решения. MVP-разработка, прототипирование и полная разработка от Innovatio-Pro.", "keywords": "разработка мобильных приложений, Flutter разработка, MVP разработка, интеграция ИИ, цифровые решения, прототипирование, полная разработка, Innovatio-Pro"}, "templates": {"title": "Решения для Мобильных Приложений и Интеграция ИИ - Innovatio-Pro", "description": "Откройте для себя наши решения для мобильных приложений, интеграцию ИИ и цифровые платформы. Специализируемся на Flutter, MVP-разработке и инновационных мобильных технологиях.", "keywords": "решения мобильных приложений, интеграция ИИ, Flutter приложения, MVP разработка, цифровые платформы, мобильные технологии, разработка приложений"}, "services": {"title": "Услуги Разработки Мобильных Приложений - Flutter, ИИ и Цифровые Решения", "description": "Профессиональные услуги разработки мобильных приложений с использованием Flutter, интеграции ИИ и современных технологий. Специализируемся на MVP, прототипировании и полных мобильных решениях.", "keywords": "услуги мобильных приложений, Flutter разработка, MVP услуги, интеграция ИИ, мобильные решения, услуги разработки приложений, цифровая трансформация"}, "about": {"title": "О Innovatio-Pro - Разработка Мобильных Приложений и Специалист по ИИ", "description": "Узнайте об Innovatio-Pro, вашем специалисте по разработке мобильных приложений на Flutter, интеграции ИИ и инновационных цифровых решений.", "keywords": "Innovatio-Pro, разработчик мобильных приложений, Flutter специалист, интеграция ИИ, цифровые решения, компания разработки приложений"}, "contact": {"title": "Контакты - Разработка Мобильных Приложений и Цифровые Решения | Innovatio-Pro", "description": "Свяжитесь с Innovatio-Pro для профессиональной разработки мобильных приложений, интеграции ИИ и цифровых решений. Доступна бесплатная консультация для вашего следующего проекта.", "keywords": "контакты разработки мобильных приложений, консультация Flutter разработки, услуги интеграции ИИ, запрос цифровых решений, контакты Innovatio-Pro"}, "pricing": {"title": "Цены на Разработку Мобильных Приложений - MVP, Прототипирование и ИИ Решения", "description": "Прозрачные цены на разработку мобильных приложений, MVP-разработку, прототипирование и интеграцию ИИ. От быстрых прототипов до полных мобильных решений.", "keywords": "цены разработки мобильных приложений, стоимость MVP разработки, цены Flutter приложений, тарифы интеграции ИИ, цены прототипирования, предложения разработки приложений"}, "faq": {"title": "FAQ - Разработка Мобильных Приложений и Цифровые Решения | Innovatio-Pro", "description": "Часто задаваемые вопросы о наших услугах разработки мобильных приложений, Flutter разработке, интеграции ИИ и цифровых решениях. Поддержка и ответы от Innovatio-Pro.", "keywords": "FAQ разработки мобильных приложений, вопросы Flutter разработки, помощь интеграции ИИ, поддержка цифровых решений, ответы разработки приложений"}}, "openGraph": {"siteName": "Innovatio-Pro - Разработка Мобильных Приложений и Цифровые Решения", "defaultImage": "/images/og-innovatio-pro-ru.jpg", "defaultImageAlt": "Innovatio-Pro - Разработка Мобильных Приложений на Flutter с Интеграцией ИИ"}, "jsonLd": {"organization": {"name": "Innovatio-Pro", "alternateName": "Innovatio-Pro Мобильная Разработка", "description": "Специалист по разработке мобильных приложений на Flutter, интеграции ИИ и инновационных цифровых решений для современного бизнеса.", "url": "https://innovatio-pro.com", "telephone": "+49-175-9918357", "email": "<EMAIL>", "address": {"streetAddress": "Remote Office", "addressLocality": "Wyoming", "addressRegion": "Wyoming", "postalCode": "82001", "addressCountry": "US"}, "sameAs": ["https://github.com/innovatio-pro", "https://linkedin.com/company/innovatio-pro"]}}}, "blog": {"title": "Технические Инсайты", "description": "Изучайте последние тренды, инсайты и инновации в мобильной разработке. От лучших практик Flutter до стратегий интеграции ИИ - оставайтесь впереди в технологической революции.", "categoriesTitle": "Категории", "categoriesDescription": "Изучайте статьи по различным технологическим областям", "subscriptionTitle": "Оставайтесь в курсе", "subscriptionDescription": "Получайте последние инсайты Flutter, тренды ИИ и технологические инновации на ваш почтовый ящик.", "readMore": "Читать далее", "noArticles": "Статьи не найдены", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "readingTime": "Время чтения", "views": "Просмотры", "tags": "Теги", "floatingButton": {"tooltip": "Технический Блог", "currentArticle": "Текущая статья", "viewAll": "Посмотреть все статьи", "newPosts": "Новые посты"}, "categories": {"all": "Все статьи", "company": "О компании", "flutter": "Flutter разработка", "mobile": "Мобильные тренды", "ai": "Интеграция ИИ", "performance": "Производительность", "caseStudies": "Кейсы", "trends": "Отраслевые тренды"}}}