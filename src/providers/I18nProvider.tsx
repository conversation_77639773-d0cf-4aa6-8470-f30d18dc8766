'use client'

import { ReactNode, createContext, useContext, useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { getDictionary } from "@/lib/dictionaries";

export type Locale = "en" | "de" | "ru" | "tr" | "ar";

interface I18nContextProps {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string) => string;
  dir: "ltr" | "rtl";
  loading: boolean;
}

const defaultLocale: Locale = "en";
const supportedLocales: Locale[] = ["en", "de", "ru", "tr", "ar"];

// Create a context
const I18nContext = createContext<I18nContextProps>({
  locale: defaultLocale,
  setLocale: () => {},
  t: (key: string) => key,
  dir: "ltr",
  loading: true,
});

// Hook to use the i18n context
export const useI18n = () => useContext(I18nContext);

// Type for the translation dictionary
type TranslationDictionary = Record<string, any>;

// Cache for dictionaries to avoid repeated fetch
const dictionaryCache: Record<string, TranslationDictionary> = {};

export function I18nProvider({ children }: { children: ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>(defaultLocale);
  const [translations, setTranslations] = useState<TranslationDictionary>({});
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  // Handle RTL direction for Arabic
  const dir = locale === "ar" ? "rtl" : "ltr";

  // Map of countries to locales (same as in middleware)
  const countryLanguageMap: Record<string, Locale> = {
    DE: "de",
    AT: "de",
    CH: "de", // German-speaking
    RU: "ru",
    BY: "ru",
    KZ: "ru", // Russian-speaking
    TR: "tr",
    CY: "tr", // Turkish-speaking
    AE: "ar",
    SA: "ar",
    EG: "ar", // Arabic-speaking
    QA: "ar",
    BH: "ar",
    KW: "ar",
    OM: "ar",
    IQ: "ar",
  };

  // Effect to initialize locale from URL or local storage
  useEffect(() => {
    // PRIORITY 1: Always respect the URL path locale first
    // This is set by the middleware based on country detection
    const pathSegments = pathname ? pathname.split("/") : [];
    const pathLocale = pathSegments && pathSegments.length > 1 ? pathSegments[1] as Locale : null;
    if (pathLocale && supportedLocales.includes(pathLocale)) {
      console.log(`Using locale from URL path: ${pathLocale}`);
      setLocaleState(pathLocale);
      localStorage.setItem("locale", pathLocale);
      return;
    }

    // PRIORITY 2: First check if we're on innovatio-pro.com - always use German
    if (
      typeof window !== "undefined" &&
      window.location.hostname.includes("innovatio-pro.com")
    ) {
      console.log("On innovatio-pro.com domain - forcing German locale");
      setLocaleState("de");
      localStorage.setItem("locale", "de");

      // If we're not already on the German path, redirect
      if (pathLocale !== "de" && pathname && pathLocale) {
        const newPath = pathname.replace(`/${pathLocale}`, "/de");
        router.push(newPath || "/de");
      }
      return;
    }

    // PRIORITY 3: Check for ?country= parameter in URL
    if (typeof window !== "undefined") {
      const urlParams = new URLSearchParams(window.location.search);
      const countryParam = urlParams.get("country");

      if (countryParam) {
        const countryCode = countryParam.toUpperCase();
        const countryLocale = countryLanguageMap[countryCode] as Locale;

        if (countryLocale && supportedLocales.includes(countryLocale)) {
          console.log(
            `Country parameter detected: ${countryCode} → using locale ${countryLocale}`
          );

          // If we're already on a path with a different locale, we need to redirect
          if (
            pathLocale &&
            supportedLocales.includes(pathLocale) &&
            pathLocale !== countryLocale &&
            pathname
          ) {
            const newPath = pathname.replace(
              `/${pathLocale}`,
              `/${countryLocale}`
            );
            router.push(newPath);
          } else if (!pathLocale) {
            // No locale in path, add it
            router.push(`/${countryLocale}${pathname || ""}`);
          }

          // Set locale state and save to localStorage
          setLocaleState(countryLocale);
          localStorage.setItem("locale", countryLocale);
          return;
        }
      }
    }

    // PRIORITY 4: If no locale in URL, check localStorage
    const savedLocale = localStorage.getItem("locale") as Locale;
    if (savedLocale && supportedLocales.includes(savedLocale)) {
      console.log(`Using saved locale from localStorage: ${savedLocale}`);
      setLocaleState(savedLocale);

      // If we're not on any locale path, redirect to the saved locale
      if (!pathLocale && pathname) {
        router.push(`/${savedLocale}${pathname}`);
      }
    } else {
      // PRIORITY 5: Try to detect user's language
      const browserLang = navigator.language ? navigator.language.split("-")[0] as Locale : defaultLocale;
      const detectedLocale = supportedLocales.includes(browserLang)
        ? browserLang
        : defaultLocale;
      console.log(
        `Detected browser language: ${browserLang}, using locale: ${detectedLocale}`
      );
      setLocaleState(detectedLocale);
      localStorage.setItem("locale", detectedLocale);

      // If we're not on any locale path, redirect to the detected locale
      if (!pathLocale && pathname) {
        router.push(`/${detectedLocale}${pathname}`);
      }
    }
  }, [pathname, router]);

  // Load translations when locale changes
  useEffect(() => {
    if (!locale) return;

    async function loadTranslations() {
      try {
        setLoading(true);

        // Check cache first
        if (dictionaryCache[locale]) {
          setTranslations(dictionaryCache[locale]);
          setLoading(false);
          return;
        }

        // Fetch new dictionary
        const dict = await getDictionary(locale);
        dictionaryCache[locale] = dict;
        setTranslations(dict);
      } catch (error) {
        console.error(`Failed to load translations for ${locale}:`, error);
        // Fallback to English if translation fails
        if (locale !== "en") {
          try {
            const fallbackDict = await getDictionary("en");
            dictionaryCache["en"] = fallbackDict;
            setTranslations(fallbackDict);
          } catch (e) {
            console.error("Failed to load fallback English dictionary:", e);
          }
        }
      } finally {
        setLoading(false);
      }
    }

    loadTranslations();

    // Update html lang attribute and dir attribute
    document.documentElement.lang = locale;
    document.documentElement.dir = dir;
  }, [locale, dir]);

  // Function to change locale
  const setLocale = (newLocale: Locale) => {
    if (supportedLocales.includes(newLocale)) {
      setLocaleState(newLocale);
      localStorage.setItem("locale", newLocale);

      // Update URL to reflect locale change
      const segments = pathname ? pathname.split("/") : [];
      if (
        segments.length > 1 &&
        supportedLocales.includes(segments[1] as Locale)
      ) {
        segments[1] = newLocale;
        const newPath = segments.join("/");
        router.push(newPath);
      } else {
        router.push(`/${newLocale}${pathname || ""}`);
      }
    }
  };

  // Translation function
  const t = (key: string) => {
    if (!key) {
      return "";
    }

    // During loading, return the key itself to prevent hydration mismatch
    if (loading) {
      return key;
    }

    // Handle nested keys like "nav.home"
    const keys = key.split(".");
    let value = translations;

    for (const k of keys) {
      if (!value || typeof value !== "object") {
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
      value = value[k];
      if (value === undefined) {
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
    }

    return typeof value === "string" ? value : key;
  };

  return (
    <I18nContext.Provider value={{ locale, setLocale, t, dir, loading }}>
      {children}
    </I18nContext.Provider>
  );
}