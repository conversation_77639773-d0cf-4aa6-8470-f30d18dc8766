"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  DollarSign,
  Clock,
  ArrowRight,
  Code,
  Shield,
  Bot,
  X,
  Rocket,
  AlertTriangle,
  Check,
} from "lucide-react";
import { type Dictionary } from "@/lib/dictionary";
import { UnifiedBackground } from "@/components/ui/UnifiedBackground";

interface PainPointSectionProps {
  dictionary: Dictionary["painPointSection"];
}

// Default fallback data in case dictionary is missing
const defaultDictionary = {
  familiarQuestion: "Sound familiar?",
  valueProposition: "Save over €45,000 on development costs",
  valueSubtitle:
    "Here's how our proven approach delivers exceptional results while cutting costs and time by more than half",
  mainTitle: "These problems cost you money daily",
  mainSubtitle:
    "Are you also suffering from these typical development problems?",
  solutionsTitle: "How we solve problems",
  solutionsSubtitle: "Ready solutions that work immediately",
  costComparisonTitle: "Comprehensive Cost & Value Analysis",
  traditionalLabel: "Traditional Approach",
  ourSolutionLabel: "Our Solution",
  savingsText: "Total savings: €45,000 + 6 weeks faster delivery",
  provenText: "Proven with 15+ successful projects across industries",
  ctaText: "Start saving money today",
  infiniteScrollPainPoints: [
    "Endless meetings with no results",
    "Expensive agencies without transparency",
    "Months of development, nothing to show",
    "Junior developers on critical projects",
    "Hidden costs and surprises",
    "Overcomplicated technical solutions",
    "No clear timelines or milestones",
    "Poor communication with developers",
    "Vendor lock-in and no flexibility",
    "Project chaos and poor management",
    "Missing technical expertise",
    "Slow response times to requests",
  ],
  comparisonHeaders: {
    criteria: "Comparison Criteria",
    traditional: "Traditional Development",
    ourSolution: "Our Proven Solution",
  },
  comparisonLabels: {
    cost: "Development Cost",
    time: "Time to Launch",
    architecture: "Architecture Quality",
    architectureTraditional: "Built from scratch",
    architectureOurs: "Proven & battle-tested",
    security: "Security Implementation",
    securityTraditional: "Must be developed",
    securityOurs: "Enterprise-ready",
    ai: "AI Integration",
    aiTraditional: "Not available",
    aiOurs: "Ready-to-use prompts",
    team: "Team Expertise",
    teamTraditional: "Junior-heavy teams",
    teamOurs: "Senior specialists",
    maintenance: "Long-term Maintenance",
    maintenanceTraditional: "High ongoing costs",
    maintenanceOurs: "Optimized & efficient",
    scalability: "Scalability",
    scalabilityTraditional: "Rebuild required",
    scalabilityOurs: "Built for scale",
    risk: "Project Risk",
    riskTraditional: "High failure rate",
    riskOurs: "Proven success rate",
  },
  problems: {
    missedOpportunities: {
      title: "Missed Market Opportunities",
      points: [
        "Months of planning without results",
        "Competitors establish faster",
        "Ideas get stuck in meetings",
        "Market share is lost",
      ],
    },
    burningBudgets: {
      title: "Burning Budgets",
      points: [
        "Overloaded internal teams",
        "Projects explode cost-wise",
        "No measurable ROI",
        "Inefficient resource usage",
      ],
    },
    stagnatingScaling: {
      title: "Stagnating Scaling",
      points: [
        "Platform doesn't keep up with growth",
        "Performance issues during expansion",
        "Technical debt blocks progress",
        "No future-proof architecture",
      ],
    },
  },
  solutions: {
    readyArchitecture: {
      title: "Ready Project Architecture",
      description: "Scalable & production-ready",
      value: "~€15,000 savings",
    },
    seniorExpertise: {
      title: "Senior Expertise on-demand",
      description: "Immediate productivity",
      value: "~€8,000 savings",
    },
    aiDevelopment: {
      title: "AI-powered Development",
      description: "Ready AI prompts for all areas",
      value: "~€12,000 savings",
    },
    enterpriseSecurity: {
      title: "Enterprise Security",
      description: "Integrated security standards",
      value: "~€10,000 savings",
    },
  },
  costComparison: {
    traditional: {
      amount: "~€65,000",
      timeline: "10-12 weeks",
    },
    ourSolution: {
      amount: "~€20,000",
      timeline: "4-6 weeks",
    },
  },
};


export const PainPointSection = ({ dictionary }: PainPointSectionProps) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.01,
    rootMargin: "200px 0px 200px 0px",
  });


  // Use provided dictionary or fallback to default
  const dict = dictionary || defaultDictionary;

  // Additional safety checks for nested properties
  const safeDict = {
    ...defaultDictionary,
    ...dict,
    problems: {
      ...defaultDictionary.problems,
      ...dict.problems,
    },
    solutions: {
      ...defaultDictionary.solutions,
      ...dict.solutions,
    },
    costComparison: {
      ...defaultDictionary.costComparison,
      ...dict.costComparison,
    },
    comparisonHeaders: {
      ...defaultDictionary.comparisonHeaders,
      ...dict.comparisonHeaders,
    },
    comparisonLabels: {
      ...defaultDictionary.comparisonLabels,
      ...dict.comparisonLabels,
    },
    infiniteScrollPainPoints:
      dict.infiniteScrollPainPoints ||
      defaultDictionary.infiniteScrollPainPoints,
  };

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.05,
      },
    },
  };

  // Compact comparison data - focusing on the most important points
  const comparisonData = [
    {
      icon: DollarSign,
      label: safeDict.comparisonLabels.cost,
      traditional: safeDict.costComparison.traditional.amount,
      ourSolution: safeDict.costComparison.ourSolution.amount,
      isHighlight: true,
    },
    {
      icon: Clock,
      label: safeDict.comparisonLabels.time,
      traditional: safeDict.costComparison.traditional.timeline,
      ourSolution: safeDict.costComparison.ourSolution.timeline,
      isHighlight: true,
    },
    {
      icon: Code,
      label: safeDict.comparisonLabels.architecture,
      traditional: safeDict.comparisonLabels.architectureTraditional,
      ourSolution: safeDict.comparisonLabels.architectureOurs,
      isHighlight: false,
    },
    {
      icon: Shield,
      label: safeDict.comparisonLabels.security,
      traditional: safeDict.comparisonLabels.securityTraditional,
      ourSolution: safeDict.comparisonLabels.securityOurs,
      isHighlight: false,
    },
    {
      icon: Bot,
      label: safeDict.comparisonLabels.ai,
      traditional: safeDict.comparisonLabels.aiTraditional,
      ourSolution: safeDict.comparisonLabels.aiOurs,
      isHighlight: false,
    },
  ];



  return (
    <UnifiedBackground className="w-full">
      <section
        ref={ref}
        className="relative overflow-hidden w-full py-16 md:py-20"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            variants={staggerContainer}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="space-y-10"
          >
            {/* Header Section */}
            <motion.div variants={fadeInUp} className="text-center space-y-6">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800/50 dark:to-slate-700/50 text-slate-700 dark:text-slate-300 rounded-full text-sm font-medium border border-slate-200/50 dark:border-slate-700/50">
                <AlertTriangle className="w-4 h-4" />
                <span>{safeDict.familiarQuestion}</span>
              </div>

              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white text-center mb-6 leading-tight">
                {safeDict.valueProposition.split(" ").map((word, index) => {
                  const isColoredWord =
                    word.toLowerCase().includes("€") ||
                    word.toLowerCase().includes("save") ||
                    word.toLowerCase().includes("sparen");
                  return (
                    <span key={index}>
                      {isColoredWord ? (
                        <span className="text-[#1e3a8a] dark:text-blue-400">
                          {word}
                        </span>
                      ) : (
                        word
                      )}
                      {index <
                        safeDict.valueProposition.split(" ").length - 1 && " "}
                    </span>
                  );
                })}
              </h2>

              <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 text-center mb-8 max-w-3xl mx-auto leading-relaxed">
                {safeDict.valueSubtitle}
              </p>
            </motion.div>

            {/* Elegant Problem Badges - Scrolling */}
            <motion.div
              variants={fadeInUp}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="relative py-4 mb-8"
            >
              <div className="overflow-hidden">
                <motion.div
                  className="flex gap-6 items-center"
                  animate={{ x: [0, -300] }}
                  transition={{
                    duration: 25,
                    repeat: Infinity,
                    ease: "linear",
                  }}
                  style={{ width: "max-content" }}
                >
                  {[
                    "Überkomplizierte technische Lösungen",
                    "Keine klaren Timelines oder Meilensteine", 
                    "Schlechte Kommunikation mit Entwicklern",
                    "Versteckte Kosten und Überraschungen",
                    "Junior-Entwickler bei kritischen Projekten",
                    "Endlose Meetings ohne Ergebnisse",
                    "Monate Entwicklung, nichts funktioniert",
                    "Teure Agenturen ohne Transparenz",
                    // Duplicate for seamless loop
                    "Überkomplizierte technische Lösungen",
                    "Keine klaren Timelines oder Meilensteine", 
                    "Schlechte Kommunikation mit Entwicklern",
                    "Versteckte Kosten und Überraschungen",
                    "Junior-Entwickler bei kritischen Projekten",
                    "Endlose Meetings ohne Ergebnisse",
                    "Monate Entwicklung, nichts funktioniert",
                    "Teure Agenturen ohne Transparenz",
                  ].map((problem, index) => (
                    <div
                      key={index}
                      className="px-4 py-2 bg-gray-50 dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50 rounded-full text-gray-600 dark:text-gray-400 text-sm font-medium whitespace-nowrap hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors"
                    >
                      {problem}
                    </div>
                  ))}
                </motion.div>
              </div>
            </motion.div>

            {/* Kompakte Comparison Table */}
            <motion.div
              variants={fadeInUp}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="max-w-4xl mx-auto"
            >
              <div className="bg-white dark:bg-gray-800/90 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700/50 overflow-hidden">
                {/* Kompakter Header */}
                <div className="grid grid-cols-2">
                  <div className="p-4 bg-gray-50 dark:bg-gray-800/50 text-center border-r border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-center gap-2">
                      <AlertTriangle className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                      <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                        Andere Entwickler
                      </h3>
                    </div>
                  </div>
                  <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-center border border-green-200/50 dark:border-green-700/50">
                    <div className="flex items-center justify-center gap-2">
                      <h3 className="text-sm font-bold text-green-700 dark:text-green-300">
                        Mit Innovatio ✨
                      </h3>
                    </div>
                    <motion.div
                      className="mt-1 text-xs text-green-600 dark:text-green-400 font-medium"
                      animate={{ 
                        opacity: [0.6, 1, 0.6]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      Die bessere Lösung
                    </motion.div>
                  </div>
                </div>

                {/* Kompakte Comparison Rows */}
                <div className="divide-y divide-gray-100 dark:divide-gray-700">
                  {comparisonData.slice(0, 4).map((row, index) => {
                    const IconComponent = row.icon;
                    return (
                      <div key={index} className="grid grid-cols-2 text-sm">
                        {/* Traditional Column */}
                        <div className="p-4 border-r border-gray-100 dark:border-gray-700">
                          <div className="flex items-center gap-2 mb-1">
                            <IconComponent className="w-4 h-4 text-gray-400" />
                            <span className="font-medium text-gray-600 dark:text-gray-400 text-xs">{row.label}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="font-semibold text-gray-800 dark:text-gray-200">
                              {row.traditional}
                            </div>
                            <X className="w-4 h-4 text-red-500 flex-shrink-0 ml-2" />
                          </div>
                        </div>

                        {/* Our Solution Column */}
                        <div className="p-4 bg-green-50/30 dark:bg-green-900/10">
                          <div className="flex items-center gap-2 mb-1">
                            <IconComponent className="w-4 h-4 text-gray-400" />
                            <span className="font-medium text-gray-600 dark:text-gray-400 text-xs">{row.label}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="font-bold text-green-700 dark:text-green-300">
                              {row.ourSolution}
                            </div>
                            <Check className="w-4 h-4 text-green-500 flex-shrink-0 ml-2" />
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Eye-catching Bottom Summary */}
                <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-t border-green-200/50 dark:border-green-700/50">
                  <div className="flex items-center justify-center gap-3">
                    <motion.div
                      animate={{ 
                        rotate: [0, 10, -10, 0],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Rocket className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </motion.div>
                    <span className="text-sm font-bold text-green-700 dark:text-green-300">
                      Gesamtersparnis: 45.000€ + 6 Wochen früher
                    </span>
                    <motion.div
                      animate={{ 
                        scale: [1, 1.2, 1]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Check className="w-5 h-5 text-green-500" />
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>


            {/* Kompakter CTA */}
            <motion.div
              variants={fadeInUp}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-center"
            >
              <a
                href="#contact"
                className="inline-flex items-center gap-2 bg-gradient-to-r from-[#1e3a8a] to-[#1d4ed8] hover:from-[#1d4ed8] hover:to-[#2563eb] text-white font-semibold px-6 py-3 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02]"
              >
                <span>{dictionary?.ctaText || "Start Saving Costs Now"}</span>
                <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
              </a>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </UnifiedBackground>
  );
};

export default PainPointSection;
