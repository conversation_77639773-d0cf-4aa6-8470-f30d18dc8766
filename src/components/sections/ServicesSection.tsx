"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Target, Palette, Smartphone, Server, Brain, Shield, 
  Spark<PERSON>, Star 
} from 'lucide-react';
import { UnifiedBackground } from '@/components/ui/UnifiedBackground';
import { useI18n } from '@/providers/I18nProvider';
import { type Dictionary } from '@/lib/dictionary';
import BusinessDashboard from './BusinessDashboard';

interface Service {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  metric: string;
  metricLabel: string;
  color: string;
}

interface ServicesSectionProps {
  dictionary?: Dictionary["services"];
}

const CompactServicesSection = ({ dictionary }: ServicesSectionProps = {}) => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const { t } = useI18n();

  // iPhone Mockup Component - Premium realistic design
  const iPhoneMockup = () => (
    <motion.div
      className="w-80 h-[650px] mx-auto relative transition-transform duration-300 ease-out"
      style={{
        filter:
          "drop-shadow(0px 25px 50px rgba(0, 0, 0, 0.4)) drop-shadow(0px 12px 24px rgba(0, 0, 0, 0.3)) drop-shadow(0px 6px 12px rgba(0, 0, 0, 0.2))",
      }}
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.5 }}
    >
      {/* Main iPhone body with realistic thick black frame */}
      <div className="w-full h-full relative bg-gradient-to-br from-black via-gray-900 to-black rounded-[50px] overflow-hidden">
        {/* Screen area with dashboard */}
        <div className="absolute inset-3 rounded-[35px] overflow-hidden bg-black">
          {/* Dynamic Island */}
          <div
            className="absolute top-4 left-1/2 transform -translate-x-1/2 w-24 h-6 bg-black rounded-full flex items-center justify-center z-10"
            style={{
              boxShadow: "inset 0 1px 3px rgba(0,0,0,0.8)",
            }}
          >
            <div className="w-2 h-2 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full opacity-80"></div>
          </div>

          <BusinessDashboard />

          {/* Subtle screen reflections */}
          <div className="absolute inset-0 bg-gradient-to-tr from-white/5 via-transparent to-transparent pointer-events-none"></div>
        </div>

        {/* Volume buttons */}
        <div className="absolute left-[-2px] top-[130px] w-[3px] h-[32px] bg-gradient-to-r from-black to-gray-800 rounded-l-[1px]"></div>
        <div className="absolute left-[-2px] top-[180px] w-[3px] h-[32px] bg-gradient-to-r from-black to-gray-800 rounded-l-[1px]"></div>
        <div className="absolute left-[-2px] top-[230px] w-[3px] h-[52px] bg-gradient-to-r from-black to-gray-800 rounded-l-[1px]"></div>

        {/* Power button */}
        <div className="absolute right-[-2px] top-[180px] w-[3px] h-[60px] bg-gradient-to-l from-black to-gray-800 rounded-r-[1px]"></div>

        {/* Subtle frame highlights */}
        <div className="absolute inset-[1px] rounded-[49px] bg-gradient-to-t from-transparent via-white/5 to-white/10 pointer-events-none"></div>
      </div>
    </motion.div>
  );

  // Get services from dictionary or use fallback
  const getServices = (): Service[] => {
    if (dictionary?.services && Array.isArray(dictionary.services)) {
      return dictionary.services.map((service, index) => {
        const icons = [Target, Brain, Smartphone, Server, Palette, Shield];
        const metrics = ["+40%", "60%", "300%", "99.9%", "4.9★", "95%"];
        const metricLabels = ["growth", "efficiency", "performance", "uptime", "rating", "protection"];
        const colors = [
          "from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",
          "from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20",
          "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
          "from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",
          "from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20",
          "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
        ];
        
        return {
          title: service.title || "Service",
          description: service.description || "Service description",
          icon: icons[index] || Target,
          metric: metrics[index] || "+40%",
          metricLabel: metricLabels[index] || "growth",
          color: colors[index] || "from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20"
        };
      });
    }
    
    // Fallback services
    return [
      {
        title: "Digital Strategy",
        description: "Transformative business strategies",
        icon: Target,
        metric: "+40%",
        metricLabel: "Growth",
        color: "from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",
      },
      {
        title: "AI Integration", 
        description: "Intelligent automation solutions",
        icon: Brain,
        metric: "60%",
        metricLabel: "Efficiency",
        color: "from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20",
      },
      {
        title: "App Development",
        description: "Cross-platform mobile solutions", 
        icon: Smartphone,
        metric: "300%",
        metricLabel: "Performance",
        color: "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
      },
      {
        title: "Cloud Architecture",
        description: "Scalable infrastructure solutions",
        icon: Server,
        metric: "99.9%",
        metricLabel: "Uptime",
        color: "from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",
      },
      {
        title: "UI/UX Design",
        description: "User-centered design excellence",
        icon: Palette,
        metric: "4.9★",
        metricLabel: "Rating",
        color: "from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20",
      },
      {
        title: "Cybersecurity",
        description: "Advanced security solutions",
        icon: Shield,
        metric: "95%",
        metricLabel: "Protection",
        color: "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
      },
    ];
  };

  const services = getServices();

  return (
    <UnifiedBackground className="py-16 lg:py-24 bg-white dark:bg-gray-900 relative overflow-hidden">
      <section>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header section */}
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            {/* Badge */}
            <motion.div
              className="inline-flex items-center gap-2 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-4 py-2 rounded-full text-sm font-bold mb-6 border border-blue-100 dark:border-blue-800"
              whileHover={{ scale: 1.05 }}
            >
              <Sparkles className="w-4 h-4" />
              <span>
                {dictionary?.badge || t("services.badge") || "Our Services"}
              </span>
            </motion.div>

            <motion.h2
              className="text-4xl sm:text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gray-900 via-blue-600 to-purple-600 dark:from-white dark:via-blue-400 dark:to-purple-400 mb-6 leading-tight"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              {dictionary?.title ||
                t("services.title") ||
                "Innovation Meets Expertise"}
            </motion.h2>

            <motion.p
              className="text-lg lg:text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              {dictionary?.subtitle ||
                t("services.subtitle") ||
                "Digital solutions that transform your business"}
            </motion.p>
          </motion.div>

          {/* Services and iPhone Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-16 mb-20 items-start">
            {/* Left: Services Grid - Takes 2 columns */}
            <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-12">
              {services.map((service, index) => {
                const IconComponent = service.icon;

                const gradients = [
                  "from-blue-500 to-indigo-600",
                  "from-emerald-500 to-teal-600",
                  "from-purple-500 to-pink-600",
                  "from-blue-500 to-indigo-600",
                  "from-emerald-500 to-teal-600",
                  "from-purple-500 to-pink-600",
                ];

                return (
                  <motion.div
                    key={index}
                    className="group text-center"
                    initial={{
                      opacity: 0,
                      y: 30,
                      scale: 0.95,
                    }}
                    animate={{
                      opacity: 1,
                      y: 0,
                      scale: 1,
                    }}
                    transition={{
                      duration: 0.6,
                      delay: index * 0.1,
                    }}
                    whileHover={{
                      scale: 1.02,
                      y: -4,
                      transition: { duration: 0.3, ease: "easeOut" },
                    }}
                    onMouseEnter={() => setHoveredCard(index)}
                    onMouseLeave={() => setHoveredCard(null)}
                  >
                    {/* Centered Icon */}
                    <div
                      className={`w-16 h-16 bg-gradient-to-r ${gradients[index]} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg`}
                    >
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>

                    {/* Title */}
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 leading-tight">
                      {service.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
                      {service.description}
                    </p>

                    {/* Highlight Tag */}
                    <div
                      className={`inline-block text-sm font-semibold bg-gradient-to-r ${gradients[index]} bg-clip-text text-transparent`}
                    >
                      {service.metric} {service.metricLabel}
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {/* Right: iPhone Mockup - Takes 1 column */}
            <div className="lg:col-span-1 flex justify-center lg:justify-center">
              {iPhoneMockup()}
            </div>
          </div>

          {/* CTA Section */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <motion.button
              className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-200/20"
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <span className="relative z-10 flex items-center gap-2">
                {dictionary?.cta?.button ||
                  t("services.cta.button") ||
                  t("contact.getInTouch") ||
                  "Get In Touch"}
                <Star className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </motion.button>

            <motion.p
              className="text-gray-500 dark:text-gray-400 text-sm mt-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.5 }}
            >
              {dictionary?.cta?.description ||
                t("services.cta.description") ||
                "Free Consultation • No Obligation • 30 Min"}
            </motion.p>
          </motion.div>
        </div>
      </section>
    </UnifiedBackground>
  );
};

export const ServicesSection = CompactServicesSection;
export default CompactServicesSection;