"use client";

import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Rocket,
  Shield,
  Star,
  Sparkles,
  Bot,
  Code2,
  CheckCircle2,
  Brain,
  Zap,
  Search,
  FileCheck,
  Gauge,
  FileText,
  RefreshCw,
} from "lucide-react";
import { UnifiedBackground } from "@/components/ui/UnifiedBackground";

interface AiEnhancedSectionProps {
  dictionary: {
    badge?: string;
    title: string;
    subtitle: string;
    businessBenefits: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
    processTitle: string;
    aiFeatures: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
  };
}

export function AiEnhancedSection({ dictionary }: AiEnhancedSectionProps) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Icon mapping for business benefits
  const businessIconMap = {
    rocket: Rocket,
    shield: Shield,
    star: Star,
  };

  // Icon mapping for AI features
  const featureIconMap = {
    bot: Bo<PERSON>,
    code: Code2,
    check: CheckCircle2,
    brain: Brain,
    zap: Zap,
    search: Search,
    file: FileCheck,
    gauge: Gauge,
    text: FileText,
    refresh: RefreshCw,
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.15,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 40, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.7,
      },
    },
  };

  return (
    <UnifiedBackground className="py-16 lg:py-24 bg-white dark:bg-gray-900 relative overflow-hidden">
      <section
        id="ai-enhanced"
        ref={ref}
        className="relative"
      >
        <div className="w-full px-4 sm:px-6 lg:px-8 xl:px-12 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="max-w-7xl mx-auto"
        >
          {/* Header section */}
          <motion.div
            variants={itemVariants}
            className="text-center mb-16"
          >
            {/* Badge */}
            <motion.div
              variants={itemVariants}
              className="inline-flex items-center gap-2 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-4 py-2 rounded-full text-sm font-bold mb-6 border border-blue-100 dark:border-blue-800"
            >
              <Sparkles className="w-4 h-4" />
              <span>{dictionary.badge || "Technologie-Führerschaft"}</span>
            </motion.div>

            <motion.h2
              variants={itemVariants}
              className="text-4xl sm:text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gray-900 via-blue-600 to-purple-600 dark:from-white dark:via-blue-400 dark:to-purple-400 mb-6 leading-tight"
            >
              {dictionary.title}
            </motion.h2>

            <motion.p
              variants={itemVariants}
              className="text-lg lg:text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed"
            >
              {dictionary.subtitle}
            </motion.p>
          </motion.div>

          {/* Business Benefits - Three Cards Grid */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20"
          >
            {dictionary.businessBenefits.map((benefit, index) => {
              const IconComponent = businessIconMap[benefit.icon as keyof typeof businessIconMap] || Rocket;
              const gradients = [
                "from-blue-500 to-indigo-600",
                "from-emerald-500 to-teal-600", 
                "from-purple-500 to-pink-600",
              ];
              const bgGradients = [
                "from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",
                "from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20",
                "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
              ];

              return (
                <motion.div
                  key={index}
                  variants={cardVariants}
                  className={`group relative p-8 rounded-3xl bg-gradient-to-br ${bgGradients[index]} border border-white/50 dark:border-gray-700/50 shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden`}
                  whileHover={{
                    scale: 1.02,
                    y: -8,
                    transition: { duration: 0.3, ease: "easeOut" },
                  }}
                >
                  {/* Background glow effect */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${gradients[index]} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl`}
                  ></div>

                  {/* Icon */}
                  <div
                    className={`w-20 h-20 rounded-2xl bg-gradient-to-br ${gradients[index]} flex items-center justify-center mb-6 shadow-xl group-hover:scale-110 transition-transform duration-300`}
                  >
                    <IconComponent className="w-10 h-10 text-white" />
                  </div>

                  {/* Content */}
                  <div className="relative z-10">
                    <h3 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white mb-4 leading-tight">
                      {benefit.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {benefit.description}
                    </p>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-white/20 to-transparent rounded-full transform translate-x-8 -translate-y-8" />
                  <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-white/10 to-transparent rounded-full transform -translate-x-10 translate-y-10" />
                </motion.div>
              );
            })}
          </motion.div>

          {/* Process Section */}
          <motion.div
            variants={itemVariants}
            className="text-center mb-12"
          >
            <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-8">
              {dictionary.processTitle}
            </h3>

            {/* AI Features Grid - 2x4 Layout */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-6xl mx-auto">
              {dictionary.aiFeatures.slice(0, 8).map((feature, index) => {
                const IconComponent = featureIconMap[feature.icon as keyof typeof featureIconMap] || Bot;
                const gradients = [
                  "from-violet-500 to-purple-600",
                  "from-cyan-500 to-blue-600",
                  "from-green-500 to-emerald-600",
                  "from-orange-500 to-red-600",
                  "from-rose-500 to-pink-600",
                  "from-amber-500 to-yellow-600",
                  "from-indigo-500 to-blue-600",
                  "from-teal-500 to-cyan-600",
                ];

                return (
                  <motion.div
                    key={index}
                    variants={cardVariants}
                    whileHover={{
                      scale: 1.02,
                      y: -4,
                      transition: { duration: 0.2, ease: "easeOut" },
                    }}
                    className="group relative"
                  >
                    <div className="relative p-6 rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col">
                      {/* Icon */}
                      <div
                        className={`w-14 h-14 rounded-xl bg-gradient-to-br ${gradients[index]} flex items-center justify-center mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300 mx-auto`}
                      >
                        <IconComponent className="w-7 h-7 text-white" />
                      </div>

                      {/* Title */}
                      <h4 className="text-base font-semibold text-gray-900 dark:text-white text-center mb-2 leading-tight">
                        {feature.title}
                      </h4>

                      {/* Description */}
                      <p className="text-sm text-gray-600 dark:text-gray-300 text-center leading-relaxed flex-grow">
                        {feature.description}
                      </p>

                      {/* Hover glow */}
                      <div
                        className={`absolute inset-0 bg-gradient-to-r ${gradients[index]} opacity-0 group-hover:opacity-5 transition-opacity duration-300 rounded-2xl`}
                      ></div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </motion.div>
        </div>
      </section>
    </UnifiedBackground>
  );
}