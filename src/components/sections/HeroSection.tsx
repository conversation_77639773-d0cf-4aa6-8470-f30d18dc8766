"use client";

import React from "react";
import { type Dictionary } from "@/lib/dictionary";
import { motion, type Variants } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  ArrowRight,
  Clock,
  Star,
  Award,
  Shield,
  Settings,
  TrendingUp,
  Users,
} from "lucide-react";
import Link from "next/link";
import UnifiedBackground from "@/components/ui/UnifiedBackground";
import AnimatedHeroPhone from "@/components/ui/AnimatedHeroPhone";

interface HeroSectionProps {
  dictionary: Dictionary["hero"];
}

const companies = [
  { name: "<PERSON><PERSON><PERSON>", logo: "/images/companies/adesso.png" },
  { name: "Heg<PERSON>", logo: "/images/companies/hegla.png" },
  { name: "Innovate", logo: "/images/companies/innovate.png" },
  { name: "<PERSON><PERSON>han<PERSON>", logo: "/images/companies/lufthansa.png" },
  { name: "Lumeus", logo: "/images/companies/lumeus.png" },
  { name: "Togg", logo: "/images/companies/togg.png" },
  { name: "Union Investment", logo: "/images/companies/union-investment.png" },
];

export const HeroSection = ({ dictionary }: HeroSectionProps) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <UnifiedBackground className="min-h-screen bg-white dark:bg-gray-900 overflow-hidden">
      <section className="relative min-h-screen" ref={ref}>
        <div className="w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20 3xl:px-24 4xl:px-32 relative z-10 pt-2">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="relative min-h-screen pt-16 pb-0 items-center flex"
          >
            {/* Grid Layout: Content Left, iPhone Right */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 w-full items-center">
              {/* Left Column - Content */}
              <div className="relative z-20 flex flex-col justify-center text-center lg:text-left space-y-3 lg:space-y-4 xl:space-y-5 2xl:space-y-6">
                {/* Stats row */}
                <motion.div
                  variants={itemVariants}
                  className="flex flex-row justify-center lg:justify-start items-center gap-2 lg:gap-4 divide-x divide-gray-300 dark:divide-gray-700 mb-6"
                >
                  {/* Rating */}
                  <div className="flex items-center space-x-1 text-xs lg:text-sm font-medium text-gray-700 dark:text-gray-300">
                    {/* 1 star on mobile, 5 stars on desktop */}
                    <div className="flex items-center space-x-0.5">
                      <Star className="w-2.5 h-2.5 lg:w-3 lg:h-3 fill-yellow-400 text-yellow-400" />
                      <span className="hidden lg:flex lg:space-x-0.5">
                        {Array.from({ length: 4 }).map((_, i) => (
                          <Star
                            key={i}
                            className="w-3 h-3 fill-yellow-400 text-yellow-400"
                          />
                        ))}
                      </span>
                    </div>
                    <span className="ml-1 text-xs lg:text-sm">
                      4.9/5 {dictionary.rating || "Bewertung"}
                    </span>
                  </div>

                  {/* Projects completed */}
                  <div className="flex items-center space-x-1 text-xs lg:text-sm font-medium text-gray-700 dark:text-gray-300 pl-2 lg:pl-4">
                    <Award className="w-2.5 h-2.5 lg:w-3 lg:h-3 text-green-600" />
                    <span className="text-xs lg:text-sm">
                      {dictionary.trustVisual?.appsLaunched ||
                        "erfolgreich gelauncht"}
                    </span>
                  </div>

                  {/* Delivery speed */}
                  <div className="flex items-center space-x-1 text-xs lg:text-sm font-medium text-gray-700 dark:text-gray-300 pl-2 lg:pl-4">
                    <Clock className="w-2.5 h-2.5 lg:w-3 lg:h-3 text-blue-600" />
                    <span className="text-xs lg:text-sm">
                      4-{dictionary.responseTime || "Antwortzeit"}
                    </span>
                  </div>
                </motion.div>

                {/* Main Title */}
                <motion.h1
                  variants={itemVariants}
                  className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 dark:text-white group cursor-pointer leading-tight mb-6"
                >
                  <span className="text-gray-900 dark:text-white">
                    {dictionary.title || "Apps die Ihr"}{" "}
                  </span>
                  <span className="relative inline-block">
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-gray-900 via-blue-600 to-purple-600 dark:from-white dark:via-blue-400 dark:to-purple-400 relative">
                      {dictionary.subtitle || "Business transformieren"}
                      {/* Shimmer effect */}
                      <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    </span>
                    {/* Professional underline effect */}
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 transform scale-x-100 group-hover:scale-x-105 transition-all duration-300 origin-left"></span>
                  </span>
                </motion.h1>

                {/* Business Value Statement */}
                <motion.div variants={itemVariants} className="mb-8">
                  <p className="text-xl md:text-2xl font-semibold text-gray-900 dark:text-white mb-4 leading-tight">
                    {dictionary.businessValue?.headline}
                  </p>
                  <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl leading-relaxed">
                    {dictionary.businessValue?.description}
                  </p>
                </motion.div>

                {/* Business Value Propositions */}
                <motion.div
                  variants={itemVariants}
                  className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8 max-w-4xl mb-12"
                >
                  <motion.div
                    className="flex flex-col items-start text-left p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 group hover:shadow-lg hover:scale-105 transition-all duration-300"
                    whileHover={{ y: -5 }}
                  >
                    <motion.div
                      className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mb-3"
                      whileHover={{ rotate: [0, -10, 10, 0], scale: 1.1 }}
                      transition={{ duration: 0.5 }}
                    >
                      <Shield className="w-5 h-5 text-white" />
                    </motion.div>
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">
                      {dictionary.enterpriseFeatures?.security?.title}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {dictionary.enterpriseFeatures?.security?.description}
                    </p>
                  </motion.div>

                  <motion.div
                    className="flex flex-col items-start text-left p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 group hover:shadow-lg hover:scale-105 transition-all duration-300"
                    whileHover={{ y: -5 }}
                  >
                    <motion.div
                      className="w-10 h-10 bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg flex items-center justify-center mb-3"
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6 }}
                    >
                      <Settings className="w-5 h-5 text-white" />
                    </motion.div>
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">
                      {dictionary.enterpriseFeatures?.architecture?.title}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {dictionary.enterpriseFeatures?.architecture?.description}
                    </p>
                  </motion.div>

                  <motion.div
                    className="flex flex-col items-start text-left p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 group hover:shadow-lg hover:scale-105 transition-all duration-300"
                    whileHover={{ y: -5 }}
                  >
                    <motion.div
                      className="w-10 h-10 bg-green-700 rounded-lg flex items-center justify-center mb-3"
                      whileHover={{
                        y: [0, -3, 0],
                        scale: 1.1,
                        rotate: [0, 5, -5, 0],
                      }}
                      transition={{ duration: 0.5 }}
                    >
                      <TrendingUp className="w-5 h-5 text-white" />
                    </motion.div>
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">
                      {dictionary.enterpriseFeatures?.roi?.title}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {dictionary.enterpriseFeatures?.roi?.description}
                    </p>
                  </motion.div>

                  <motion.div
                    className="flex flex-col items-start text-left p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 group hover:shadow-lg hover:scale-105 transition-all duration-300"
                    whileHover={{ y: -5 }}
                  >
                    <motion.div
                      className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center mb-3"
                      whileHover={{
                        scale: [1, 1.2, 1],
                        rotate: [0, 180, 360],
                      }}
                      transition={{ duration: 0.7 }}
                    >
                      <Users className="w-5 h-5 text-white" />
                    </motion.div>
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">
                      {dictionary.enterpriseFeatures?.support?.title}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {dictionary.enterpriseFeatures?.support?.description}
                    </p>
                  </motion.div>
                </motion.div>

                {/* Professional CTA */}
                <motion.div variants={itemVariants} className="mb-10">
                  <Link
                    href="https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center gap-3 bg-gradient-to-r from-blue-600 via-blue-600 to-purple-600 hover:from-blue-700 hover:via-blue-700 hover:to-purple-700 dark:from-blue-500 dark:via-blue-500 dark:to-purple-500 dark:hover:from-blue-600 dark:hover:via-blue-600 dark:hover:to-purple-600 text-white font-semibold px-8 py-4 rounded-lg text-base transition-all duration-300 shadow-lg hover:shadow-xl group border border-blue-600"
                  >
                    <span>{dictionary.cta?.consultation}</span>
                    <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-3 text-left">
                    {dictionary.cta?.consultationSubtext}
                  </p>
                </motion.div>
              </div>

              {/* Right Column - Enhanced Single iPhone Mockup */}
              <motion.div
                variants={itemVariants}
                className="relative flex items-center justify-center lg:justify-end order-last lg:order-last"
              >
                {/* Mobile: Show only bottom half of iPhone */}
                <div className="block lg:hidden w-full max-w-lg overflow-hidden h-64">
                  <div className="transform translate-y-[-50%]">
                    <AnimatedHeroPhone className="w-full" dualPhones={false} />
                  </div>
                </div>

                {/* Desktop: Show full iPhone */}
                <div className="hidden lg:block">
                  <AnimatedHeroPhone
                    className="w-full max-w-xl"
                    dualPhones={false}
                  />
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
        <div className="w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20 3xl:px-24 4xl:px-32 relative z-10 pb-12 flex flex-col items-center justify-center">
          <motion.div
            ref={ref}
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-4"
          >
            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium uppercase tracking-wider ">
              {dictionary.companyTrust?.title}
            </p>
          </motion.div>

          {/* Infinite Scrolling Container */}
          <div className="relative overflow-hidden">
            <motion.div
              initial={{ opacity: 0 }}
              animate={inView ? { opacity: 1 } : { opacity: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="flex space-x-6 lg:space-x-8 animate-scroll"
            >
              {/* First set of logos */}
              {companies.map((company, index) => (
                <img
                  key={`first-${index}`}
                  src={company.logo}
                  alt={`${company.name} Logo`}
                  className="flex-shrink-0 h-10 lg:h-12 w-auto object-contain transition-all duration-300 opacity-70 hover:opacity-100 hover:scale-105 rounded-xl min-w-[100px] lg:min-w-[120px]"
                />
              ))}

              {/* Duplicate set for infinite effect */}
              {companies.map((company, index) => (
                <img
                  key={`second-${index}`}
                  src={company.logo}
                  alt={`${company.name} Logo`}
                  className="flex-shrink-0 h-10 lg:h-12 w-auto object-contain transition-all duration-300 opacity-70 hover:opacity-100 hover:scale-105 rounded-xl min-w-[100px] lg:min-w-[120px]"
                />
              ))}

              {/* Third set for seamless loop */}
              {companies.map((company, index) => (
                <img
                  key={`third-${index}`}
                  src={company.logo}
                  alt={`${company.name} Logo`}
                  className="flex-shrink-0 h-10 lg:h-12 w-auto object-contain transition-all duration-300 opacity-70 hover:opacity-100 hover:scale-105 rounded-xl min-w-[100px] lg:min-w-[120px]"
                />
              ))}
            </motion.div>
          </div>
        </div>
      </section>
    </UnifiedBackground>
  );
};