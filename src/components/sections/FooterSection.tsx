'use client'

import React, { useState, useMemo, useCallback } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion, useReducedMotion } from "framer-motion";
import { UnifiedBackground } from "@/components/ui/UnifiedBackground";
import { useI18n } from "@/providers/I18nProvider";
import { type Dictionary } from "@/lib/dictionary";
import {
  Mail,
  Phone,
  Github,
  Linkedin,
  MapPin,
  Clock,
  Shield,
  Award,
  Star,
  Heart,
  Zap,
  CheckCircle,
  ArrowUp,
  Sparkles,
  Globe,
  Users,
  Calendar,
} from "lucide-react";

// Enhanced social media links with modern icons and additional platforms - memoized
const socialLinks = [
  {
    name: "Email",
    icon: <Mail className="w-5 h-5" />,
    url: "mailto:<EMAIL>",
    color: "from-blue-500 to-indigo-600",
    hoverColor: "hover:bg-blue-500/20",
    description: "Send us an email",
  },
  {
    name: "Phone",
    icon: <Phone className="w-5 h-5" />,
    url: "tel:+491759918357",
    color: "from-green-500 to-emerald-600",
    hoverColor: "hover:bg-green-500/20",
    description: "Call us directly",
  },
  {
    name: "GitHub",
    icon: <Github className="w-5 h-5" />,
    url: "https://github.com/Viktor-Hermann/",
    color: "from-gray-700 to-gray-900",
    hoverColor: "hover:bg-gray-500/20",
    description: "Check our code",
  },
  {
    name: "LinkedIn",
    icon: <Linkedin className="w-5 h-5" />,
    url: "https://www.linkedin.com/in/viktor-hermann-103125245/",
    color: "from-blue-600 to-blue-800",
    hoverColor: "hover:bg-blue-600/20",
    description: "Professional network",
  },
];

// Enhanced navigation links with better organization - matching active sections
const navigationLinks = [
  { name: "nav.home", href: "#hero", category: "main" },
  { name: "nav.usp", href: "#usp", category: "main" },
  { name: "nav.services", href: "#services", category: "main" },
  { name: "nav.aienhanced", href: "#ai-enhanced", category: "main" },
  { name: "nav.portfolio", href: "#portfolio", category: "secondary" },
  { name: "nav.pricing", href: "#pricing", category: "secondary" },
  { name: "nav.testimonials", href: "#testimonials", category: "secondary" },
  { name: "nav.contact", href: "#contact", category: "secondary" },
];

// Enhanced services links - memoized
const serviceLinks = [
  { name: "App Development", href: "#services" },
  { name: "KI Integration", href: "#ai-enhanced" },
  { name: "Digital Strategy", href: "#services" },
  { name: "Portfolio Review", href: "#portfolio" },
];

interface FooterSectionProps {
  dictionary: Dictionary["footer"];
  aboutDictionary?: Dictionary["about"];
  locale?: string;
}

export const FooterSection = ({
  dictionary,
  aboutDictionary,
  locale = "en",
}: FooterSectionProps) => {
  const [hoveredSocial, setHoveredSocial] = useState<string | null>(null);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const { t, dir } = useI18n();
  const isRtl = dir === "rtl";

  // Performance optimization: useReducedMotion hook
  const shouldReduceMotion = useReducedMotion();

  // Memoized trust indicators for footer
  const trustIndicators = useMemo(
    () => [
      {
        icon: <Shield className="w-4 h-4" />,
        text: t("about.privacyFocused") || "Privacy Focused",
        color: "text-green-400",
      },
      {
        icon: <Award className="w-4 h-4" />,
        text: t("about.yearsExperience") || "8+ Years Experience",
        color: "text-blue-400",
      },
      {
        icon: <Users className="w-4 h-4" />,
        text: t("about.personalService") || "Personal Service",
        color: "text-purple-400",
      },
      {
        icon: <Star className="w-4 h-4" />,
        text: t("about.qualityFocused") || "Quality Focused",
        color: "text-yellow-400",
      },
    ],
    [t]
  );

  // Memoized animation configuration
  const animationConfig = useMemo(
    () => ({
      floatingOrb1: shouldReduceMotion
        ? {}
        : {
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
            x: [0, 20, 0],
            y: [0, -15, 0],
          },
      floatingOrb2: shouldReduceMotion
        ? {}
        : {
            scale: [1.1, 1, 1.1],
            opacity: [0.2, 0.4, 0.2],
            x: [0, -15, 0],
            y: [0, 15, 0],
          },
      sparkle: shouldReduceMotion
        ? {}
        : {
            rotate: [0, 360],
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2],
          },
    }),
    [shouldReduceMotion]
  );

  // Memoized scroll to top function
  const scrollToTop = useCallback(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  // Handle smooth scrolling for navigation links
  const handleNavClick = useCallback(
    (event: React.MouseEvent<HTMLAnchorElement>, href: string) => {
      // Handle smooth scrolling for anchor links
      if (href.startsWith("#")) {
        event.preventDefault();
        const targetId = href.substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }
    },
    []
  );

  return (
    <UnifiedBackground className="w-full bg-gray-900 dark:bg-gray-900">
      <footer
        className={`w-full text-white relative overflow-hidden bg-gray-900 dark:bg-gray-900 ${
          isRtl ? "rtl-section" : ""
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 py-8 md:py-16 relative z-10">
          {/* Enhanced Trust Indicators Bar - Hidden on mobile */}
          <motion.div
            className="hidden md:flex mb-6 md:mb-12 flex-wrap justify-center items-center gap-3 md:gap-6 lg:gap-10 p-3 md:p-6 rounded-2xl bg-white/5 backdrop-blur-xl border border-white/10 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {trustIndicators.map((indicator, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                className="flex items-center gap-1.5 md:gap-2 text-center"
              >
                <div
                  className={`${indicator.color} bg-white/5 p-1.5 md:p-2 rounded-full`}
                >
                  {indicator.icon}
                </div>
                <span className="text-xs md:text-sm font-medium text-gray-300">
                  {indicator.text}
                </span>
              </motion.div>
            ))}
          </motion.div>

          {/* Mobile Compact Layout */}
          <div className="md:hidden">
            {/* Company Info + Contact in one row */}
            <motion.div
              className="flex flex-col space-y-4 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              {/* Company Name */}
              <div className="text-center">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <Image
                    src="/images/w_company_logo.png"
                    alt="Innovatio Logo"
                    width={32}
                    height={32}
                    priority
                    className="h-auto"
                  />
                  <motion.h2
                    className="text-xl font-semibold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                  >
                    Innovatio-Pro
                  </motion.h2>
                </div>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {dictionary.description || "Creating innovative digital solutions for your business growth."}
                </p>
              </div>

              {/* Compact Contact Row */}
              <div className="flex justify-center gap-4">
                <motion.a
                  href="mailto:<EMAIL>"
                  className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="p-2 rounded-lg bg-blue-500/10">
                    <Mail className="w-4 h-4 text-blue-400" />
                  </div>
                  <span className="text-sm">Email</span>
                </motion.a>

                <motion.a
                  href="tel:+491759918357"
                  className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="p-2 rounded-lg bg-green-500/10">
                    <Phone className="w-4 h-4 text-green-400" />
                  </div>
                  <span className="text-sm">Call</span>
                </motion.a>
              </div>

              {/* Social Links Row */}
              <div className="flex justify-center gap-3">
                {socialLinks.map((social) => (
                  <motion.a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className={`p-2 rounded-lg transition-all duration-300 bg-white/5 ${social.hoverColor} border border-white/10`}
                    aria-label={social.name}
                  >
                    <div className="text-white/70 hover:text-white transition-colors">
                      {React.cloneElement(social.icon, {
                        className: "w-4 h-4",
                      })}
                    </div>
                  </motion.a>
                ))}
              </div>
            </motion.div>

            {/* Mobile Bottom Bar */}
            <motion.div
              className="border-t border-white/10 pt-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex flex-col items-center gap-3">
                <p className="text-gray-400 text-xs text-center">
                  © {new Date().getFullYear()} Innovatio-Pro. All rights
                  reserved.
                </p>

                <div className="flex items-center gap-3 text-xs">
                  <Link
                    href={`/${locale}/privacy-policy`}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Privacy
                  </Link>
                  <span className="text-gray-600">•</span>
                  <Link
                    href={`/${locale}/terms-and-conditions`}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Terms
                  </Link>
                </div>

                {/* Scroll to top button */}
                <motion.button
                  onClick={scrollToTop}
                  className="p-2 rounded-lg bg-white/5 hover:bg-white/10 text-gray-400 hover:text-white transition-all duration-300 border border-white/10"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  title="Scroll to top"
                >
                  <ArrowUp className="w-4 h-4" />
                </motion.button>
              </div>
            </motion.div>
          </div>

          {/* Desktop Full Layout */}
          <div className="hidden md:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 lg:gap-12">
            {/* Enhanced Company info */}
            <motion.div
              className="lg:col-span-2 space-y-4 md:space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div>
                <div className="flex items-center gap-3 mb-2 md:mb-4">
                  <Image
                    src="/images/w_company_logo.png"
                    alt="Innovatio Logo"
                    width={40}
                    height={40}
                    priority
                    className="h-auto"
                  />
                  <motion.h2
                    className="text-xl md:text-2xl font-semibold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                  >
                    Innovatio-Pro
                  </motion.h2>
                </div>
                <p className="text-gray-300 text-sm md:text-lg leading-relaxed max-w-md">
                  {dictionary.description ||
                    "Creating innovative digital solutions that transform businesses and empower growth through cutting-edge technology."}
                </p>
              </div>

              {/* Enhanced Social links */}
              <div>
                <h4 className="text-xs md:text-sm font-semibold text-gray-200 mb-2 md:mb-4 uppercase tracking-wider">
                  {t("footer.connectWithUs") || "Connect With Us"}
                </h4>
                <div className="flex gap-2 md:gap-4">
                  {socialLinks.map((social) => (
                    <motion.a
                      key={social.name}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      onMouseEnter={() => setHoveredSocial(social.name)}
                      onMouseLeave={() => setHoveredSocial(null)}
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      className={`group relative p-2 md:p-3 rounded-xl transition-all duration-300 bg-white/5 ${social.hoverColor} border border-white/10 hover:border-white/20`}
                      aria-label={social.name}
                      title={social.description}
                    >
                      {/* Hover glow effect */}
                      <div
                        className={`absolute inset-0 rounded-xl bg-gradient-to-r ${social.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
                      ></div>

                      <div className="relative text-white/70 group-hover:text-white transition-colors">
                        {social.icon}
                      </div>

                      {/* Tooltip */}
                      {hoveredSocial === social.name && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="absolute -top-12 left-1/2 transform -translate-x-1/2 px-3 py-1 bg-gray-800 text-white text-xs rounded-lg whitespace-nowrap border border-gray-600"
                        >
                          {social.description}
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                        </motion.div>
                      )}
                    </motion.a>
                  ))}
                </div>
              </div>

              {/* Business Hours */}
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-3 md:p-4 border border-white/10">
                <div className="flex items-center gap-2 mb-1 md:mb-2">
                  <Clock className="w-3 md:w-4 h-3 md:h-4 text-green-400" />
                  <h4 className="text-xs md:text-sm font-semibold text-gray-200">
                    {t("footer.businessHours") || "Business Hours"}
                  </h4>
                </div>
                <div className="text-xs md:text-sm text-gray-300 space-y-0.5 md:space-y-1">
                  <div className="flex justify-between">
                    <span>{t("footer.weekdays") || "Monday - Friday:"}</span>
                    <span className="text-green-400">{t("footer.weekdaysTime") || "9:00 AM - 6:00 PM"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t("footer.weekend") || "Weekend:"}</span>
                    <span className="text-yellow-400">{t("footer.byAppointment") || "By Appointment"}</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Enhanced Quick links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h3 className="text-base md:text-lg font-semibold mb-3 md:mb-6 text-white flex items-center gap-2">
                <Globe className="w-4 md:w-5 h-4 md:h-5 text-blue-400" />
                {t("footer.quickLinks") || "Schnelle Links"}
              </h3>
              <div className="space-y-1.5 md:space-y-3">
                <motion.a
                  href="#hero"
                  className="block text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 text-xs md:text-sm group"
                  onClick={(e) => handleNavClick(e, "#hero")}
                  whileHover={{ x: 8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-blue-400 rounded-full group-hover:bg-white transition-colors"></div>
                    {t("nav.home") || "Home"}
                  </div>
                </motion.a>
                <motion.a
                  href="#usp"
                  className="block text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 text-xs md:text-sm group"
                  onClick={(e) => handleNavClick(e, "#usp")}
                  whileHover={{ x: 8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-blue-400 rounded-full group-hover:bg-white transition-colors"></div>
                    {t("nav.usp") || "Warum wir anders sind"}
                  </div>
                </motion.a>
                <motion.a
                  href="#services"
                  className="block text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 text-xs md:text-sm group"
                  onClick={(e) => handleNavClick(e, "#services")}
                  whileHover={{ x: 8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-blue-400 rounded-full group-hover:bg-white transition-colors"></div>
                    {t("nav.services") || "Services"}
                  </div>
                </motion.a>
                <motion.a
                  href="#ai-enhanced"
                  className="block text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 text-xs md:text-sm group"
                  onClick={(e) => handleNavClick(e, "#ai-enhanced")}
                  whileHover={{ x: 8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-blue-400 rounded-full group-hover:bg-white transition-colors"></div>
                    {t("nav.aienhanced") || "KI-Integration"}
                  </div>
                </motion.a>
                <motion.a
                  href="#portfolio"
                  className="block text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 text-xs md:text-sm group"
                  onClick={(e) => handleNavClick(e, "#portfolio")}
                  whileHover={{ x: 8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-blue-400 rounded-full group-hover:bg-white transition-colors"></div>
                    {t("nav.portfolio") || "Portfolio"}
                  </div>
                </motion.a>
                <motion.a
                  href="#pricing"
                  className="block text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 text-xs md:text-sm group"
                  onClick={(e) => handleNavClick(e, "#pricing")}
                  whileHover={{ x: 8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-blue-400 rounded-full group-hover:bg-white transition-colors"></div>
                    {t("nav.pricing") || "Pricing"}
                  </div>
                </motion.a>
                <motion.a
                  href="#testimonials"
                  className="block text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 text-xs md:text-sm group"
                  onClick={(e) => handleNavClick(e, "#testimonials")}
                  whileHover={{ x: 8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-blue-400 rounded-full group-hover:bg-white transition-colors"></div>
                    {t("nav.testimonials") || "Testimonials"}
                  </div>
                </motion.a>
                <motion.a
                  href="#contact"
                  className="block text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 text-xs md:text-sm group"
                  onClick={(e) => handleNavClick(e, "#contact")}
                  whileHover={{ x: 8 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-1 bg-blue-400 rounded-full group-hover:bg-white transition-colors"></div>
                    {t("nav.contact") || "Kontakt"}
                  </div>
                </motion.a>
              </div>

              {/* Services */}
              <div className="mt-4 md:mt-8">
                <h4 className="text-xs md:text-sm font-semibold text-gray-200 mb-2 md:mb-4 uppercase tracking-wider">
                  Services
                </h4>
                <div className="space-y-1 md:space-y-2">
                  {serviceLinks.map((service) => (
                    <motion.a
                      key={service.name}
                      href={service.href}
                      className="block text-gray-400 hover:text-white transition-colors duration-300 text-xs md:text-sm"
                      onClick={(e) => handleNavClick(e, service.href)}
                      whileHover={{ x: 4 }}
                    >
                      {service.name}
                    </motion.a>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Enhanced Contact info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <h3 className="text-base md:text-lg font-semibold mb-3 md:mb-6 text-white flex items-center gap-2">
                <Mail className="w-4 md:w-5 h-4 md:h-5 text-green-400" />
                {t("contact.title") || "Kontakt"}
              </h3>
              <div className="space-y-2 md:space-y-4">
                <motion.a
                  href="mailto:<EMAIL>"
                  className="group flex items-center gap-2 md:gap-3 text-gray-400 hover:text-white transition-all duration-300"
                  whileHover={{ x: 4 }}
                >
                  <div className="p-1.5 md:p-2 rounded-lg bg-blue-500/10 group-hover:bg-blue-500/20 transition-colors">
                    <Mail className="w-3 md:w-4 h-3 md:h-4 text-blue-400" />
                  </div>
                  <div>
                    <div className="text-xs md:text-sm font-medium">Email</div>
                    <div className="text-xs text-gray-500">
                      <EMAIL>
                    </div>
                  </div>
                </motion.a>

                <motion.a
                  href="tel:+491759918357"
                  className="group flex items-center gap-2 md:gap-3 text-gray-400 hover:text-white transition-all duration-300"
                  whileHover={{ x: 4 }}
                >
                  <div className="p-1.5 md:p-2 rounded-lg bg-green-500/10 group-hover:bg-green-500/20 transition-colors">
                    <Phone className="w-3 md:w-4 h-3 md:h-4 text-green-400" />
                  </div>
                  <div>
                    <div className="text-xs md:text-sm font-medium">Phone</div>
                    <div className="text-xs text-gray-500">+49 175 9918357</div>
                  </div>
                </motion.a>

                <div className="flex items-center gap-2 md:gap-3 text-gray-400">
                  <div className="p-1.5 md:p-2 rounded-lg bg-purple-500/10">
                    <MapPin className="w-3 md:w-4 h-3 md:h-4 text-purple-400" />
                  </div>
                  <div>
                    <div className="text-xs md:text-sm font-medium">
                      Location
                    </div>
                    <div className="text-xs text-gray-500">Wyoming, USA</div>
                  </div>
                </div>
              </div>

              {/* Call to Action */}
              <div className="mt-4 md:mt-8 p-3 md:p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl border border-white/10">
                <h4 className="text-xs md:text-sm font-semibold text-white mb-1 md:mb-2">
                  Ready to Start?
                </h4>
                <p className="text-xs text-gray-400 mb-2 md:mb-3">
                  Let's discuss your project
                </p>
                <motion.a
                  href="#contact"
                  className="inline-flex items-center gap-1.5 md:gap-2 px-3 md:px-4 py-1.5 md:py-2 bg-gradient-to-r from-[#1e3a8a] to-[#1d4ed8] hover:from-[#1d4ed8] hover:to-[#2563eb] text-white text-xs md:text-sm font-semibold rounded-lg transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl ring-1 ring-[#1e3a8a]/20 hover:ring-[#1e3a8a]/40"
                  onClick={(e) => handleNavClick(e, "#contact")}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Calendar className="w-3 md:w-4 h-3 md:h-4" />
                  Get Started
                </motion.a>
              </div>
            </motion.div>
          </div>

          {/* Desktop Bottom bar */}
          <motion.div
            className="hidden md:block mt-8 md:mt-16 pt-4 md:pt-8 border-t border-white/10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <div className="flex flex-col lg:flex-row justify-between items-center gap-3 md:gap-6">
              <div className="flex flex-col md:flex-row items-center gap-2 md:gap-4">
                <p className="text-gray-400 text-xs md:text-sm text-center">
                  {dictionary.copyright ||
                    `© ${new Date().getFullYear()} Innovatio-Pro. All rights reserved.`}
                </p>

                {/* Privacy & Terms */}
                <div className="flex items-center gap-2 md:gap-4 text-xs md:text-sm">
                  <Link
                    href={`/${locale}/privacy-policy`}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Privacy Policy
                  </Link>
                  <span className="text-gray-600">•</span>
                  <Link
                    href={`/${locale}/terms-and-conditions`}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Terms of Service
                  </Link>
                </div>
              </div>

              {/* Enhanced Built with note */}
              <div className="flex items-center gap-2 md:gap-4">
                <div className="flex items-center gap-1.5 md:gap-2 text-gray-400 text-xs md:text-sm">
                  <Heart className="w-3 md:w-4 h-3 md:h-4 text-red-400" />
                  <span>{t("footer.builtWith") || "Built with"}</span>
                  <span className="text-blue-400 font-medium">React</span>
                  <span>{t("footer.and") || "&"}</span>
                  <span className="text-purple-400 font-medium">Next.js</span>
                </div>

                {/* Scroll to top button */}
                <motion.button
                  onClick={scrollToTop}
                  className="p-1.5 md:p-2 rounded-lg bg-white/5 hover:bg-white/10 text-gray-400 hover:text-white transition-all duration-300 border border-white/10 hover:border-white/20"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  title="Scroll to top"
                >
                  <ArrowUp className="w-3 md:w-4 h-3 md:h-4" />
                </motion.button>
              </div>
            </div>

            {/* Achievement badges */}
            <div className="mt-3 md:mt-6 flex flex-wrap justify-center gap-2 md:gap-4">
              <div className="flex items-center gap-1.5 md:gap-2 px-2 md:px-3 py-0.5 md:py-1 bg-white/5 rounded-full text-xs text-gray-400">
                <CheckCircle className="w-2.5 md:w-3 h-2.5 md:h-3 text-green-400" />
                <span>Privacy Respected</span>
              </div>
              <div className="flex items-center gap-1.5 md:gap-2 px-2 md:px-3 py-0.5 md:py-1 bg-white/5 rounded-full text-xs text-gray-400">
                <Shield className="w-2.5 md:w-3 h-2.5 md:h-3 text-blue-400" />
                <span>Secure Development</span>
              </div>
              <div className="flex items-center gap-1.5 md:gap-2 px-2 md:px-3 py-0.5 md:py-1 bg-white/5 rounded-full text-xs text-gray-400">
                <Award className="w-2.5 md:w-3 h-2.5 md:h-3 text-purple-400" />
                <span>Personal Attention</span>
              </div>
            </div>
          </motion.div>
        </div>
      </footer>
    </UnifiedBackground>
  );
};