'use client'

import { Section } from '@/components/ui/Section';
import { type Dictionary } from '@/lib/dictionary';
import { useMemo } from "react";
import { cn } from "@/lib/utils";
// Image import removed - no longer needed
import {
  CheckCircle,
  Sparkles,
  Star,
  Lightbulb,
  Target,
  User,
  // Chevron icons removed - no longer needed
  Award,
  // Shield icon removed - no longer needed
  Clock,
  TrendingUp,
  // Calendar and Folder icons removed - no longer needed
  Users,
  // Heart icon removed - no longer needed
} from "lucide-react";
import { motion, useReducedMotion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { UnifiedBackground } from "@/components/ui/UnifiedBackground";
// Marquee import removed - no longer needed

interface AboutSectionProps {
  dictionary: Dictionary["about"];
}

// ClientCard component removed - no longer needed

export const AboutSection = ({ dictionary }: AboutSectionProps) => {
  const shouldReduceMotion = useReducedMotion();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.01,
    rootMargin: "0px 0px 600px 0px",
  });

  const fadeInUp = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 },
  };

  const fadeInLeft = {
    hidden: { opacity: 0, x: -30 },
    visible: { opacity: 1, x: 0 },
  };

  const fadeInRight = {
    hidden: { opacity: 0, x: 30 },
    visible: { opacity: 1, x: 0 },
  };

  // Memoized stats array
  const stats = useMemo(
    () => [
      {
        value: dictionary.metrics?.yearsExperience || "8+",
        label: dictionary.experience,
      },
      {
        value: dictionary.metrics?.projectsCompleted || "15+",
        label: dictionary.projectsCompleted,
      },
      {
        value: dictionary.metrics?.happyClients || "12+",
        label: dictionary.clients,
      },
      {
        value: "100%",
        label: dictionary.dedication || "Dedication",
      },
    ],
    [
      dictionary.metrics?.yearsExperience,
      dictionary.metrics?.projectsCompleted,
      dictionary.metrics?.happyClients,
      dictionary.experience,
      dictionary.projectsCompleted,
      dictionary.clients,
      dictionary.dedication,
    ]
  );

  // Memoized differentiators
  const differentiators = useMemo(
    () => [
      {
        icon: TrendingUp,
        title:
          dictionary?.reasons?.fastDelivery?.title ||
          "Lightning Fast Onboarding",
        description:
          dictionary?.reasons?.fastDelivery?.description ||
          "In every project, I take the leadership role in the shortest time and set new standards.",
      },
      {
        icon: Clock,
        title: dictionary?.reasons?.founderInvolved?.title || "24/7 Dedication",
        description:
          dictionary?.reasons?.founderInvolved?.description ||
          "I can't switch off until the problem is solved and the customer is thrilled.",
      },
      {
        icon: Users,
        title:
          dictionary?.reasons?.flutterSpecialization?.title ||
          "Teamplayer & Leadership",
        description:
          dictionary?.reasons?.flutterSpecialization?.description ||
          "Authentic, funny and full of energy - I fit into any team and lead it to success.",
      },
      {
        icon: Award,
        title:
          dictionary?.reasons?.scalableArchitecture?.title ||
          "Customer Favorite",
        description:
          dictionary?.reasons?.scalableArchitecture?.description ||
          "Every customer was overwhelmed by my speed and quality - even as an external consultant I was invited to company events.",
      },
    ],
    [dictionary]
  );

  // Clients data removed - no longer needed

  return (
    <UnifiedBackground className="w-full">
      <div ref={ref}>
        <Section
        titleClassName="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white text-center mb-6 leading-tight"
        subtitleClassName="text-lg md:text-xl text-gray-600 dark:text-gray-300 text-center mb-8 max-w-3xl mx-auto leading-relaxed"
        title={
          <span>
            {(dictionary?.title || "Warum ich anders bin")
              .split(" ")
              .map((word, index) => {
                const isColoredWord =
                  word.toLowerCase().includes("anders") ||
                  word.toLowerCase().includes("warum");
                return (
                  <span key={index}>
                    {isColoredWord ? (
                      <span className="text-[#1e3a8a] dark:text-blue-400">
                        {word}
                      </span>
                    ) : (
                      word
                    )}
                    {index <
                      (dictionary?.title || "Warum ich anders bin").split(" ")
                        .length -
                        1 && " "}
                  </span>
                );
              })}
          </span>
        }
        subtitle={
          dictionary?.subtitle || "Der Entwickler, der den Unterschied macht"
        }
        className="py-20 md:py-28 bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden"
        id="about"
      >
        {/* Simplified background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-transparent to-purple-50/20 dark:from-blue-900/10 dark:via-transparent dark:to-purple-900/10"></div>

        <div className="max-w-7xl mx-auto px-4 relative z-10">
          {/* Motto Section - directly under subtitle */}
          <div className="text-center mb-12">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 dark:from-blue-400/20 dark:to-purple-400/20 blur-xl rounded-full"></div>
              <div className="relative">
                <h2 className="text-xl md:text-2xl font-bold leading-tight text-gray-900 dark:text-white">
                  <span>
                    {(dictionary?.motto || '"You never lose. Either you win, or you learn."').split(" ").map((word, index) => {
                      const isColoredWord = word.toLowerCase().includes("win") || word.toLowerCase().includes("learn") || word.toLowerCase().includes("never");
                      return (
                        <span key={index}>
                          {isColoredWord ? (
                            <span className="text-[#1e3a8a] dark:text-blue-400">
                              {word}
                            </span>
                          ) : (
                            word
                          )}
                          {index < (dictionary?.motto || '"You never lose. Either you win, or you learn."').split(" ").length - 1 && " "}
                        </span>
                      );
                    })}
                  </span>
                </h2>
              </div>
            </div>
            <div className="w-20 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full mt-4"></div>
          </div>

          <div className="grid md:grid-cols-2 gap-8 md:gap-16 items-stretch">
            {/* Left column: Image and info */}
            <motion.div
              initial="hidden"
              animate={inView ? "visible" : "hidden"}
              variants={fadeInLeft}
              transition={{ duration: 0.6, delay: 0 }}
              className="overflow-hidden rounded-2xl shadow-2xl dark:shadow-none relative min-h-[300px] sm:min-h-[350px] md:min-h-[450px] group flex flex-col"
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/10 to-transparent z-10 pointer-events-none" />

              <img
                src="/images/founder_small.png"
                alt={dictionary?.founderTitle ?? "Founder"}
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                loading="lazy"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src =
                    "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1974&q=80";
                }}
              />
              <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 md:p-8 z-20 text-white rounded-b-2xl">
                <h3 className="text-xl sm:text-2xl font-bold mb-1 sm:mb-2 flex items-center">
                  <span className="bg-primary/20 w-8 h-8 rounded-full flex items-center justify-center mr-2">
                    <User className="h-4 w-4 text-primary-foreground" />
                  </span>
                  {dictionary?.founderName || "Hi, I'm Viktor"}
                </h3>
                <p className="text-white text-xs sm:text-sm leading-relaxed font-normal">
                  {dictionary?.founderRole ||
                    "Mobile App Developer specializing in modern, efficient solutions"}
                </p>

                {/* Simplified stats row */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mt-4">
                  {stats.map((stat, index) => (
                    <div
                      key={index}
                      className="text-center bg-black/40 backdrop-blur-sm rounded-lg p-2 sm:p-3 flex-1 transition-colors border border-white/10"
                    >
                      <div className="text-xl sm:text-2xl font-semibold text-white dark:text-blue-400">
                        {stat.value}
                      </div>
                      <div className="text-xs text-white font-medium">
                        {stat.label}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Right column: About Text */}
            <motion.div
              initial="hidden"
              animate={inView ? "visible" : "hidden"}
              variants={fadeInRight}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="space-y-6 flex flex-col"
            >
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg dark:shadow-none border border-gray-100 dark:border-gray-700/30 p-6 md:p-8 flex-1 flex flex-col justify-center">
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-lg md:text-xl font-semibold text-gray-900 dark:text-white mb-3">
                      {dictionary?.whyDifferentTitle ||
                        "Was mich von anderen unterscheidet"}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed text-base">
                      {dictionary?.whyDifferentText ||
                        "Während andere noch diskutieren, entwickle ich bereits. Meine Leidenschaft für Perfektion und meine Unfähigkeit abzuschalten, bis der Kunde begeistert ist, haben mich überall zum Lead Mobile App Developer gemacht - egal ob bei Lufthansa, Union Investment oder anderen Unternehmen."}
                    </p>
                  </div>

                  {/* Differentiators - Cool Asymmetric Layout */}
                  <div className="mt-6 space-y-3">
                    {/* First row - Large card spanning full width */}
                    <div className="group relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-900/20 dark:via-gray-800 dark:to-purple-900/20 rounded-xl p-4 border border-blue-100 dark:border-blue-800/30 hover:shadow-lg hover:scale-[1.01] transition-all duration-300 cursor-pointer">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative z-10 flex items-center gap-3">
                        <div className="bg-gradient-to-br from-blue-500 to-purple-600 w-10 h-10 rounded-xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-300 shadow-md">
                          <TrendingUp className="text-white h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-base">
                            {differentiators[0].title}
                          </h4>
                          <p className="text-gray-600 dark:text-gray-300 leading-relaxed text-sm">
                            {differentiators[0].description}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Second row - Two medium cards */}
                    <div className="grid md:grid-cols-2 gap-3">
                      <div className="group relative overflow-hidden bg-gradient-to-br from-green-50 via-white to-emerald-50 dark:from-green-900/20 dark:via-gray-800 dark:to-emerald-900/20 rounded-lg p-4 border border-green-100 dark:border-green-800/30 hover:shadow-md hover:scale-[1.02] transition-all duration-300 cursor-pointer">
                        <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-green-400/10 to-emerald-400/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-500"></div>
                        <div className="relative z-10">
                          <div className="bg-gradient-to-br from-green-500 to-emerald-600 w-9 h-9 rounded-lg flex items-center justify-center mb-3 group-hover:rotate-3 transition-transform duration-300 shadow-sm">
                            <Clock className="text-white h-4 w-4" />
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-sm">
                            {differentiators[1].title}
                          </h4>
                          <p className="text-gray-600 dark:text-gray-300 text-xs leading-relaxed">
                            {differentiators[1].description}
                          </p>
                        </div>
                      </div>

                      <div className="group relative overflow-hidden bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-orange-900/20 dark:via-gray-800 dark:to-red-900/20 rounded-lg p-4 border border-orange-100 dark:border-orange-800/30 hover:shadow-md hover:scale-[1.02] transition-all duration-300 cursor-pointer">
                        <div className="absolute bottom-0 left-0 w-14 h-14 bg-gradient-to-tr from-orange-400/10 to-red-400/10 rounded-full translate-y-6 -translate-x-6 group-hover:scale-125 transition-transform duration-500"></div>
                        <div className="relative z-10">
                          <div className="bg-gradient-to-br from-orange-500 to-red-600 w-9 h-9 rounded-lg flex items-center justify-center mb-3 group-hover:-rotate-3 transition-transform duration-300 shadow-sm">
                            <Users className="text-white h-4 w-4" />
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-sm">
                            {differentiators[2].title}
                          </h4>
                          <p className="text-gray-600 dark:text-gray-300 text-xs leading-relaxed">
                            {differentiators[2].description}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Third row - One special highlight card */}
                    <div className="group relative overflow-hidden bg-gradient-to-br from-purple-50 via-white to-pink-50 dark:from-purple-900/20 dark:via-gray-800 dark:to-pink-900/20 rounded-xl p-4 border border-purple-100 dark:border-purple-800/30 hover:shadow-lg hover:scale-[1.01] transition-all duration-300 cursor-pointer">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute top-3 right-3 w-16 h-16 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full group-hover:scale-110 group-hover:rotate-12 transition-all duration-400"></div>
                      <div className="relative z-10 flex items-center gap-3">
                        <div className="bg-gradient-to-br from-purple-500 to-pink-600 w-10 h-10 rounded-xl flex items-center justify-center group-hover:rotate-6 group-hover:scale-105 transition-transform duration-300 shadow-md">
                          <Award className="text-white h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-base">
                            {differentiators[3].title}
                          </h4>
                          <p className="text-gray-600 dark:text-gray-300 leading-relaxed text-sm">
                            {differentiators[3].description}
                          </p>
                        </div>
                        <div className="hidden md:block text-2xl opacity-20 group-hover:opacity-30 transition-opacity duration-300">
                          🏆
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* My Secret Section - below the grid */}
          <div className="mt-16">
            <motion.div
              initial="hidden"
              animate={inView ? "visible" : "hidden"}
              variants={fadeInUp}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="w-full"
            >
              <div className="max-w-4xl mx-auto">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl border border-blue-100 dark:border-blue-800/30 p-8 md:p-12">
                  <div className="text-center">
                    <h3 className="text-xl md:text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                      <span className="bg-gradient-to-r from-slate-900 via-slate-700 to-slate-900 dark:from-white dark:via-slate-100 dark:to-white bg-clip-text text-transparent">
                        {dictionary?.secretTitle || "Mein Geheimnis?"}
                      </span>
                    </h3>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed text-base md:text-lg">
                      {dictionary?.secretText ||
                        "Ich bringe nicht nur technische Exzellenz mit, sondern auch die Energie und Authentizität, die Teams zum Leben erweckt. Deshalb wurde ich als Externer zu jedem Firmenevent eingeladen und bin bei jedem Kunden in Erinnerung geblieben."}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </Section>
      </div>
    </UnifiedBackground>
  );
};