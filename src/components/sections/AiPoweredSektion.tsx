"use client";

import React from "react";
import { Section } from "@/components/ui/Section";
import { StaticBackground } from "@/components/ui/StaticBackground";
import { motion, Variants, useReducedMotion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Zap,
  Shield,
  Sparkles,
  Bot,
  Code2,
  CheckCircle2,
  Brain,
  Rocket,
  LineChart,
  ArrowRight,
} from "lucide-react";

interface AIPoweredDevelopmentSectionProps {
  // The dictionary object is optional and may vary in structure across locales.
  // Using a loose type to avoid TypeScript errors when certain keys are missing.
  dictionary?: Record<string, any>;
}

// Animation variants
const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

const itemVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

const cardVariants: Variants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
  hover: {
    scale: 1.02,
    y: -5,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
};

// Default dictionary fallback
const defaultDictionary = {
  featuresTitle: "How We Use AI to Deliver Superior Results",
  title: "Development of the Future: AI-Enhanced Coding",
  subtitle: "When Senior Expertise Meets Cutting-Edge AI Tools",
  description:
    "We combine 8+ years of development experience with the latest AI tools to deliver exceptional results. Every line of code is optimized, tested, and production-ready.",
  benefits: {
    speed: {
      title: "40% Faster Development",
      description:
        "AI-assisted code generation significantly accelerates programming",
      icon: "Zap",
    },
    quality: {
      title: "90% Fewer Bugs",
      description:
        "AI-powered code reviews catch errors before they become problems",
      icon: "Shield",
    },
    innovation: {
      title: "Premium Code Quality",
      description: "Best practices and patterns automatically implemented",
      icon: "Sparkles",
    },
  },
  features: [
    {
      title: "Claude & Cursor AI Integration",
      description:
        "Context-aware code completion and generation without vendor lock-in",
      detail:
        "Faster implementation of complex features using the newest AI tooling",
    },
    {
      title: "AI Code Reviews",
      description: "Automated quality checks with every commit",
      detail: "Consistent code quality guaranteed",
    },
    {
      title: "Smart Testing",
      description: "AI-generated test cases for edge scenarios",
      detail: "Higher test coverage, fewer surprises",
    },
    {
      title: "Performance Optimization",
      description: "AI analyzes and optimizes critical code paths",
      detail: "Faster, more efficient applications",
    },
    {
      title: "Automated Documentation",
      description: "AI generates comprehensive docs and comments",
      detail: "Self-documenting code for better maintainability",
    },
    {
      title: "Intelligent Refactoring",
      description: "AI suggests and implements code improvements",
      detail: "Cleaner, more maintainable codebase evolution",
    },
  ],
  stats: {
    codeQuality: {
      value: "99.9%",
      label: "Code Quality Score",
    },
    timeReduction: {
      value: "40%",
      label: "Time Reduction",
    },
    bugPrevention: {
      value: "85%",
      label: "Bugs Prevented",
    },
    satisfaction: {
      value: "100%",
      label: "Client Satisfaction",
    },
  },
  cta: {
    title: "Ready for AI-Enhanced Development?",
    description: "Experience the future of software development today",
    primaryButton: "Let's Get In Touch",
  },
};

export const AIPoweredDevelopmentSection: React.FC<
  AIPoweredDevelopmentSectionProps
> = ({ dictionary }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const shouldReduceMotion = useReducedMotion();
  const dict = dictionary || defaultDictionary;

  // Icon mapping
  const iconMap = {
    Zap: Zap,
    Shield: Shield,
    Sparkles: Sparkles,
    Bot: Bot,
    Code2: Code2,
    Brain: Brain,
  };

  const benefitsInput = dict.benefits || defaultDictionary.benefits;
  const benefits =
    benefitsInput && (Object.values(benefitsInput as any)[0] as any)?.title
      ? (benefitsInput as Record<
          string,
          { title: string; description: string; icon: string }
        >)
      : (defaultDictionary.benefits as Record<
          string,
          { title: string; description: string; icon: string }
        >);

  const featuresInput = dict.features || defaultDictionary.features;
  let features: Array<{ title: string; description: string; detail: string }>;
  if (Array.isArray(featuresInput)) {
    const first = (featuresInput as any)[0];
    if (typeof first === "object" && first && "title" in first) {
      features = featuresInput as Array<{
        title: string;
        description: string;
        detail: string;
      }>;
    } else {
      // Assume array of plain strings – convert to objects with title only
      features = (featuresInput as string[]).map((str) => ({
        title: str,
        description: "",
        detail: "",
      }));
    }
  } else {
    features = defaultDictionary.features as Array<{
      title: string;
      description: string;
      detail: string;
    }>;
  }

  const statsInput = dict.stats || defaultDictionary.stats;
  const stats =
    statsInput && (Object.values(statsInput as any)[0] as any)?.value
      ? (statsInput as Record<string, { value: string; label: string }>)
      : (defaultDictionary.stats as Record<
          string,
          { value: string; label: string }
        >);

  const ctaInput = dict.cta || defaultDictionary.cta;

  return (
    <StaticBackground className="w-full">
      <Section
        id="ai-development"
        className="py-20 md:py-28 relative overflow-hidden"
      >
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/20 to-cyan-400/20 rounded-full blur-3xl"></div>
        </div>

        <div className="w-full px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            ref={ref}
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="w-full"
          >
            {/* Header Section */}
            <motion.div variants={itemVariants} className="text-center mb-16">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-600 dark:text-blue-400 text-sm font-medium mb-6">
                <Sparkles className="w-4 h-4" />
                <span>
                  {dict.featuresTitle || defaultDictionary.featuresTitle}
                </span>
              </div>
              <h2 className="text-3xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
                {dict.title || defaultDictionary.title}
              </h2>
              <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
                {dict.subtitle || defaultDictionary.subtitle}
              </p>
            </motion.div>
            {/* Benefits Section - Compact Cards */}
            <motion.div
              variants={itemVariants}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16"
            >
              {Object.entries(benefits).map(([key, benefit], index) => {
                const IconComponent =
                  iconMap[benefit.icon as keyof typeof iconMap] || Sparkles;
                const gradients = [
                  "from-blue-500 to-indigo-600",
                  "from-emerald-500 to-teal-600",
                  "from-purple-500 to-pink-600",
                ];
                const bgGradients = [
                  "from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",
                  "from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20",
                  "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
                ];

                return (
                  <motion.div
                    key={key}
                    variants={cardVariants}
                    whileHover="hover"
                    className={`relative p-4 sm:p-6 rounded-2xl bg-gradient-to-br ${bgGradients[index]} border border-white/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-500 group overflow-hidden`}
                  >
                    {/* Background glow effect */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${gradients[index]} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-2xl`}
                    ></div>

                    {/* Icon */}
                    <div
                      className={`w-14 h-14 sm:w-12 sm:h-12 rounded-xl bg-gradient-to-br ${gradients[index]} flex items-center justify-center mb-3 sm:mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                    >
                      <IconComponent className="w-7 h-7 sm:w-6 sm:h-6 text-white" />
                    </div>

                    {/* Content */}
                    <div className="relative z-10">
                      <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-2">
                        {benefit.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </motion.div>
                );
              })}
            </motion.div>
            {/* How We Use AI - Compact */}
            <motion.div variants={itemVariants} className="mb-12">
              <div className="text-center mb-8">
                <h3 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  How We Use AI
                </h3>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {features.map((feature, index) => {
                  const icons = [
                    Bot,
                    Code2,
                    Shield,
                    Rocket,
                    CheckCircle2,
                    Brain,
                  ];
                  const IconComponent = icons[index] || Bot;
                  const gradients = [
                    "from-violet-500 to-purple-600",
                    "from-cyan-500 to-blue-600",
                    "from-green-500 to-emerald-600",
                    "from-orange-500 to-red-600",
                    "from-rose-500 to-pink-600",
                    "from-amber-500 to-yellow-600",
                  ];

                  return (
                    <motion.div
                      key={index}
                      variants={cardVariants}
                      whileHover="hover"
                      className="group relative"
                    >
                      <div className="relative p-4 rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/50 shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden">
                        {/* Icon and title row */}
                        <div className="flex items-center gap-3 mb-2 relative z-10">
                          <div
                            className={`w-10 h-10 sm:w-8 sm:h-8 rounded-lg bg-gradient-to-br ${gradients[index]} flex items-center justify-center shadow-md`}
                          >
                            <IconComponent className="w-5 h-5 sm:w-4 sm:h-4 text-white" />
                          </div>
                          <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                            {feature.title}
                          </h4>
                        </div>

                        <p className="text-xs text-gray-600 dark:text-gray-300 leading-relaxed relative z-10">
                          {feature.description}
                        </p>

                        {/* Hover glow */}
                        <div
                          className={`absolute inset-0 bg-gradient-to-r ${gradients[index]} opacity-0 group-hover:opacity-5 transition-opacity duration-300 rounded-xl`}
                        ></div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </motion.div>
            ;{/* Stats Section - Enhanced */}
            <motion.div variants={itemVariants} className="mb-16">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {Object.entries(stats).map(([key, stat], index) => {
                  const gradients = [
                    "from-blue-500 to-indigo-600",
                    "from-emerald-500 to-teal-600",
                    "from-purple-500 to-pink-600",
                    "from-orange-500 to-red-600",
                  ];

                  return (
                    <motion.div
                      key={key}
                      variants={cardVariants}
                      whileHover="hover"
                      className="text-center p-6 rounded-2xl bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 group"
                    >
                      <div
                        className={`text-3xl md:text-4xl font-bold bg-gradient-to-r ${gradients[index]} bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300`}
                      >
                        {stat.value}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                        {stat.label}
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </motion.div>
            ;{/* CTA Section - Simple */}
            <motion.div variants={itemVariants} className="text-center">
              <h3 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-4">
                {ctaInput.title || defaultDictionary.cta.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6 text-base">
                {ctaInput.description || defaultDictionary.cta.description}
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() =>
                  window.open(
                    "https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01",
                    "_blank"
                  )
                }
                className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Sparkles className="w-4 h-4" />
                {ctaInput.primaryButton || defaultDictionary.cta.primaryButton}
              </motion.button>
            </motion.div>
          </motion.div>
        </div>
      </Section>
    </StaticBackground>
  );
};