'use client'

import React, { useState } from "react";
import {
  Check,
  X,
  Info,
  Brain,
  Code,
  Shield,
  Target,
  Rocket,
  TrendingUp,
  Calendar,
  Database,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { UnifiedBackground } from '@/components/ui/UnifiedBackground';
import { useI18n } from '@/providers/I18nProvider';
import { type Dictionary } from '@/lib/dictionary';

interface PricingSectionProps {
  dictionary?: Dictionary['prices'];
  packages?: Dictionary['packages'];
}

const PricingSection = ({ dictionary, packages }: PricingSectionProps) => {
  const { dir } = useI18n();
  const isRtl = dir === "rtl";
  const [activeInfoModal, setActiveInfoModal] = useState<string | null>(null);

  // Icon mapping function since JSON can't store React components
  const getIconForPackage = (packageKey: string) => {
    const iconMap: { [key: string]: any } = {
      prototype: Target,
      mvp: Rocket,
      professional: TrendingUp,
      enterprise: Shield,
    };
    return iconMap[packageKey] || Target;
  };

  // Always show exactly the same 4 packages as German version
  const mainPackageKeys = ['prototype', 'mvp', 'professional', 'enterprise'];
  
  const pricingPackages = packages ? Object.fromEntries(
    mainPackageKeys.map(key => {
      if (packages[key]) {
        return [key, packages[key]];
      }
      return null;
    }).filter(Boolean)
  ) : {};

  // Check if we have the main packages after filtering
  if (!packages || Object.keys(pricingPackages).length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px] bg-gray-50 dark:bg-gray-900">
        <p className="text-gray-600 dark:text-gray-400">
          Pricing packages not available for this language
        </p>
      </div>
    );
  }

  const InfoModal = ({
    packageKey,
    packageData,
  }: {
    packageKey: string;
    packageData: any;
  }) => (
    <AnimatePresence>
      {activeInfoModal === packageKey && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={() => setActiveInfoModal(null)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                {packageData.title} - {dictionary?.fullDetails || "Full Details"}
              </h3>
              <button
                onClick={() => setActiveInfoModal(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  {dictionary?.description || "Beschreibung"}
                </h4>
                <p className="text-gray-600 dark:text-gray-300">
                  {packageData.description}
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  {dictionary?.allIncludedFeatures || "All Included Features"}
                </h4>
                <ul className="space-y-2">
                  {packageData.detailedFeatures?.map(
                    (feature: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 dark:text-gray-300 text-sm">
                          {feature}
                        </span>
                      </li>
                    )
                  ) || []}
                </ul>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">
                  {dictionary?.backendOptions || "Backend Options"}
                </h4>
                <div className="flex items-center gap-4 text-sm text-blue-700 dark:text-blue-300">
                  <div className="flex items-center gap-1">
                    <Database className="w-4 h-4" />
                    <span>Firebase</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Database className="w-4 h-4" />
                    <span>Supabase</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  const PackageCard = ({
    packageKey,
    packageData,
  }: {
    packageKey: string;
    packageData: any;
  }) => {
    const IconComponent = packageData.icon || getIconForPackage(packageKey);
    const isPopular = packageKey === "mvp";

    return (
      <motion.div
        className={`relative overflow-hidden p-6 rounded-2xl shadow-lg border-2 transition-all duration-300 flex flex-col h-full ${
          isPopular
            ? "border-green-500 bg-gradient-to-br from-green-500/10 to-transparent"
            : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-500"
        }`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        whileHover={{ y: -5 }}
      >
        {/* Badge - inside card */}
        {isPopular && (
          <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold mb-4 self-start">
            🚀 {packageData.badge}
          </div>
        )}

        {/* Header - Column Layout */}
        <div className="mb-4">
          {/* Icon */}
          <div className="mb-4">
            <IconComponent
              className={`w-8 h-8 ${
                packageKey === "prototype"
                  ? "text-blue-600 dark:text-blue-400"
                  : packageKey === "mvp"
                    ? "text-green-600 dark:text-green-400"
                    : packageKey === "professional"
                      ? "text-purple-600 dark:text-purple-400"
                      : "text-indigo-600 dark:text-indigo-400"
              }`}
            />
          </div>

          {/* Title */}
          <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
            {packageData.title}
          </h3>

          {/* Price */}
          <div
            className={`text-2xl font-bold mb-2 ${
              packageKey === "prototype"
                ? "text-blue-600 dark:text-blue-400"
                : packageKey === "mvp"
                  ? "text-green-600 dark:text-green-400"
                  : packageKey === "professional"
                    ? "text-purple-600 dark:text-purple-400"
                    : "text-indigo-600 dark:text-indigo-400"
            }`}
          >
            {packageData.price}
          </div>

          {/* Duration */}
          <div className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            {packageData.timeframe}
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 leading-relaxed">
          {packageData.description}
        </p>

        {/* Key Features */}
        <div className="flex-grow">
          <ul className="space-y-2 mb-6 text-sm">
            {packageData.keyFeatures?.map((feature: string, index: number) => (
              <li key={index} className="flex items-start gap-3">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">
                  {feature}
                </span>
              </li>
            )) || []}
          </ul>
        </div>

        {/* Bottom section with buttons */}
        <div className="mt-auto space-y-3">
          {/* Details Button */}
          <button
            onClick={() => setActiveInfoModal(packageKey)}
            className="w-full py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 flex items-center justify-center gap-2"
          >
            <Info className="w-4 h-4" />
            {dictionary?.showDetails || "Show Details"}
          </button>

          {/* CTA Button */}
          <motion.button
            className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 text-white ${
              packageKey === "prototype"
                ? "bg-blue-600 hover:bg-blue-700"
                : packageKey === "mvp"
                  ? "bg-green-600 hover:bg-green-700"
                  : packageKey === "professional"
                    ? "bg-purple-600 hover:bg-purple-700"
                    : "bg-indigo-600 hover:bg-indigo-700"
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {packageData.cta} →
          </motion.button>
        </div>
      </motion.div>
    );
  };

  return (
    <UnifiedBackground className="w-full py-16 md:py-24 lg:py-32">
      <section
        className={`w-full relative overflow-hidden ${isRtl ? "rtl-section" : ""}`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Header */}
          <motion.div
            className="text-center mb-12 md:mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              className="inline-flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium mb-4"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              💰 {dictionary?.title || "Transparente Preise"}
            </motion.div>

            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4 md:mb-6">
              {dictionary?.subtitle || "Apps die Ihr Business transformieren"}
            </h2>

            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8 md:mb-12">
              {dictionary?.description ||
                "Von der Idee bis zur fertigen App - AI-unterstützte Entwicklung in Rekordzeit"}
            </p>
          </motion.div>

          {/* Value Proposition */}
          <motion.div
            className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 p-4 md:p-6 rounded-xl shadow-lg mb-8 md:mb-12"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 text-center">
              <div>
                <Brain className="w-8 h-8 mx-auto mb-2 text-blue-600 dark:text-blue-400" />
                <div className="font-semibold text-sm mb-1 text-gray-900 dark:text-white">
                  {dictionary?.valueProps?.aiDevelopment?.title || "AI-Enhanced Development"}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {dictionary?.valueProps?.aiDevelopment?.description || "70% faster development"}
                </div>
              </div>
              <div>
                <Database className="w-8 h-8 mx-auto mb-2 text-blue-600 dark:text-blue-400" />
                <div className="font-semibold text-sm mb-1 text-gray-900 dark:text-white">
                  {dictionary?.valueProps?.backend?.title || "Firebase & Supabase"}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {dictionary?.valueProps?.backend?.description || "Modern Backend Solutions"}
                </div>
              </div>
              <div>
                <Code className="w-8 h-8 mx-auto mb-2 text-blue-600 dark:text-blue-400" />
                <div className="font-semibold text-sm mb-1 text-gray-900 dark:text-white">
                  {dictionary?.valueProps?.quality?.title || "Enterprise Quality"}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {dictionary?.valueProps?.quality?.description || "Clean Architecture"}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Pricing Packages */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mb-12 md:mb-16">
            {Object.entries(pricingPackages).map(([key, packageData]) => (
              <PackageCard
                key={key}
                packageKey={key}
                packageData={packageData}
              />
            ))}
          </div>

          {/* Info Modals */}
          {Object.entries(pricingPackages).map(([key, packageData]) => (
            <InfoModal key={key} packageKey={key} packageData={packageData} />
          ))}

          {/* CTA Section */}
          <motion.div
            className="bg-blue-50 dark:bg-blue-900/20 p-6 md:p-8 rounded-xl border border-blue-200 dark:border-blue-700 text-center max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <h3 className="text-2xl md:text-3xl font-bold text-blue-800 dark:text-blue-300 mb-4">
              🚀 {dictionary?.otherInquiries?.title || "Bereit für Ihre App?"}
            </h3>
            <p className="text-blue-700 dark:text-blue-300 mb-6">
              {dictionary?.otherInquiries?.description ||
                "Kostenloses 30-min Beratungsgespräch • Individuelle Projektplanung inklusive"}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                className="bg-blue-600 text-white py-3 px-6 md:px-8 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-300 flex items-center justify-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Calendar className="w-4 h-4" />
                {dictionary?.otherInquiries?.bookCall ||
                  "Beratungstermin buchen"}
              </motion.button>
              <motion.button
                className="bg-green-600 text-white py-3 px-6 md:px-8 rounded-lg font-semibold hover:bg-green-700 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {dictionary?.otherInquiries?.whatsappContact ||
                  "WhatsApp: Projekt besprechen"}
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>
    </UnifiedBackground>
  );
};

export default PricingSection;