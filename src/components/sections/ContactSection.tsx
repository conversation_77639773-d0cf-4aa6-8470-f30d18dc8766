'use client'

import { useState, useMemo, useCallback, memo, useEffect } from "react";
import { motion, AnimatePresence, useReducedMotion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Section } from "@/components/ui/Section";
import { Button } from "@/components/ui/Button";
import { UnifiedBackground } from "@/components/ui/UnifiedBackground";
import ClientOnly from "@/components/ui/ClientOnly";
import { useI18n } from "@/providers/I18nProvider";
import { type Dictionary } from "@/lib/dictionary";
import {
  SERVICES,
  BUDGET_RANGES,
  TIMELINE_OPTIONS,
  SOURCE_OPTIONS,
  formatPrice,
  getServicePrice,
  getServiceById,
} from "@/lib/config/services";
import { EnhancedContactFormData } from "@/types/proposal";
import {
  Mail,
  Phone,
  Calendar,
  MapPin,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  Shield,
  Zap,
  Star,
  Award,
  Verified,
  TrendingUp,
  Users,
  Globe,
  Sparkles,
  MessageCircle,
  Headphones,
  ArrowRight,
  Calculator,
  Building,
  DollarSign,
  Package,
  ChevronDown,
  Bot,
  Rocket,
} from "lucide-react";

// Contact details from Flutter code - memoized
const contactInfo = {
  email: "<EMAIL>",
  phone: "+49 175 9918357",
  location: "Wyoming",
  calendlyLink:
    "https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01",
};

interface ContactSectionProps {
  dictionary: Dictionary["contact"] & {
    location?: string;
  };
  aboutDictionary?: Dictionary["about"];
}

// Memoized contact method card component
const ContactMethodCard = memo(
  ({
    method,
    index,
    hoveredContact,
    setHoveredContact,
    dictionary,
  }: {
    method: any;
    index: number;
    hoveredContact: string | null;
    setHoveredContact: (id: string | null) => void;
    dictionary: Dictionary["contact"];
  }) => {
    const handleMouseEnter = useCallback(() => {
      setHoveredContact(method.id);
    }, [method.id, setHoveredContact]);

    const handleMouseLeave = useCallback(() => {
      setHoveredContact(null);
    }, [setHoveredContact]);

    const isHovered = hoveredContact === method.id;

    return (
      <motion.a
        key={method.id}
        href={method.action}
        target={
          method.id === "calendar" || method.id === "website"
            ? "_blank"
            : method.id === "location"
              ? "_self"
              : undefined
        }
        rel={
          method.id === "calendar" || method.id === "website"
            ? "noopener noreferrer"
            : undefined
        }
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="group block transform transition-all duration-300 hover:scale-105"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
      >
        <div
          className={`relative bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-3 sm:p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50 transition-all duration-500 overflow-hidden h-full flex flex-col cursor-pointer ${
            isHovered
              ? `scale-105 shadow-2xl border-transparent`
              : `hover:shadow-xl`
          }`}
        >
          {/* Background gradient effect */}
          <div
            className={`absolute inset-0 bg-gradient-to-br ${method.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-2xl sm:rounded-3xl`}
          />

          {/* Top glow effect */}
          <div className="absolute inset-0 bg-gradient-to-t from-transparent to-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl sm:rounded-3xl pointer-events-none" />

          <div className="flex flex-col items-center text-center gap-2 sm:gap-3 relative z-10 h-full">
            <div
              className={`w-10 h-10 sm:w-16 sm:h-16 bg-gradient-to-br ${method.gradient} rounded-xl sm:rounded-2xl flex items-center justify-center mb-2 sm:mb-4 shadow-xl group-hover:scale-110 transition-transform duration-300`}
            >
              <div className={`${isHovered ? "text-white" : "text-white"}`}>
                <div className="w-5 h-5 sm:w-6 sm:h-6">{method.icon}</div>
              </div>
            </div>

            <div className="flex-1 w-full">
              <h4
                className={`text-sm sm:text-base font-bold text-gray-900 dark:text-white mb-1 sm:mb-2 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:bg-clip-text group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300`}
              >
                {method.title}
              </h4>
              <p
                className={`font-semibold mb-1 sm:mb-2 text-xs sm:text-sm transition-colors ${
                  isHovered
                    ? "text-gray-800 dark:text-white"
                    : "text-gray-700 dark:text-gray-300"
                }`}
              >
                {method.value}
              </p>
              <p
                className={`text-xs mb-1 sm:mb-2 transition-colors hidden sm:block ${
                  isHovered
                    ? "text-gray-600 dark:text-gray-300"
                    : "text-gray-500 dark:text-gray-400"
                }`}
              >
                {method.description}
              </p>
            </div>

            {/* Call to Action */}
            <div
              className={`flex items-center justify-center w-full px-2 py-1.5 sm:px-3 sm:py-2 rounded-lg font-medium text-xs sm:text-sm transition-all duration-300 ${
                isHovered
                  ? "bg-white/90 text-gray-800 border border-gray-200 shadow-sm"
                  : "bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800"
              }`}
            >
              <span className="mr-1 sm:mr-2">
                {method.id === "email"
                  ? dictionary?.sendMessage || "Send Email"
                  : method.id === "phone"
                    ? dictionary?.callNow || "Call Now"
                    : method.id === "calendar"
                      ? dictionary?.bookCall || "Book Call"
                      : dictionary?.viewInfo || "View Info"}
              </span>
              <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />
            </div>
          </div>

          {/* Hover effect overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl sm:rounded-3xl pointer-events-none"></div>
        </div>
      </motion.a>
    );
  }
);

ContactMethodCard.displayName = "ContactMethodCard";

// Memoized WhatsApp quick action component
const WhatsAppQuickAction = memo(
  ({
    href,
    children,
    icon: Icon,
  }: {
    href: string;
    children: React.ReactNode;
    icon: any;
  }) => (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="relative flex items-center justify-between p-4 bg-white dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-100 dark:border-gray-700/50 hover:border-transparent hover:shadow-3xl hover:scale-105 transition-all duration-500 group overflow-hidden"
    >
      {/* Background gradient effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-500 via-emerald-500 to-teal-500 opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl"></div>

      <div className="flex items-center gap-3 relative z-10">
        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center">
          <Icon className="w-5 h-5 text-white" />
        </div>
        <div>{children}</div>
      </div>
      <ArrowRight className="w-5 h-5 text-green-600 dark:text-green-400 group-hover:translate-x-1 transition-transform relative z-10" />

      {/* Hover effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl pointer-events-none"></div>
    </a>
  )
);

WhatsAppQuickAction.displayName = "WhatsAppQuickAction";

// Selectable Card Component
const SelectableCard = memo(
  ({
    option,
    isSelected,
    onSelect,
    icon: Icon,
  }: {
    option: { value: string; label: string; description?: string };
    isSelected: boolean;
    onSelect: (value: string) => void;
    icon?: any;
  }) => (
    <motion.div
      onClick={() => onSelect(option.value)}
      className={`relative p-2 sm:p-3 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
        isSelected
          ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md"
          : "border-gray-200 dark:border-gray-700 bg-white/60 dark:bg-gray-800/60 hover:border-blue-300 hover:shadow-sm"
      }`}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
    >
      <div className="flex items-center gap-2">
        {Icon && (
          <div
            className={`w-5 h-5 sm:w-6 sm:h-6 rounded-md flex items-center justify-center ${
              isSelected
                ? "bg-blue-500 text-white"
                : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300"
            }`}
          >
            <Icon className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
          </div>
        )}
        <div className="flex-1 min-w-0">
          <div
            className={`text-xs sm:text-sm font-medium truncate ${
              isSelected
                ? "text-blue-700 dark:text-blue-300"
                : "text-gray-900 dark:text-white"
            }`}
          >
            {option.label}
          </div>
          {option.description && (
            <div className="text-xs text-gray-500 dark:text-gray-400 truncate hidden sm:block">
              {option.description}
            </div>
          )}
        </div>
        {isSelected && (
          <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-blue-500 flex-shrink-0" />
        )}
      </div>
    </motion.div>
  )
);

SelectableCard.displayName = "SelectableCard";

export const ContactSection = ({
  dictionary,
  aboutDictionary,
}: ContactSectionProps) => {
  const [formData, setFormData] = useState<EnhancedContactFormData>({
    name: "",
    email: "",
    companyName: "",
    phone: "",
    message: "",
    selectedService: "",
    estimatedBudget: "",
    projectTimeline: "",
  });

  const [formStatus, setFormStatus] = useState<
    "idle" | "submitting" | "success" | "error"
  >("idle");
  const [hoveredContact, setHoveredContact] = useState<string | null>(null);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [showServiceDetails, setShowServiceDetails] = useState(false);
  const { t, dir } = useI18n();
  const isRtl = dir === "rtl";

  const shouldReduceMotion = useReducedMotion();

  // Effect to handle service selection from URL parameters and custom events
  useEffect(() => {
    // Only run on client side to avoid hydration mismatch
    if (typeof window === "undefined") return;

    // Check URL parameters on component mount
    const urlParams = new URLSearchParams(window.location.search);
    const serviceFromUrl = urlParams.get("service");
    if (
      serviceFromUrl &&
      Object.values(SERVICES).some((s) => s.id === serviceFromUrl)
    ) {
      setFormData((prev) => ({ ...prev, selectedService: serviceFromUrl }));
    }

    // Listen for custom service selection events
    const handleServiceSelection = (event: CustomEvent) => {
      const { serviceId } = event.detail;
      if (
        serviceId &&
        Object.values(SERVICES).some((s) => s.id === serviceId)
      ) {
        setFormData((prev) => ({ ...prev, selectedService: serviceId }));
      }
    };

    window.addEventListener(
      "selectService",
      handleServiceSelection as EventListener
    );

    return () => {
      window.removeEventListener(
        "selectService",
        handleServiceSelection as EventListener
      );
    };
  }, []);

  // Memoized input change handler
  const handleInputChange = useCallback(
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
    ) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    },
    []
  );

  // Memoized submit handler
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setFormStatus("submitting");

      try {
        const response = await fetch("/api/contact", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            // Full form data for Notion CRM
            ...formData,
            // Override with email-specific fields
            subject: formData.selectedService
              ? `Service Inquiry: ${formData.selectedService}`
              : "Contact Form Inquiry",
            message: `
Company: ${formData.companyName || "Not specified"}
Phone: ${formData.phone || "Not specified"}
Selected Service: ${formData.selectedService || "Not specified"}
Estimated Budget: ${formData.estimatedBudget || "Not specified"}
Project Timeline: ${formData.projectTimeline || "Not specified"}

Message:
${formData.message}
            `.trim(),
          }),
        });

        if (response.ok) {
          const result = await response.json();

          // Log successful submission with Notion details
          console.log("✅ Contact form submitted successfully:", {
            emailSent: true,
            notionLead: result.leadId ? "Created" : "Failed",
            leadId: result.leadId,
            proposalUrl: result.proposalUrl,
          });

          setFormData({
            name: "",
            email: "",
            companyName: "",
            phone: "",
            message: "",
            selectedService: "",
            estimatedBudget: "",
            projectTimeline: "",
          });
          setFormStatus("success");

          // Show extended success message if Notion lead was created
          if (result.leadId) {
            console.log("🎯 Notion CRM Lead Created:", result.leadId);
            if (result.proposalUrl) {
              console.log("📄 Proposal URL generated:", result.proposalUrl);
            }
          }

          setTimeout(() => {
            setFormStatus("idle");
          }, 5000); // Extended time to show success
        } else {
          throw new Error("Failed to send email");
        }
      } catch (error) {
        console.error("Error sending email:", error);
        setFormStatus("error");
        setTimeout(() => {
          setFormStatus("idle");
        }, 4000);
      }
    },
    [formData]
  );

  // Memoized enhanced contact methods with trust indicators and modern icons
  const contactMethods = useMemo(
    () => [
      {
        id: "email",
        icon: <Mail className="w-6 h-6" />,
        title: dictionary?.email || "Email",
        value: contactInfo.email,
        action: `mailto:${contactInfo.email}`,
        color: "#3B82F6",
        gradient: "from-blue-500 to-indigo-600",
        shadowColor: "shadow-blue-500/25",
        description: dictionary?.emailDescription || "Quick response within 4 hours",
        trustBadge: "✉️ 4h Response",
        availability: "24/7",
      },
      {
        id: "phone",
        icon: <Phone className="w-6 h-6" />,
        title: dictionary?.phone || "Phone",
        value: contactInfo.phone,
        action: `tel:${contactInfo.phone}`,
        color: "#10B981",
        gradient: "from-emerald-500 to-teal-600",
        shadowColor: "shadow-emerald-500/25",
        description: dictionary?.phoneDescription || "Direct line for urgent matters",
        trustBadge: "📞 Direct Line",
        availability: "Mon-Fri 9-18",
      },
      {
        id: "calendar",
        icon: <Calendar className="w-6 h-6" />,
        title: dictionary?.schedule || "Schedule a Call",
        value: dictionary?.freeConsultation || "Book a free 15-min consultation",
        action: contactInfo.calendlyLink,
        color: "#8B5CF6",
        gradient: "from-violet-500 to-purple-600",
        shadowColor: "shadow-purple-500/25",
        description: dictionary?.calendarDescription || "Free consultation call",
        trustBadge: "🎯 Free Consultation",
        availability: "Flexible",
      },
      {
        id: "location",
        icon: <MapPin className="w-6 h-6" />,
        title: dictionary?.location || "Location",
        value: contactInfo.location,
        action: "#",
        color: "#F59E0B",
        gradient: "from-blue-500 to-blue-600",
        shadowColor: "shadow-amber-500/25",
        description: dictionary?.locationDescription || "Remote work, global reach",
        trustBadge: "🌍 Global Service",
        availability: "Worldwide",
      },
    ],
    [dictionary?.email, dictionary?.phone, dictionary?.location]
  );

  // Memoized trust indicators for footer
  const trustIndicators = useMemo(
    () => [
      {
        icon: <Clock className="w-6 h-6" />,
        value: "4h",
        label: "Response Time",
        color: "from-blue-500 to-indigo-600",
      },
      {
        icon: <Verified className="w-6 h-6" />,
        value: "100%",
        label: "Secure & Private",
        color: "from-emerald-500 to-teal-600",
      },
      {
        icon: <MessageCircle className="w-6 h-6" />,
        value: "150+",
        label: "Messages Handled",
        color: "from-purple-500 to-pink-600",
      },
      {
        icon: <Headphones className="w-6 h-6" />,
        value: "24/7",
        label: "Available",
        color: "from-orange-500 to-amber-600",
      },
    ],
    []
  );

  return (
    <UnifiedBackground className="w-full">
      <Section
        id="contact"
        title={
          <span>
            {(dictionary?.title || "Contact Us")
              .split(" ")
              .map((word, index) => {
                const isColoredWord =
                  word.toLowerCase().includes("contact") ||
                  word.toLowerCase().includes("us") ||
                  word.toLowerCase().includes("kontakt");
                return (
                  <span key={index}>
                    {isColoredWord ? (
                      <span className="text-[#1e3a8a] dark:text-blue-400">
                        {word}
                      </span>
                    ) : (
                      word
                    )}
                    {index <
                      (dictionary?.title || "Contact Us").split(" ").length -
                        1 && " "}
                  </span>
                );
              })}
          </span>
        }
        subtitle={dictionary?.subtitle || "Let's start shaping the future"}
        titleClassName="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white text-center mb-6 leading-tight"
        subtitleClassName="text-lg md:text-xl text-gray-600 dark:text-gray-300 text-center mb-8 max-w-3xl mx-auto leading-relaxed"
        className={`py-20 md:py-28 relative w-full ${
          isRtl ? "rtl-section" : ""
        }`}
      >
        {/* Trust Statistics Bar - Horizontal Auto Scrolling */}
        <motion.div
          className="mb-16 relative overflow-hidden rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-white/30 dark:border-gray-700/30 shadow-xl max-w-5xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          <div className="flex animate-scroll-horizontal gap-8 py-6 px-4">
            {/* First set of indicators */}
            {trustIndicators.map((indicator, index) => (
              <div
                key={`first-${index}`}
                className="flex items-center gap-3 text-center min-w-fit whitespace-nowrap"
              >
                <div
                  className={`w-12 h-12 rounded-full bg-gradient-to-br ${indicator.color} flex items-center justify-center shadow-lg`}
                >
                  {indicator.icon}
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                    {indicator.value}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {indicator.label}
                  </div>
                </div>
              </div>
            ))}
            {/* Duplicate set for seamless scrolling */}
            {trustIndicators.map((indicator, index) => (
              <div
                key={`second-${index}`}
                className="flex items-center gap-3 text-center min-w-fit whitespace-nowrap"
              >
                <div
                  className={`w-12 h-12 rounded-full bg-gradient-to-br ${indicator.color} flex items-center justify-center shadow-lg`}
                >
                  {indicator.icon}
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                    {indicator.value}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {indicator.label}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Main Layout: Cards Grid (Left) and Form (Right) */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-start">
            {/* Contact Methods Section - Left Side 2x2 Grid */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4 }}
            >
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                {dictionary?.getInTouch || "Get in Touch"}
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {contactMethods.map((method, index) => (
                  <ContactMethodCard
                    key={method.id}
                    method={method}
                    index={index}
                    hoveredContact={hoveredContact}
                    setHoveredContact={setHoveredContact}
                    dictionary={dictionary}
                  />
                ))}
              </div>
            </motion.div>

            {/* Enhanced Contact Form - Right Side with Dropdowns */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6 text-center">
                {dictionary?.writeUs || "Send a Message"}
              </h3>
              <ClientOnly
                fallback={
                  <div className="relative bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl p-6 rounded-2xl sm:rounded-3xl shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded mb-6"></div>
                      <div className="space-y-4">
                        <div className="h-10 bg-gray-200 dark:bg-gray-600 rounded"></div>
                        <div className="h-10 bg-gray-200 dark:bg-gray-600 rounded"></div>
                        <div className="h-24 bg-gray-200 dark:bg-gray-600 rounded"></div>
                        <div className="h-10 bg-gray-200 dark:bg-gray-600 rounded"></div>
                      </div>
                    </div>
                  </div>
                }
              >
                <form
                  onSubmit={handleSubmit}
                  className="relative bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl p-6 rounded-2xl sm:rounded-3xl shadow-lg border border-gray-200/50 dark:border-gray-700/50"
                  suppressHydrationWarning
                >
                  <div className="relative z-10" suppressHydrationWarning>
                    <p className="text-gray-600 dark:text-gray-400 text-center mb-6">
                      {dictionary?.formDescription || "Fill out the form below and I'll get back to you shortly"}
                    </p>
                    <div className="space-y-4" suppressHydrationWarning>
                      {/* Name and Email - Two columns */}
                      <div
                        className="grid grid-cols-1 md:grid-cols-2 gap-4"
                        suppressHydrationWarning
                      >
                        <div>
                          <label
                            htmlFor="name"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                          >
                            {dictionary?.name || "Name"} *
                          </label>
                          <input
                            type="text"
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            onFocus={() => setFocusedField("name")}
                            onBlur={() => setFocusedField(null)}
                            required
                            className={`w-full px-3 py-2.5 rounded-lg border transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                              focusedField === "name"
                                ? "border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20 dark:ring-blue-400/20"
                                : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                            }`}
                            placeholder={dictionary?.yourName || "Your Name"}
                            disabled={formStatus === "submitting"}
                          />
                        </div>

                        <div>
                          <label
                            htmlFor="email"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                          >
                            {dictionary?.email || "Email"} *
                          </label>
                          <input
                            type="email"
                            id="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            onFocus={() => setFocusedField("email")}
                            onBlur={() => setFocusedField(null)}
                            required
                            className={`w-full px-3 py-2.5 rounded-lg border transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                              focusedField === "email"
                                ? "border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20 dark:ring-blue-400/20"
                                : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                            }`}
                            placeholder={dictionary?.yourEmail || "Your Email"}
                            disabled={formStatus === "submitting"}
                          />
                        </div>
                      </div>

                      {/* Service Selection - Dropdown */}
                      <div>
                        <label
                          htmlFor="selectedService"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          <Package className="w-4 h-4 inline mr-1" />
                          {dictionary?.yourRequest || "Your Request"}
                        </label>
                        <div className="relative">
                          <select
                            id="selectedService"
                            name="selectedService"
                            value={formData.selectedService}
                            onChange={handleInputChange}
                            onFocus={() => setFocusedField("selectedService")}
                            onBlur={() => setFocusedField(null)}
                            className={`w-full px-3 py-2.5 rounded-lg border transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white appearance-none cursor-pointer ${
                              focusedField === "selectedService"
                                ? "border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20 dark:ring-blue-400/20"
                                : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                            }`}
                            disabled={formStatus === "submitting"}
                          >
                            <option value="">{dictionary?.whatDoYouNeed || "What do you need?"}</option>
                            <option value="app-neuentwicklung">
                              {dictionary?.newAppDevelopment || "New App Development - New app from scratch"}
                            </option>
                            <option value="app-maintenance">
                              {dictionary?.appMaintenance || "App Maintenance - Maintain/extend existing app"}
                            </option>
                            <option value="app-pruefung">
                              {dictionary?.appReview || "App Review - Review existing app"}
                            </option>
                            <option value="app-modernisierung">
                              {dictionary?.appModernization || "App Modernization - Update old app to latest standard"}
                            </option>
                            <option value="beratung">
                              {dictionary?.consulting || "Consulting - Clarify app development questions"}
                            </option>
                            <option value="sonstiges">
                              {dictionary?.other || "Other - Individual request"}
                            </option>
                          </select>
                          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                        </div>
                      </div>

                      {/* Company and Timeline - Two columns */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label
                            htmlFor="companyName"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                          >
                            <Building className="w-4 h-4 inline mr-1" />
                            {dictionary?.company || "Company"}
                          </label>
                          <input
                            type="text"
                            id="companyName"
                            name="companyName"
                            value={formData.companyName}
                            onChange={handleInputChange}
                            onFocus={() => setFocusedField("companyName")}
                            onBlur={() => setFocusedField(null)}
                            className={`w-full px-3 py-2.5 rounded-lg border transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                              focusedField === "companyName"
                                ? "border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20 dark:ring-blue-400/20"
                                : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                            }`}
                            placeholder={dictionary?.yourCompanyName || "Your Company Name"}
                            disabled={formStatus === "submitting"}
                          />
                        </div>

                        <div>
                          <label
                            htmlFor="projectTimeline"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                          >
                            <Clock className="w-4 h-4 inline mr-1" />
                            {dictionary?.timeline || "Timeline"}
                          </label>
                          <div className="relative">
                            <select
                              id="projectTimeline"
                              name="projectTimeline"
                              value={formData.projectTimeline}
                              onChange={handleInputChange}
                              onFocus={() => setFocusedField("projectTimeline")}
                              onBlur={() => setFocusedField(null)}
                              className={`w-full px-3 py-2.5 rounded-lg border transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white appearance-none cursor-pointer ${
                                focusedField === "projectTimeline"
                                  ? "border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20 dark:ring-blue-400/20"
                                  : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                              }`}
                              disabled={formStatus === "submitting"}
                            >
                              <option value="">{dictionary?.selectTimeline || "Select timeline..."}</option>
                              <option value="asap">{dictionary?.asap || "ASAP"}</option>
                              <option value="1-3months">{dictionary?.oneToThreeMonths || "1-3 Months"}</option>
                              <option value="3-6months">{dictionary?.threeToSixMonths || "3-6 Months"}</option>
                              <option value="flexible">{dictionary?.flexible || "Flexible"}</option>
                            </select>
                            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                          </div>
                        </div>
                      </div>

                      {/* Budget - Dropdown */}
                      <div>
                        <label
                          htmlFor="estimatedBudget"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          <DollarSign className="w-4 h-4 inline mr-1" />
                          {dictionary?.estimatedBudget || "Estimated Budget"}
                        </label>
                        <div className="relative">
                          <select
                            id="estimatedBudget"
                            name="estimatedBudget"
                            value={formData.estimatedBudget}
                            onChange={handleInputChange}
                            onFocus={() => setFocusedField("estimatedBudget")}
                            onBlur={() => setFocusedField(null)}
                            className={`w-full px-3 py-2.5 rounded-lg border transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white appearance-none cursor-pointer ${
                              focusedField === "estimatedBudget"
                                ? "border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20 dark:ring-blue-400/20"
                                : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                            }`}
                            disabled={formStatus === "submitting"}
                          >
                            <option value="">{dictionary?.selectBudgetRange || "Select budget range..."}</option>
                            <option value="below5k">
                              {dictionary?.below5k || "Below €5,000 - Small projects"}
                            </option>
                            <option value="5to15k">
                              {dictionary?.fiveToFifteenK || "€5,000 - €15,000 - Medium projects"}
                            </option>
                            <option value="15to30k">
                              {dictionary?.fifteenToThirtyK || "€15,000 - €30,000 - Large projects"}
                            </option>
                            <option value="above30k">
                              {dictionary?.above30k || "€30,000+ - Enterprise projects"}
                            </option>
                            <option value="notSure">
                              {dictionary?.notSure || "Not sure yet - Let's discuss"}
                            </option>
                          </select>
                          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                        </div>
                      </div>

                      {/* Project Description - Compact */}
                      <div>
                        <label
                          htmlFor="message"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          <MessageCircle className="w-4 h-4 inline mr-1" />
                          {dictionary?.projectDescription || "Project Description"} *
                        </label>
                        <textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          onFocus={() => setFocusedField("message")}
                          onBlur={() => setFocusedField(null)}
                          required
                          rows={4}
                          className={`w-full px-3 py-2.5 rounded-lg border transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none ${
                            focusedField === "message"
                              ? "border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20 dark:ring-blue-400/20"
                              : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                          }`}
                          placeholder={dictionary?.projectPlaceholder || "Briefly describe your project idea, goals, and requirements..."}
                          disabled={formStatus === "submitting"}
                        />
                      </div>

                      {/* Submit Button - Compact */}
                      <div>
                        <button
                          type="submit"
                          className={`w-full py-3 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center ${
                            formStatus === "success"
                              ? "bg-green-500 hover:bg-green-600"
                              : formStatus === "error"
                                ? "bg-red-500 hover:bg-red-600"
                                : "bg-gradient-to-r from-[#1e3a8a] to-[#1d4ed8] hover:from-[#1d4ed8] hover:to-[#2563eb]"
                          } text-white shadow-xl hover:shadow-2xl transform hover:scale-[1.02] active:scale-[0.98] ring-1 ring-[#1e3a8a]/20 hover:ring-[#1e3a8a]/40 disabled:opacity-70 disabled:cursor-not-allowed`}
                          disabled={formStatus === "submitting"}
                        >
                          <AnimatePresence mode="wait">
                            {formStatus === "submitting" ? (
                              <motion.div
                                key="submitting"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                className="flex items-center"
                              >
                                <motion.div
                                  animate={{ rotate: 360 }}
                                  transition={{
                                    duration: 1,
                                    repeat: Infinity,
                                    ease: "linear",
                                  }}
                                  className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full mr-2"
                                />
                                {dictionary?.sending || "Sending..."}
                              </motion.div>
                            ) : formStatus === "success" ? (
                              <motion.div
                                key="success"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0 }}
                                className="flex items-center"
                              >
                                <CheckCircle className="w-4 h-4 mr-2" />
                                {dictionary?.messageSent || "Message Sent!"}
                              </motion.div>
                            ) : formStatus === "error" ? (
                              <motion.div
                                key="error"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0 }}
                                className="flex items-center"
                              >
                                <XCircle className="w-4 h-4 mr-2" />
                                {dictionary?.tryAgain || "Try Again"}
                              </motion.div>
                            ) : (
                              <motion.div
                                key="idle"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                className="flex items-center"
                              >
                                <Send className="w-4 h-4 mr-2" />
                                {dictionary?.sendMessage || "Send Message"}
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </button>
                      </div>

                      {/* Footer - Compact */}
                      <div className="text-center pt-2">
                        <div className="flex items-center justify-center gap-2 text-xs text-gray-400 dark:text-gray-500">
                          <Shield className="w-3 h-3" />
                          <span>Secure & Private</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </ClientOnly>
            </motion.div>
          </div>
        </div>
      </Section>
    </UnifiedBackground>
  );
};