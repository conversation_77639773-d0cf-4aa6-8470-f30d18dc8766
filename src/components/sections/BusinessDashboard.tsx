import React, { useState, useEffect } from 'react';
import { TrendingUp, Users, Download, Bell, BarChart3, Zap, Bot, Activity } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const BusinessDashboard: React.FC = () => {
  const [animateCharts, setAnimateCharts] = useState(false);
  const [aiEnabled, setAiEnabled] = useState(true);
  const [performanceData, setPerformanceData] = useState(92);

  useEffect(() => {
    const timer = setTimeout(() => setAnimateCharts(true), 500);
    return () => clearTimeout(timer);
  }, []);


  const ChartBar: React.FC<{ height: number; delay?: number }> = ({ height, delay = 0 }) => (
    <div className="flex flex-col items-center">
      <div 
        className="w-3 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all duration-1000 ease-out"
        style={{ 
          height: animateCharts ? `${height}px` : '2px',
          transitionDelay: `${delay}ms` 
        }}
      />
    </div>
  );

  return (
    <motion.div 
      className="w-full h-full bg-gradient-to-br from-slate-50 to-blue-50 pt-12 pr-4 pl-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      {/* Header */}
      <motion.div 
        className="flex items-center justify-between mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div 
          animate={{ y: [-2, 2, -2] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        >
          <h1 className="text-xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-xs text-gray-500">Live App Performance</p>
        </motion.div>
        <motion.div 
          className="relative"
          animate={{ y: [-2, 2, -2] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
        >
          <Bell className="w-5 h-5 text-gray-600" />
          <motion.div 
            className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          ></motion.div>
        </motion.div>
      </motion.div>

      {/* Live Status */}
      <motion.div 
        className="bg-white rounded-xl p-4 mb-4 shadow-sm"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
        whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
      >
        <div className="flex items-center space-x-2 mb-2">
          <motion.div 
            className="w-2 h-2 bg-green-500 rounded-full"
            animate={{ scale: [1, 1.3, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          ></motion.div>
          <span className="text-sm font-medium text-gray-900">Live Updates</span>
        </div>
        <motion.p 
          className="text-lg font-bold text-gray-900"
          animate={{ y: [-2, 2, -2] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        >
          2,407 total users
        </motion.p>
      </motion.div>

      {/* Stats Grid */}
      <motion.div 
        className="grid grid-cols-2 gap-3 mb-5"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.5 }}
      >
        {/* Active Users */}
        <motion.div 
          className="bg-white rounded-xl p-4 shadow-sm"
          animate={{ y: [-2, 2, -2] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
        >
          <div className="flex items-center space-x-2 mb-2">
            <motion.div 
              className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
            >
              <Users className="w-4 h-4 text-white" />
            </motion.div>
            <div>
              <p className="text-xs text-gray-500">ACTIVE USERS</p>
              <p className="text-lg font-bold text-gray-900">2.4K</p>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <motion.div
              animate={{ y: [0, -2, 0] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              <TrendingUp className="w-3 h-3 text-green-500" />
            </motion.div>
            <span className="text-xs font-medium text-green-500">+12%</span>
            <span className="text-xs text-gray-400">vs last month</span>
          </div>
        </motion.div>

        {/* Downloads */}
        <motion.div 
          className="bg-white rounded-xl p-4 shadow-sm"
          animate={{ y: [-2, 2, -2] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
        >
          <div className="flex items-center space-x-2 mb-2">
            <motion.div 
              className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
            >
              <Download className="w-4 h-4 text-white" />
            </motion.div>
            <div>
              <p className="text-xs text-gray-500">DOWNLOADS</p>
              <p className="text-lg font-bold text-gray-900">8.7K</p>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <motion.div
              animate={{ y: [0, -2, 0] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0.5 }}
            >
              <TrendingUp className="w-3 h-3 text-green-500" />
            </motion.div>
            <span className="text-xs font-medium text-green-500">+24%</span>
            <span className="text-xs text-gray-400">vs last month</span>
          </div>
        </motion.div>
      </motion.div>

      {/* Revenue Chart */}
      <motion.div 
        className="bg-white rounded-xl p-4 mb-4 shadow-sm"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.5 }}
        whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
      >
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-gray-900">Revenue Growth</h3>
          <div className="flex items-center space-x-1">
            <motion.div 
              className="w-1.5 h-1.5 bg-blue-500 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            ></motion.div>
            <span className="text-xs text-gray-500">This Year</span>
          </div>
        </div>
        
        <motion.div 
          className="mb-3"
          animate={{ y: [-2, 2, -2] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        >
          <p className="text-xl font-bold text-gray-900">€45.2K</p>
          <div className="flex items-center space-x-1">
            <motion.div
              animate={{ y: [0, -2, 0] }}
              transition={{ duration: 1, repeat: Infinity, delay: 1 }}
            >
              <TrendingUp className="w-3 h-3 text-green-500" />
            </motion.div>
            <span className="text-xs font-medium text-green-500">+18.5%</span>
          </div>
        </motion.div>

        {/* Mini Chart */}
        <motion.div 
          className="flex items-end justify-between space-x-1 h-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <ChartBar height={16} delay={100} />
          <ChartBar height={24} delay={200} />
          <ChartBar height={12} delay={300} />
          <ChartBar height={28} delay={400} />
          <ChartBar height={20} delay={500} />
          <ChartBar height={30} delay={600} />
          <ChartBar height={18} delay={700} />
        </motion.div>
      </motion.div>

      {/* AI Toggle Section */}
      <motion.div 
        className="bg-white rounded-xl p-4 mb-4 shadow-sm"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.5 }}
        whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <motion.div 
              className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
            >
              <Bot className="w-4 h-4 text-white" />
            </motion.div>
            <motion.div
              animate={{ y: [-2, 2, -2] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            >
              <p className="text-sm font-medium text-gray-900">AI Optimization</p>
              <p className="text-xs text-gray-500">Smart Performance Tuning</p>
            </motion.div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">OFF</span>
            <motion.button 
              onClick={() => setAiEnabled(!aiEnabled)}
              className={`relative w-10 h-6 rounded-full transition-all ${
                aiEnabled ? 'bg-green-500' : 'bg-gray-300'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div 
                className="absolute w-4 h-4 bg-white rounded-full top-1"
                animate={{ x: aiEnabled ? 20 : 4 }}
                transition={{ duration: 0.3 }}
              />
            </motion.button>
            <span className="text-xs text-gray-500">ON</span>
          </div>
        </div>
        <AnimatePresence>
          {aiEnabled && (
            <motion.div 
              className="mt-3 p-2 bg-green-50 rounded-lg"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center space-x-1">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                >
                  <Zap className="w-3 h-3 text-green-500" />
                </motion.div>
                <span className="text-xs font-medium text-green-600">AI Active</span>
                <span className="text-xs text-gray-500">• Performance boosted by 23%</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Performance Speed Chart */}
      <motion.div 
        className="bg-white rounded-xl p-4 mb-4 shadow-sm"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0, duration: 0.5 }}
        whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
      >
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <motion.div 
              className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center"
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
            >
              <Activity className="w-4 h-4 text-white" />
            </motion.div>
            <motion.div
              animate={{ y: [-2, 2, -2] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            >
              <h3 className="text-sm font-semibold text-gray-900">Performance Speed</h3>
              <p className="text-xs text-gray-500">Real-time metrics</p>
            </motion.div>
          </div>
          <motion.div 
            className="text-right"
            animate={{ y: [-2, 2, -2] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          >
            <p className="text-lg font-bold text-gray-900">{performanceData}%</p>
            <p className="text-xs text-green-500 font-medium">Excellent</p>
          </motion.div>
        </div>
        
        {/* Performance Bar */}
        <motion.div 
          className="relative"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.5 }}
        >
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-orange-400 to-orange-500 h-2 rounded-full transition-all duration-1000 ease-out"
              style={{ 
                width: animateCharts ? `${performanceData}%` : '0%',
                transitionDelay: '800ms'
              }}
            />
          </div>
          <div className="flex justify-between mt-2 text-xs text-gray-500">
            <span>0%</span>
            <span>50%</span>
            <span>100%</span>
          </div>
        </motion.div>
        
        {/* Performance Metrics */}
        <motion.div 
          className="grid grid-cols-3 gap-2 mt-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.4, duration: 0.5 }}
        >
          {[
            { label: 'Load Time', value: '1.2s', delay: 0 },
            { label: 'Response', value: '0.8s', delay: 0.1 },
            { label: 'Uptime', value: '99.9%', delay: 0.2 }
          ].map((metric, index) => (
            <motion.div 
              key={index}
              className="text-center p-2 bg-gray-50 rounded-lg"
              animate={{ y: [-2, 2, -2] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: metric.delay }}
              whileHover={{ scale: 1.05 }}
            >
              <p className="text-xs font-medium text-gray-900">{metric.label}</p>
              <p className="text-xs text-green-500">{metric.value}</p>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>

      {/* Quick Action */}
      <motion.button 
        className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-3 flex items-center justify-center space-x-2 hover:from-blue-600 hover:to-blue-700 transition-all shadow-sm"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.6, duration: 0.5 }}
        whileHover={{ scale: 1.05, y: -2 }}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          animate={{ rotate: [0, 360] }}
          transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
        >
          <BarChart3 className="w-4 h-4" />
        </motion.div>
        <span className="text-sm font-medium">View Full Analytics</span>
      </motion.button>
    </motion.div>
  );
};

export default BusinessDashboard;