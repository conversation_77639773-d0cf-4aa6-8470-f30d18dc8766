"use client";

import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { 
  <PERSON>rk<PERSON>,
  ArrowRight,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import FlipGallery from "@/components/ui/flip-gallery";
import { UnifiedBackground } from "@/components/ui/UnifiedBackground";
import { useI18n } from "@/providers/I18nProvider";
import { type Dictionary } from "@/lib/dictionary";

interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  category: string;
  image: string;
  technologies: string[];
  features: string[];
}

interface PortfolioSectionProps {
  dictionary?: Dictionary["portfolio"];
}

// Base portfolio items structure - content will come from dictionaries
const basePortfolioItems = [
  {
    id: "logistics-platform",
    image: "/images/hero/aigo.png",
  },
  {
    id: "detoxme", 
    image: "/images/hero/detoxme.png",
  },
  {
    id: "reserv",
    image: "/images/hero/reserv.png",
  },
  {
    id: "togg",
    image: "/images/hero/togg.png",
  }
];

// Transform base portfolio items to flip gallery format
const galleryItems = basePortfolioItems.map(item => ({
  url: item.image,
  title: item.id // Will be replaced with proper title in component
}));

export function PortfolioSection({ dictionary }: PortfolioSectionProps) {
  const [currentProjectIndex, setCurrentProjectIndex] = useState(0);
  const autoAdvanceRef = useRef<NodeJS.Timeout>();
  const { t } = useI18n();

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Auto advance functionality
  // Get portfolio items from dictionary
  const getPortfolioItems = () => {
    if (!dictionary?.projects) {
      return basePortfolioItems.map(base => ({
        ...base,
        title: base.id,
        description: "Description not available",
        category: "general",
        technologies: [],
        features: []
      }));
    }

    return basePortfolioItems.map(base => {
      const dictProject = dictionary?.projects?.find((p: any) => p.id === base.id);
      return {
        ...base,
        title: dictProject?.title || base.id,
        description: dictProject?.description || "Description not available",
        category: dictProject?.category || "general", 
        technologies: dictProject?.technologies || [],
        features: dictProject?.features || []
      };
    });
  };

  const portfolioItems = getPortfolioItems();

  useEffect(() => {
    if (inView) {
      autoAdvanceRef.current = setInterval(() => {
        setCurrentProjectIndex((prev) => (prev + 1) % portfolioItems.length);
      }, 6000);

      return () => {
        if (autoAdvanceRef.current) {
          clearInterval(autoAdvanceRef.current);
        }
      };
    }
  }, [inView, portfolioItems.length]);

  const nextProject = () => {
    // Clear auto advance when manually navigating
    if (autoAdvanceRef.current) {
      clearInterval(autoAdvanceRef.current);
    }
    const newIndex = (currentProjectIndex + 1) % portfolioItems.length;
    setCurrentProjectIndex(newIndex);
    // Restart auto advance after manual navigation
    autoAdvanceRef.current = setInterval(() => {
      setCurrentProjectIndex((prev) => (prev + 1) % portfolioItems.length);
    }, 6000);
  };

  const prevProject = () => {
    // Clear auto advance when manually navigating
    if (autoAdvanceRef.current) {
      clearInterval(autoAdvanceRef.current);
    }
    const newIndex =
      (currentProjectIndex - 1 + portfolioItems.length) % portfolioItems.length;
    setCurrentProjectIndex(newIndex);
    // Restart auto advance after manual navigation
    autoAdvanceRef.current = setInterval(() => {
      setCurrentProjectIndex((prev) => (prev + 1) % portfolioItems.length);
    }, 6000);
  };

  const currentProject = portfolioItems[currentProjectIndex];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <UnifiedBackground className="py-12 lg:py-20 bg-gradient-to-br from-slate-50/80 via-blue-50/20 to-slate-50/80 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      <section id="portfolio" ref={ref} className="relative">
        <div className="w-full px-4 sm:px-6 lg:px-8 xl:px-12 relative z-10">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="max-w-7xl mx-auto"
          >
            {/* Header */}
            <motion.div variants={itemVariants} className="text-center mb-16">
              {/* Badge */}
              <motion.div
                variants={itemVariants}
                className="inline-flex items-center gap-2 bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 px-6 py-3 rounded-full text-sm font-bold mb-6 border border-purple-100 dark:border-purple-800"
              >
                <Sparkles className="w-4 h-4" />
                <span>{dictionary?.badge || "Our Apps"}</span>
              </motion.div>

              <motion.h2
                variants={itemVariants}
                className="text-4xl sm:text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gray-900 via-blue-600 to-purple-600 dark:from-white dark:via-blue-400 dark:to-purple-400 mb-4 leading-tight"
              >
                {dictionary?.title || "Successful Apps That Inspire"}
              </motion.h2>

              <motion.p
                variants={itemVariants}
                className="text-2xl lg:text-3xl text-gray-600 dark:text-blue-400 font-bold mb-4"
              >
                {dictionary?.subtitle || "From Idea to App Stores"}
              </motion.p>

              <motion.p
                variants={itemVariants}
                className="text-lg lg:text-xl text-gray-700 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed"
              >
                {dictionary?.description ||
                  t("portfolio.description") ||
                  "Discover some of our most successful app projects. From lifestyle apps to business solutions – each app was developed with cutting-edge technology and special focus on user experience."}
              </motion.p>
            </motion.div>

            {/* Project Info + Flip Gallery Layout */}
            <motion.div
              variants={itemVariants}
              className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center mb-16"
            >
              {/* Left: Project Information */}
              <motion.div
                variants={itemVariants}
                className="order-2 lg:order-1 px-4 lg:px-6"
              >
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentProjectIndex}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.5 }}
                    className="bg-gradient-to-br from-white/95 via-blue-50/50 to-white/95 dark:from-gray-800/95 dark:via-blue-900/20 dark:to-gray-800/95 backdrop-blur-lg rounded-3xl p-8 shadow-2xl border border-blue-200/30 dark:border-blue-700/30 hover:shadow-blue-500/20 dark:hover:shadow-blue-400/10 transition-all duration-500 hover:scale-[1.02] relative overflow-hidden w-full"
                  >
                    {/* Decorative Background Elements */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-3xl -translate-y-16 translate-x-16"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-500/10 to-blue-500/10 rounded-full blur-2xl translate-y-12 -translate-x-12"></div>

                    {/* Category Badge */}
                    <div className="mb-4 relative z-10">
                      <div className="inline-flex items-center bg-gradient-to-r from-blue-500 via-blue-600 to-purple-500 text-white px-4 py-2 rounded-full text-xs font-bold uppercase tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="w-2 h-2 bg-white/40 rounded-full mr-2 animate-pulse relative z-10"></div>
                        <span className="relative z-10">
                          {currentProject.category}
                        </span>
                      </div>
                    </div>

                    {/* Project Title */}
                    <h3 className="text-xl lg:text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-gray-700 dark:from-white dark:via-blue-200 dark:to-gray-200 bg-clip-text text-transparent mb-3 leading-tight relative z-10 hover:scale-105 transition-transform duration-300">
                      {currentProject.title}
                    </h3>

                    {/* Project Description */}
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 leading-relaxed relative z-10">
                      {currentProject.description}
                    </p>

                    {/* Technologies */}
                    <div className="mb-4 relative z-10">
                      <h4 className="text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2 flex items-center">
                        <span className="w-1 h-4 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full mr-2"></span>
                        {dictionary?.labels?.technologies ||
                          t("portfolio.labels.technologies") ||
                          "Technologies"}
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {currentProject.technologies.map(
                          (tech: string, techIndex: number) => (
                            <span
                              key={techIndex}
                              className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 px-3 py-1.5 rounded-full text-xs font-medium border border-blue-200 dark:border-blue-700 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-200"
                            >
                              {tech}
                            </span>
                          )
                        )}
                      </div>
                    </div>

                    {/* Features */}
                    <div className="relative z-10">
                      <h4 className="text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2 flex items-center">
                        <span className="w-1 h-4 bg-gradient-to-b from-purple-500 to-blue-500 rounded-full mr-2"></span>
                        {dictionary?.labels?.keyFeatures ||
                          t("portfolio.labels.keyFeatures") ||
                          "Key Features"}
                      </h4>
                      <div className="grid grid-cols-1 gap-2">
                        {currentProject.features
                          .slice(0, 4)
                          .map((feature: string, featureIndex: number) => (
                            <div
                              key={featureIndex}
                              className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 transition-colors duration-200"
                            >
                              <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-sm"></div>
                              {feature}
                            </div>
                          ))}
                      </div>
                    </div>
                  </motion.div>
                </AnimatePresence>
              </motion.div>

              {/* Right: Flip Gallery with iPhone Frame */}
              <motion.div
                variants={itemVariants}
                className="flex justify-center order-1 lg:order-2"
              >
                {/* iPhone Frame Container */}
                <div className="relative">
                  {/* iPhone Frame */}
                  <div className="relative w-[280px] h-[560px] bg-gray-900 rounded-[2.2rem] p-1 shadow-2xl">
                    {/* iPhone Screen */}
                    <div className="relative w-full h-full bg-black rounded-[1.8rem] overflow-hidden">
                      {/* iPhone Notch */}
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black rounded-b-2xl z-20 flex items-center justify-center">
                        <div className="w-10 h-1 bg-gray-800 rounded-full"></div>
                      </div>

                      {/* Flip Gallery in iPhone Screen */}
                      <div className="absolute inset-2 top-5 p8 bottom-6 rounded-[1.2rem] overflow-hidden">
                        <FlipGallery
                          images={galleryItems}
                          autoAdvance={false}
                          currentIndex={currentProjectIndex}
                        />
                      </div>

                      {/* iPhone Home Indicator */}
                      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full"></div>
                    </div>

                    {/* iPhone Side Buttons */}
                    <div className="absolute left-0 top-20 w-1 h-8 bg-gray-700 rounded-r-full"></div>
                    <div className="absolute left-0 top-32 w-1 h-12 bg-gray-700 rounded-r-full"></div>
                    <div className="absolute left-0 top-48 w-1 h-12 bg-gray-700 rounded-r-full"></div>
                    <div className="absolute right-0 top-32 w-1 h-16 bg-gray-700 rounded-l-full"></div>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Navigation Controls */}
            <motion.div
              variants={itemVariants}
              className="flex items-center justify-center gap-8 mb-12 px-4"
            >
              {/* Previous Button */}
              <button
                onClick={prevProject}
                className="w-14 h-14 rounded-full bg-white/10 dark:bg-gray-800/50 backdrop-blur-md hover:bg-white/20 dark:hover:bg-gray-700/50 transition-all duration-300 flex items-center justify-center border border-white/20 dark:border-gray-600/30 hover:border-white/40 dark:hover:border-gray-500/50 hover:scale-110 group"
              >
                <ChevronLeft className="w-6 h-6 text-white group-hover:text-blue-400 transition-colors duration-300" />
              </button>

              {/* Page Indicators */}
              <div className="flex gap-3">
                {portfolioItems.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      // Clear auto advance when manually navigating
                      if (autoAdvanceRef.current) {
                        clearInterval(autoAdvanceRef.current);
                      }
                      setCurrentProjectIndex(index);
                      // Restart auto advance after manual navigation
                      autoAdvanceRef.current = setInterval(() => {
                        setCurrentProjectIndex(
                          (prev) => (prev + 1) % portfolioItems.length
                        );
                      }, 6000);
                    }}
                    className={`transition-all duration-300 rounded-full ${
                      index === currentProjectIndex
                        ? "w-10 h-3 bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg"
                        : "w-3 h-3 bg-white/30 dark:bg-gray-600/50 hover:bg-white/50 dark:hover:bg-gray-500/70 hover:scale-125"
                    }`}
                  />
                ))}
              </div>

              {/* Next Button */}
              <button
                onClick={nextProject}
                className="w-14 h-14 rounded-full bg-white/10 dark:bg-gray-800/50 backdrop-blur-md hover:bg-white/20 dark:hover:bg-gray-700/50 transition-all duration-300 flex items-center justify-center border border-white/20 dark:border-gray-600/30 hover:border-white/40 dark:hover:border-gray-500/50 hover:scale-110 group"
              >
                <ChevronRight className="w-6 h-6 text-white group-hover:text-blue-400 transition-colors duration-300" />
              </button>
            </motion.div>

            {/* CTA Section */}
            <motion.div variants={itemVariants} className="text-center px-4">
              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <motion.a
                  href="https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative inline-flex items-center justify-center gap-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-purple-600 hover:to-blue-600 text-white font-bold px-10 py-5 rounded-2xl text-lg lg:text-xl transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl hover:shadow-blue-500/25"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span>
                    {dictionary?.cta?.startProject ||
                      "Start Project"}
                  </span>
                  <ArrowRight className="w-6 h-6 transition-transform group-hover:translate-x-1" />
                </motion.a>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </UnifiedBackground>
  );
}