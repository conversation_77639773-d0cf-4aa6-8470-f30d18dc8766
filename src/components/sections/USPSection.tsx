"use client";

import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  ArrowRight,
  Users,
  Hammer,
  Target,
  DollarSign,
  Phone,
  CheckCircle2,
  Sparkles,
  TrendingUp,
  Clock,
  Shield,
  Star,
  Lightbulb,
  Rocket,
  Zap,
  Award,
} from "lucide-react";
import { UnifiedBackground } from "@/components/ui/UnifiedBackground";

interface USPSectionProps {
  dictionary: {
    badge?: string;
    title: string;
    subtitle: string;
    description: string;
    features: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
    cta: {
      primary: string;
      secondary: string;
    };
    trustIndicators?: {
      freeConsultation: string;
      nonBinding: string;
      thirtyMinutes: string;
    };
  };
}

export function USPSection({ dictionary }: USPSectionProps) {
  // Safety check
  if (!dictionary || !dictionary.features || dictionary.features.length === 0) {
    return (
      <section className="py-16 bg-yellow-100 text-center">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-yellow-600">
            USP Section - Fallback
          </h2>
        </div>
      </section>
    );
  }

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Enhanced icon mapping with more modern icons
  const iconMap = [
    Users, // Personal consultation
    Lightbulb, // Innovation/Ideas
    Rocket, // Fast delivery/Launch
    Shield, // Security/Reliability
    TrendingUp, // Growth/Success
    Clock, // Time efficiency
    Award, // Quality/Excellence
    Zap, // Performance/Speed
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <UnifiedBackground className="py-16 lg:py-24 bg-white dark:bg-gray-900 overflow-hidden">
      <section id="usp" ref={ref} className="relative">
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
          >
            {/* Header */}
            <motion.div variants={itemVariants} className="text-center mb-16">
              <motion.div
                variants={itemVariants}
                className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-200/50 dark:border-blue-500/30 text-blue-600 dark:text-blue-400 px-6 py-3 rounded-full text-sm font-bold mb-8"
              >
                <Sparkles className="w-4 h-4" />
                <span>{dictionary.badge}</span>
              </motion.div>

              <motion.h2
                variants={itemVariants}
                className="text-4xl sm:text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gray-900 via-blue-600 to-purple-600 dark:from-white dark:via-blue-400 dark:to-purple-400 mb-6 leading-tight"
              >
                {dictionary.title}
              </motion.h2>

              <motion.p
                variants={itemVariants}
                className="text-xl lg:text-2xl text-gray-600 dark:text-gray-300 font-medium max-w-4xl mx-auto leading-relaxed mb-4"
              >
                {dictionary.subtitle}
              </motion.p>

              <motion.p
                variants={itemVariants}
                className="text-lg text-gray-500 dark:text-gray-400 max-w-3xl mx-auto"
              >
                {dictionary.description}
              </motion.p>
            </motion.div>

            {/* Responsive Layout: Timeline on Desktop, Simple Grid on Mobile */}
            <motion.div variants={itemVariants} className="mb-16">
              {/* Desktop Timeline Layout (hidden on mobile) */}
              <div className="hidden lg:block relative">
                {/* Central Timeline Path */}
                <div className="absolute left-1/2 top-0 bottom-0 w-1 transform -translate-x-1/2">
                  <motion.div
                    className="w-full bg-gradient-to-b from-blue-500 via-purple-500 to-blue-500 rounded-full"
                    initial={{ height: 0 }}
                    animate={inView ? { height: "100%" } : { height: 0 }}
                    transition={{ duration: 2, delay: 0.5 }}
                  />
                </div>

                {/* Timeline Steps */}
                <div className="space-y-8">
                  {dictionary.features
                    .slice(0, 6)
                    .map((feature: any, index: number) => {
                      const IconComponent = iconMap[index] || Target;
                      const isLeft = index % 2 === 0;

                      return (
                        <motion.div
                          key={index}
                          className={`flex items-center ${isLeft ? "flex-row" : "flex-row-reverse"} gap-8`}
                          initial={{ opacity: 0, x: isLeft ? -100 : 100 }}
                          animate={
                            inView
                              ? { opacity: 1, x: 0 }
                              : { opacity: 0, x: isLeft ? -100 : 100 }
                          }
                          transition={{ duration: 0.8, delay: index * 0.2 }}
                        >
                          {/* Content Card */}
                          <div
                            className={`flex-1 ${isLeft ? "text-right" : "text-left"}`}
                          >
                            <motion.div
                              className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-2xl p-4 border border-gray-200/50 dark:border-gray-600/50 max-w-sm mx-auto shadow-lg hover:shadow-xl transition-all duration-500 hover:border-blue-300/50 dark:hover:border-blue-500/50"
                              whileHover={{ scale: 1.03, y: -3 }}
                            >
                              {/* Number indicator */}
                              <div
                                className={`inline-flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full text-white text-sm font-bold mb-4 ${isLeft ? "ml-auto" : "mr-auto"}`}
                              >
                                {index + 1}
                              </div>

                              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                                {feature.title}
                              </h3>

                              <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors duration-300">
                                {feature.description}
                              </p>

                              {/* Progress indicator */}
                              <div className="mt-4 h-1 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                                <motion.div
                                  className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                                  initial={{ width: 0 }}
                                  animate={
                                    inView ? { width: "100%" } : { width: 0 }
                                  }
                                  transition={{
                                    duration: 1,
                                    delay: index * 0.2 + 1,
                                  }}
                                />
                              </div>
                            </motion.div>
                          </div>

                          {/* Central Timeline Node */}
                          <motion.div
                            className="relative z-10"
                            initial={{ scale: 0, rotate: -180 }}
                            animate={
                              inView
                                ? { scale: 1, rotate: 0 }
                                : { scale: 0, rotate: -180 }
                            }
                            transition={{
                              duration: 0.6,
                              delay: index * 0.2 + 0.3,
                              type: "spring",
                            }}
                          >
                            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-xl border-3 border-white dark:border-gray-900">
                              <IconComponent className="w-6 h-6 text-white" />
                            </div>

                            {/* Pulse animation */}
                            <motion.div
                              className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 opacity-30"
                              animate={{
                                scale: [1, 1.5, 1],
                                opacity: [0.3, 0, 0.3],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                delay: index * 0.3,
                              }}
                            />

                            {/* Connecting lines to content */}
                            <div
                              className={`absolute top-1/2 ${isLeft ? "right-full" : "left-full"} w-8 h-0.5 bg-gradient-to-${isLeft ? "l" : "r"} from-blue-500 to-transparent transform -translate-y-1/2`}
                            />
                          </motion.div>

                          {/* Spacer for opposite side */}
                          <div className="flex-1" />
                        </motion.div>
                      );
                    })}
                </div>
              </div>
              {
                /* Mobile/Tablet Simple Grid Layout */
              }
              <div className="lg:hidden">
                <div className="grid gap-4 sm:gap-6">
                  {dictionary.features
                    .slice(0, 6)
                    .map((feature: any, index: number) => {
                      const IconComponent = iconMap[index] || Target;

                      return (
                        <motion.div
                          key={index}
                          className="relative"
                          initial={{ opacity: 0, y: 50 }}
                          animate={
                            inView
                              ? { opacity: 1, y: 0 }
                              : { opacity: 0, y: 50 }
                          }
                          transition={{ duration: 0.6, delay: index * 0.1 }}
                        >
                          <motion.div
                            className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-2xl p-4 border border-gray-200/50 dark:border-gray-600/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:border-blue-300/50 dark:hover:border-blue-500/50 group"
                            whileHover={{ scale: 1.02, y: -2 }}
                          >
                            {/* Header with Number and Icon */}
                            <div className="flex flex-col items-center gap-3 mb-3">
                              {/* Large Number Counter */}
                              <motion.div
                                className="text-4xl font-black text-gray-300/60 dark:text-gray-600/40 leading-none"
                                initial={{ opacity: 0, scale: 0.5 }}
                                animate={
                                  inView
                                    ? { opacity: 1, scale: 1 }
                                    : { opacity: 0, scale: 0.5 }
                                }
                                transition={{
                                  duration: 0.8,
                                  delay: index * 0.1,
                                }}
                              >
                                {String(index + 1).padStart(2, "0")}
                              </motion.div>

                              {/* Centered Icon */}
                              <motion.div
                                className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg"
                                initial={{ scale: 0, rotate: -180 }}
                                animate={
                                  inView
                                    ? { scale: 1, rotate: 0 }
                                    : { scale: 0, rotate: -180 }
                                }
                                transition={{
                                  duration: 0.6,
                                  delay: index * 0.1 + 0.3,
                                  type: "spring",
                                }}
                              >
                                <IconComponent className="w-5 h-5 text-white" />
                              </motion.div>
                            </div>

                            <h3 className="text-base sm:text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                              {feature.title}
                            </h3>

                            <p className="text-gray-600 dark:text-gray-300 leading-relaxed group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors duration-300 text-xs sm:text-sm">
                              {feature.description}
                            </p>

                            {/* Progress indicator */}
                            <div className="mt-4 h-1 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                              <motion.div
                                className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                                initial={{ width: 0 }}
                                animate={
                                  inView ? { width: "100%" } : { width: 0 }
                                }
                                transition={{
                                  duration: 1,
                                  delay: index * 0.1 + 0.5,
                                }}
                              />
                            </div>
                          </motion.div>
                        </motion.div>
                      );
                    })}
                </div>
              </div>
              
            </motion.div>

            {/* CTA */}
            <motion.div variants={itemVariants} className="text-center">
              <div className="relative inline-block">
                {/* Glow effect behind CTA */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl blur-xl opacity-30 scale-110" />

                <motion.a
                  href="https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="relative inline-flex items-center justify-center gap-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold px-8 py-4 rounded-2xl text-lg transition-all duration-500 transform hover:scale-105 shadow-lg hover:shadow-2xl group overflow-hidden"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {/* Animated background */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: "0%" }}
                    transition={{ duration: 0.3 }}
                  />

                  <span className="relative z-10">
                    {dictionary.cta.primary}
                  </span>
                  <ArrowRight className="relative z-10 w-5 h-5 transition-transform group-hover:translate-x-1" />
                </motion.a>
              </div>

              {/* Trust indicators below CTA */}
              <motion.div
                variants={itemVariants}
                className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 mt-8 text-sm text-gray-600 dark:text-gray-400"
              >
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                  <span>
                    {dictionary.trustIndicators?.freeConsultation ||
                      "Kostenlose Beratung"}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                  <span>
                    {dictionary.trustIndicators?.nonBinding || "Unverbindlich"}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                  <span>
                    {dictionary.trustIndicators?.thirtyMinutes ||
                      "30 Min Gespräch"}
                  </span>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </UnifiedBackground>
  );
}