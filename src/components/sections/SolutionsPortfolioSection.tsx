"use client";

import { Section } from "@/components/ui/Section";
import { StaticBackground } from "@/components/ui/StaticBackground";
import { motion, Variants, useReducedMotion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  CheckCircle,
  Users,
  Award,
  Zap,
  Target,
  ArrowRight,
  Building,
  Rocket,
  Shield,
  TrendingUp,
  Clock,
  Star,
  Code,
  Bug,
  Calendar,
  BarChart3,
  MessageSquare,
  Activity,
  Sparkles,
} from "lucide-react";
import Image from "next/image";
import { useState } from "react";

// --- Interfaces and Types ---
interface ClientSuccessStoriesSectionProps {
  dictionary: {
    title?: string;
    description?: string;
    clientCaseStudies?: {
      lufthansa?: {
        title?: string;
        industry?: string;
        projectType?: string;
        description?: string;
        businessContext?: string;
        results?: string[];
      };
      unionInvestment?: {
        title?: string;
        industry?: string;
        projectType?: string;
        description?: string;
        businessContext?: string;
        results?: string[];
      };
      togg?: {
        title?: string;
        industry?: string;
        projectType?: string;
        description?: string;
        businessContext?: string;
        results?: string[];
      };
    };
    cta?: {
      startProject?: string;
    };
  };
}

// --- Optimized Animation Variants ---
const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.025,
    },
  },
};

const itemVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.22, 1, 0.36, 1],
    },
  },
};

// Simplified card variants for better performance
const cardVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.22, 1, 0.36, 1],
    },
  },
};

const scrollVariants: Variants = {
  animate: {
    x: ["-50%", "0%"],
    transition: {
      x: {
        repeat: Infinity,
        repeatType: "loop",
        duration: 30,
        ease: "linear",
      },
    },
  },
};

// --- Main Component ---
export const ClientSuccessStoriesSection: React.FC<
  ClientSuccessStoriesSectionProps
> = ({ dictionary }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.01,
    rootMargin: "200px 0px 200px 0px",
  });

  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const shouldReduceMotion = useReducedMotion();

  // Client logos for horizontal scrolling
  const clientLogos = [
    { name: "Lufthansa Group", logo: "/images/companies/lufthansa.png" },
    {
      name: "Union Investment",
      logo: "/images/companies/union-investment.png",
    },
    { name: "Togg", logo: "/images/companies/togg.png" },
    { name: "HEGLA", logo: "/images/companies/hegla.png" },
    { name: "Adesso", logo: "/images/companies/adesso.png" },
    { name: "Lumeus", logo: "/images/companies/lumeus.png" },
    { name: "Innovate", logo: "/images/companies/innovate.png" },
  ];

  // Enhanced client data with development-focused results
  const clientsData = [
    {
      id: "lufthansa",
      title:
        dictionary.clientCaseStudies?.lufthansa?.title || "Lufthansa Group",
      industry: dictionary.clientCaseStudies?.lufthansa?.industry || "Aviation",
      projectType:
        dictionary.clientCaseStudies?.lufthansa?.projectType ||
        "Enterprise Flutter App",
      description:
        dictionary.clientCaseStudies?.lufthansa?.description ||
        "Mission-critical travel management platform serving millions of passengers—where development speed and bug-free delivery were essential for success",
      businessContext:
        dictionary.clientCaseStudies?.lufthansa?.businessContext ||
        "When Europe's largest airline group needed enterprise-grade mobile architecture delivered on tight deadlines, they chose our development expertise",
      results: dictionary.clientCaseStudies?.lufthansa?.results || [
        "Accelerated development timeline by 40% through efficient Flutter architecture",
        "Zero critical bugs in production deployment",
        "Met all project deadlines despite complex requirements",
        "Delivered comprehensive testing coverage preventing future issues",
      ],
      brandColor: "from-blue-500 to-blue-700",
      accentColor: "blue",
      icon: Building,
      logo: "/images/companies/lufthansa.png",
      rating: 5.0,
    },
    {
      id: "unionInvestment",
      title:
        dictionary.clientCaseStudies?.unionInvestment?.title ||
        "Union Investment",
      industry:
        dictionary.clientCaseStudies?.unionInvestment?.industry ||
        "Financial Services",
      projectType:
        dictionary.clientCaseStudies?.unionInvestment?.projectType ||
        "Fintech Flutter MVP",
      description:
        dictionary.clientCaseStudies?.unionInvestment?.description ||
        "Regulatory-compliant investment platform where rapid development and error-free implementation were critical for market entry",
      businessContext:
        dictionary.clientCaseStudies?.unionInvestment?.businessContext ||
        "Germany's leading asset manager needed fast, reliable development to test retail investment features while maintaining institutional-grade quality",
      results: dictionary.clientCaseStudies?.unionInvestment?.results || [
        "Delivered MVP 5 weeks ahead of internal estimates",
        "Resolved all security compliance issues during development phase",
        "Achieved 100% bug-free launch with comprehensive testing",
        "Streamlined development process for future feature releases",
      ],
      brandColor: "from-emerald-500 to-green-600",
      accentColor: "green",
      icon: Shield,
      logo: "/images/companies/union-investment.png",
      rating: 4.8,
    },
    {
      id: "togg",
      title: dictionary.clientCaseStudies?.togg?.title || "Togg",
      industry: dictionary.clientCaseStudies?.togg?.industry || "Automotive",
      projectType:
        dictionary.clientCaseStudies?.togg?.projectType || "IoT Flutter App",
      description:
        dictionary.clientCaseStudies?.togg?.description ||
        "Turkey's first electric vehicle needed a smart car control system delivered on schedule with perfect reliability for their national launch",
      businessContext:
        dictionary.clientCaseStudies?.togg?.businessContext ||
        "High-stakes national project where meeting deadlines and delivering bug-free software was essential for Turkey's automotive industry credibility",
      results: dictionary.clientCaseStudies?.togg?.results || [
        "Completed development 3 weeks before launch deadline",
        "Fixed all critical IoT connectivity issues during testing phase",
        "Delivered real-time performance without any system failures",
        "Optimized code architecture for scalable vehicle fleet management",
      ],
      brandColor: "from-purple-500 to-indigo-600",
      accentColor: "purple",
      icon: Rocket,
      logo: "/images/companies/togg.png",
      rating: 5.0,
    },
  ];

  return (
    <StaticBackground className="w-full">
      <Section
        id="client-success-stories"
        className="py-20 md:py-28 relative overflow-hidden"
      >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          ref={ref}
          variants={shouldReduceMotion ? {} : containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {/* Standardized Header Section */}
          <motion.div
            variants={shouldReduceMotion ? {} : itemVariants}
            className="text-center"
          >

            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white text-center mb-6 leading-tight">
              {dictionary.title ? (
                <span>
                  {dictionary.title.split(" ").map((word, index) => {
                    const isColoredWord =
                      word.toLowerCase().includes("results") ||
                      word.toLowerCase().includes("growth");
                    return (
                      <span key={index}>
                        {isColoredWord ? (
                          <span className="bg-gradient-to-r from-slate-900 via-slate-700 to-slate-900 dark:from-white dark:via-slate-100 dark:to-white bg-clip-text text-transparent">
                            {word}
                          </span>
                        ) : (
                          word
                        )}
                        {index <
                          (dictionary?.title?.split(" ").length || 0) - 1 &&
                          " "}
                      </span>
                    );
                  })}
                </span>
              ) : (
                <span>
                  {"Proven Results That Drive Growth"
                    .split(" ")
                    .map((word, index) => {
                      const isColoredWord =
                        word.toLowerCase().includes("results") ||
                        word.toLowerCase().includes("growth");
                      return (
                        <span key={index}>
                          {isColoredWord ? (
                            <span className="bg-gradient-to-r from-slate-900 via-slate-700 to-slate-900 dark:from-white dark:via-slate-100 dark:to-white bg-clip-text text-transparent">
                              {word}
                            </span>
                          ) : (
                            word
                          )}
                          {index <
                            "Proven Results That Drive Growth".split(" ")
                              .length -
                              1 && " "}
                        </span>
                      );
                    })}
                </span>
              )}
            </h2>

            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 text-center mb-8 max-w-3xl mx-auto leading-relaxed">
              {dictionary.description ||
                "From startups to enterprise leaders — discover how we've transformed businesses with cutting-edge solutions"}
            </p>

            {/* Enhanced Customer Problem Solutions Showcase */}
            <motion.div
              className="relative py-6"
              initial={{ opacity: 0 }}
              animate={inView ? { opacity: 1 } : {}}
              transition={{ delay: 0.3, duration: 0.8 }}
            >
             
              {/* Enhanced Client Logos Strip - No Containers */}
              <div className="relative overflow-hidden py-6">
                <div className="absolute left-0 top-0 w-16 h-full bg-gradient-to-r from-white dark:from-gray-900 to-transparent z-10" />
                <div className="absolute right-0 top-0 w-16 h-full bg-gradient-to-l from-white dark:from-gray-900 to-transparent z-10" />
                
                <motion.div
                  className="flex space-x-12 items-center"
                  variants={scrollVariants}
                  animate="animate"
                  style={{ width: "max-content" }}
                >
                  {[...clientLogos, ...clientLogos].map((client, index) => (
                    <motion.div
                        key={`${client.name}-${index}`}
                        className="flex-shrink-0 group cursor-pointer"
                        whileHover={{ scale: 1.1, y: -2 }}
                        transition={{ duration: 0.2 }}
                      >
                        <div className="w-30 h-20 rounded-xl overflow-hidden">
                          <Image
                            src={client.logo}
                            alt={`${client.name} Logo`}
                            width={120}
                            height={80}
                            className="w-full h-full object-cover transition-all duration-300 opacity-90 hover:opacity-100 hover:scale-105"
                          />
                        </div>
                      </motion.div>
                  ))}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Client Cards Grid with Performance Optimizations */}
          <motion.div
            variants={shouldReduceMotion ? {} : itemVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {clientsData.map((client, index) => {
              const IconComponent = client.icon;
              const isHovered = hoveredCard === client.id;

              return (
                <motion.div
                  key={client.id}
                  variants={shouldReduceMotion ? {} : cardVariants}
                  className="group relative h-full"
                  onMouseEnter={() => setHoveredCard(client.id)}
                  onMouseLeave={() => setHoveredCard(null)}
                  style={{ willChange: "transform" }} // Performance optimization
                >
                  {/* Enhanced Card Container with glow effects from OurProcessSection */}
                  <div
                    className={`
                    relative h-full bg-white dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-100 dark:border-gray-700/50 overflow-hidden
                    transition-all duration-500 group-hover:shadow-3xl
                    ${isHovered ? "hover:border-transparent shadow-xl -translate-y-2 scale-[1.02]" : ""}
                    ${client.accentColor === "blue" ? "shadow-blue-500/25" : client.accentColor === "green" ? "shadow-emerald-500/25" : "shadow-purple-500/25"}
                  `}
                  >
                    {/* Background gradient effect on hover */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${client.brandColor} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl`}
                    />

                    {/* Hover effect overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl pointer-events-none" />

                    {/* Content Container */}
                    <div className="relative z-10 p-6 h-full flex flex-col">
                      {/* Header with Large Logo and Rating */}
                      <div className="flex items-start justify-between mb-6">
                        <div className="flex items-center space-x-4">
                          <motion.div
                            className="group-hover:scale-110 transition-transform duration-300"
                            whileHover={{ rotate: 5 }}
                          >
                            <div className="w-20 h-20 rounded-xl overflow-hidden flex items-center justify-center">
                              <Image
                                src={client.logo}
                                alt={`${client.title} Logo`}
                                width={80}
                                height={80}
                                className="w-full h-full object-cover drop-shadow-lg"
                              />
                            </div>
                          </motion.div>
                          <div>
                            <h4 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:bg-clip-text group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
                              {client.title}
                            </h4>
                            <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">
                              {client.industry}
                            </p>
                          </div>
                        </div>

                        {/* Enhanced Rating with Animation */}
                        <motion.div
                          className="flex items-center gap-1 bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 px-3 py-2 rounded-xl border border-yellow-200/50 dark:border-yellow-800/30"
                          whileHover={{ scale: 1.05 }}
                        >
                          <motion.div
                            animate={{ rotate: [0, 360] }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "linear",
                            }}
                          >
                            <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          </motion.div>
                          <span className="text-sm font-bold text-yellow-700 dark:text-yellow-400">
                            {client.rating}
                          </span>
                        </motion.div>
                      </div>

                      {/* Project Type Badge */}
                      <div className="inline-block mb-4">
                        <span
                          className={`px-4 py-2 rounded-xl text-sm font-bold bg-gradient-to-r ${client.brandColor} text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                        >
                          {client.projectType}
                        </span>
                      </div>

                      {/* Description */}
                      <p className="hidden">
                        {client.description}
                      </p>

                      {/* Development Results Section with Animated Elements */}
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="w-4 h-4 text-gray-900 dark:text-white" />
                          <span className="text-sm font-bold text-gray-900 dark:text-white">
                            Development Results
                          </span>
                        </div>

                        {/* Animated Performance Chart */}
                        <motion.div
                          className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-3 rounded-xl border border-blue-200/50 dark:border-blue-800/30"
                          whileHover={{ scale: 1.02 }}
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <BarChart3 className="w-3 h-3 text-blue-600" />
                            <span className="text-xs font-medium text-blue-700 dark:text-blue-400">
                              Performance Improvement
                            </span>
                          </div>
                          <div className="flex items-end justify-between gap-2 h-24">
                            {[
                              { value: 40, label: "Speed" },
                              { value: 60, label: "UX" },
                              { value: 85, label: "Engagement" },
                              { value: 95, label: "Revenue" },
                            ].map(({ value, label }, i) => (
                              <motion.div
                                key={i}
                                className="relative bg-gradient-to-t from-blue-500 to-blue-400 rounded-md flex-1"
                                initial={{ height: 0 }}
                                animate={{ height: `${value}%` }}
                                transition={{
                                  delay: i * 0.2,
                                  duration: 0.8,
                                  ease: "easeOut",
                                }}
                              >
                                {/* Percentage label */}
                                <span className="absolute -top-5 left-1/2 -translate-x-1/2 text-[10px] font-medium text-blue-700 dark:text-blue-300 whitespace-nowrap">
                                  {value}%
                                </span>
                                {/* Axis label */}
                                <span className="absolute -bottom-5 left-1/2 -translate-x-1/2 text-[10px] text-gray-600 dark:text-gray-400 whitespace-nowrap">
                                  {label}
                                </span>
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>

                        {client.results
                          .slice(0, 3)
                          .map((result, resultIndex) => (
                            <motion.div
                              key={resultIndex}
                              className="flex items-start gap-3"
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: resultIndex * 0.1 + 1 }}
                              whileHover={{ x: 5 }}
                            >
                              <motion.div
                                animate={{ scale: [1, 1.2, 1] }}
                                transition={{
                                  duration: 2,
                                  repeat: Infinity,
                                  delay: resultIndex * 0.5,
                                }}
                                className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
                              >
                                <CheckCircle className="w-3 h-3 text-white" />
                              </motion.div>
                              <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                                {result}
                              </p>
                            </motion.div>
                          ))}

                        {client.results.length > 3 && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 italic mt-2 pl-8">
                            +{client.results.length - 3} more achievements
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>

          {/* Minimal Call to Action */}
          <motion.div
            variants={shouldReduceMotion ? {} : itemVariants}
            className="text-center max-w-lg mx-auto"
          >
            <motion.a
              href="#contact"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-slate-800 to-slate-900 hover:from-slate-900 hover:to-black dark:from-slate-700 dark:to-slate-800 dark:hover:from-slate-600 dark:hover:to-slate-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl group"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <span>
                {dictionary?.cta?.startProject || "Start Project Now"}
              </span>
              <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
            </motion.a>
          </motion.div>
        </motion.div>
      </div>
      </Section>
    </StaticBackground>
  );
};

// Export both names for backward compatibility
export const SolutionsPortfolioSection = ClientSuccessStoriesSection;