import { useEffect, useRef, useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface FlipGalleryImage {
  title: string;
  url: string;
}

interface FlipGalleryProps {
  images?: FlipGalleryImage[];
  autoAdvance?: boolean;
  autoAdvanceInterval?: number;
  onImageChange?: (index: number) => void;
  currentIndex?: number;
}

const defaultImages = [
  { title: '<PERSON>', url: 'https://picsum.photos/id/870/600/1000' },
  { title: '<PERSON>', url: 'https://picsum.photos/id/883/600/1000' },
  { title: '<PERSON> Beauvillain', url: 'https://picsum.photos/id/478/600/1000' },
  { title: '<PERSON>', url: 'https://picsum.photos/id/903/600/1000' },
  { title: 'Ram<PERSON> Checchi', url: 'https://picsum.photos/id/503/600/1000' }
];

const FLIP_SPEED = 750;
const flipTiming = { duration: FLIP_SPEED, iterations: 1 };

// flip down
const flipAnimationTop = [
  { transform: 'rotateX(0)' },
  { transform: 'rotateX(-90deg)' },
  { transform: 'rotateX(-90deg)' }
];
const flipAnimationBottom = [
  { transform: 'rotateX(90deg)' },
  { transform: 'rotateX(90deg)' },
  { transform: 'rotateX(0)' }
];

// flip up
const flipAnimationTopReverse = [
  { transform: 'rotateX(-90deg)' },
  { transform: 'rotateX(-90deg)' },
  { transform: 'rotateX(0)' }
];
const flipAnimationBottomReverse = [
  { transform: 'rotateX(0)' },
  { transform: 'rotateX(90deg)' },
  { transform: 'rotateX(90deg)' }
];

export default function FlipGallery({ 
  images = defaultImages, 
  autoAdvance = false, 
  autoAdvanceInterval = 4000,
  onImageChange,
  currentIndex: externalIndex 
}: FlipGalleryProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const uniteRef = useRef<NodeListOf<HTMLElement>>();
  const autoAdvanceRef = useRef<NodeJS.Timeout>();
  const [currentIndex, setCurrentIndex] = useState(0);

  // initialise first image once
  useEffect(() => {
    if (!containerRef.current) return;
    uniteRef.current = containerRef.current.querySelectorAll(".unite");
    defineFirstImg();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Auto advance functionality
  useEffect(() => {
    if (autoAdvance) {
      // Clear any existing timer
      if (autoAdvanceRef.current) {
        clearInterval(autoAdvanceRef.current);
      }
      
      autoAdvanceRef.current = setInterval(() => {
        updateIndex(1);
      }, autoAdvanceInterval);

      return () => {
        if (autoAdvanceRef.current) {
          clearInterval(autoAdvanceRef.current);
        }
      };
    }
  }, [autoAdvance, autoAdvanceInterval, currentIndex]);

  // Initial setup - notify parent component on first load
  useEffect(() => {
    if (onImageChange && currentIndex === 0) {
      onImageChange(currentIndex);
    }
  }, [onImageChange]); // Only run once on mount

  // Sync with external index changes
  useEffect(() => {
    if (externalIndex !== undefined && externalIndex !== currentIndex) {
      setCurrentIndex(externalIndex);
      updateGallery(externalIndex, false);
    }
  }, [externalIndex]); // eslint-disable-line react-hooks/exhaustive-deps

  const defineFirstImg = () => {
    setActiveImage();
    setImageTitle();
  };

  const setActiveImage = (el?: HTMLElement) => {
    // Update the main gallery container background image
    if (containerRef.current) {
      containerRef.current.style.backgroundImage = `url('${images[currentIndex].url}')`;
    }
  };

  const setImageTitle = () => {
    const gallery = containerRef.current;
    if (!gallery) return;
    gallery.setAttribute("data-title", images[currentIndex].title);
    gallery.style.setProperty("--title-y", "0");
    gallery.style.setProperty("--title-opacity", "1");
  };

  const updateGallery = (nextIndex: number, isReverse = false) => {
    const gallery = containerRef.current;
    if (!gallery) return;

    // Simple fade transition
    gallery.style.transition = "background-image 0.5s ease-in-out";

    // hide title
    gallery.style.setProperty("--title-y", "-1rem");
    gallery.style.setProperty("--title-opacity", "0");
    gallery.setAttribute("data-title", "");

    // Update the background image directly
    gallery.style.backgroundImage = `url('${images[nextIndex].url}')`;

    // reveal new title
    setTimeout(setImageTitle, 250);
  };

  const updateIndex = (increment: number) => {
    const inc = Number(increment);
    const newIndex = (currentIndex + inc + images.length) % images.length;
    const isReverse = inc < 0;
    setCurrentIndex(newIndex);
    updateGallery(newIndex, isReverse);

    // Notify parent component when the new image becomes visible
    if (onImageChange) {
      setTimeout(() => {
        onImageChange(newIndex);
      }, FLIP_SPEED * 0.4); // Sync with when the new image becomes visible (reduced delay)
    }
  };

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div
        className="relative w-full h-full"
        style={
          {
            "--gallery-bg-color": "rgba(255 255 255 / 0.075)",
          } as React.CSSProperties
        }
      >
        {/* Single complete image display */}
        <div
          id="flip-gallery"
          ref={containerRef}
          className="relative w-full h-full text-center overflow-hidden rounded-xl"
          style={{
            backgroundImage: `url('${images[currentIndex]?.url}')`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        >
          {/* Hidden elements for compatibility */}
          <div className="unite hidden"></div>
          <div className="unite hidden"></div>
          <div className="unite hidden"></div>
          <div className="unite hidden"></div>
        </div>

        {/* navigation - hidden in iPhone frame */}
        <div className="hidden">
          <button
            type="button"
            onClick={() => updateIndex(-1)}
            title="Previous"
            className="text-white opacity-75 hover:opacity-100 hover:scale-125 transition"
          >
            <ChevronLeft size={20} />
          </button>
          <button
            type="button"
            onClick={() => updateIndex(1)}
            title="Next"
            className="text-white opacity-75 hover:opacity-100 hover:scale-125 transition"
          >
            <ChevronRight size={20} />
          </button>
        </div>
      </div>

      {/* component-scoped styles */}
      <style>{`
        #flip-gallery::before {
          content: attr(data-title);
          color: rgba(255 255 255 / 0.75);
          font-size: 0.75rem;
          left: -0.5rem;
          position: absolute;
          top: calc(100% + 1rem);
          line-height: 2;
          opacity: var(--title-opacity, 0);
          transform: translateY(var(--title-y, 0));
          transition: opacity 500ms ease-in-out, transform 500ms ease-in-out;
        }
      `}</style>
    </div>
  );
}