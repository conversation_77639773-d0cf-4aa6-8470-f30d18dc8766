"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';

interface CarouselImage {
  src: string;
  alt: string;
  title?: string;
  description?: string;
}

interface ImageCarouselProps {
  images: CarouselImage[];
  width?: number;
  height?: number;
  autoPlay?: boolean;
  interval?: number;
  radius?: number;
  rotationAngle?: number;
  className?: string;
  onImageChange?: (index: number) => void;
}

const SimpleImageCarousel: React.FC<ImageCarouselProps> = ({
  images,
  width = 300,
  height,
  autoPlay = true,
  interval = 4000,
  radius = 500,
  rotationAngle = 0,
  className = "",
  onImageChange,
}) => {
  // Ensure we have valid dimensions
  const containerWidth = Math.max(width, 200);
  const phoneHeight = height ?? Math.round(containerWidth * 2.0); // Better phone aspect ratio

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Reset index if images change
  useEffect(() => {
    if (currentIndex >= images.length) {
      setCurrentIndex(0);
    }
  }, [images.length, currentIndex]);

  useEffect(() => {
    if (!autoPlay || images.length <= 1) {
      setIsLoading(false);
      return;
    }

    const timer = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % images.length);
    }, interval);

    setIsLoading(false);
    return () => clearInterval(timer);
  }, [autoPlay, interval, images.length]);

  const goToPrevious = () => {
    if (images.length <= 1) return;
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const goToNext = () => {
    if (images.length <= 1) return;
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const goToSlide = (index: number) => {
    if (index >= 0 && index < images.length) {
      setCurrentIndex(index);
      onImageChange?.(index);
    }
  };

  // Call onImageChange when currentIndex changes
  useEffect(() => {
    onImageChange?.(currentIndex);
  }, [currentIndex, onImageChange]);

  if (images.length === 0) {
    return (
      <div
        className="flex items-center justify-center rounded-xl bg-gray-100 dark:bg-gray-800"
        style={{ width: containerWidth, height: phoneHeight }}
      >
        <p className="text-gray-500 dark:text-gray-400">No images available</p>
      </div>
    );
  }


  return (
    <div
      className={`relative overflow-hidden ${className}`}
      style={{
        width: containerWidth * 1.6,
        height: phoneHeight * 1.2,
        perspective: "2000px",
      }}
    >


      <div
        className="relative w-full h-full flex items-center justify-center"
      >
        <AnimatePresence mode="wait">
          {images.map((image, index) => {
            const isCenter = index === currentIndex;
            const isPrev = index === (currentIndex - 1 + images.length) % images.length;
            const isNext = index === (currentIndex + 1) % images.length;
            const isVisible = isCenter || isPrev || isNext;

            if (!isVisible) return null;

            const offsetX = (index - currentIndex) * 120;
            const scale = isCenter ? 1 : 0.85;
            const rotateY = isCenter ? 0 : (index < currentIndex ? -25 : 25);
            const opacity = isCenter ? 1 : 0.6;

            return (
              <motion.div
                key={`${index}-${currentIndex}`}
                className="absolute left-1/2 top-1/2"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ 
                  opacity,
                  scale,
                  rotateY,
                  x: `calc(-50% + ${offsetX}px)`,
                  y: "-50%"
                }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ 
                  duration: 0.6,
                  ease: [0.4, 0, 0.2, 1]
                }}
                style={{
                  zIndex: isCenter ? 10 : isPrev || isNext ? 5 : 0,
                  width: containerWidth,
                  height: phoneHeight,
                  transformStyle: 'preserve-3d',
                }}
              >
              {/* Phone frame with image */}
              <div className="relative w-full h-full">
                {/* Phone frame */}
                <div className="absolute inset-0 bg-black rounded-[2.5rem] p-2 shadow-xl">
                  <div className="relative w-full h-full bg-gray-900 rounded-[2rem] overflow-hidden">
                    {/* Screen content */}
                    <div className="absolute inset-0 bg-white dark:bg-gray-900 overflow-hidden">
                      <Image
                        src={image.src}
                        alt={image.alt}
                        fill
                        className="object-contain"
                        sizes="(max-width: 640px) 400px, (max-width: 1024px) 600px, 800px"
                        quality={90}
                        priority={index === 0}
                        onError={(e) => {
                          console.error(`Failed to load image: ${image.src}`);
                          e.currentTarget.style.display = 'none';
                        }}
                        onLoad={() => {
                          if (index === 0) setIsLoading(false);
                        }}
                      />

                      {(image.title || image.description) && (
                        <div className="absolute bottom-0 left-0 right-0 p-4 text-white bg-gradient-to-t from-black/60 to-transparent">
                          {image.title && (
                            <h3 className="text-lg font-semibold mb-1 drop-shadow-lg">
                              {image.title}
                            </h3>
                          )}
                          {image.description && (
                            <p className="text-sm opacity-90 drop-shadow-lg">
                              {image.description}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Navigation buttons - only show if more than 1 image */}
      {images.length > 1 && (
        <>
          <motion.button
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 dark:bg-black/20 dark:hover:bg-black/30 text-gray-800 dark:text-white rounded-full p-3 backdrop-blur-sm transition-all duration-300 z-20 shadow-lg"
            onClick={goToPrevious}
            aria-label="Previous image"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
            </svg>
          </motion.button>
          
          <motion.button
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 dark:bg-black/20 dark:hover:bg-black/30 text-gray-800 dark:text-white rounded-full p-3 backdrop-blur-sm transition-all duration-300 z-20 shadow-lg"
            onClick={goToNext}
            aria-label="Next image"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
            </svg>
          </motion.button>
        </>
      )}
      
      {/* Dots indicator - only show if more than 1 image */}
      {images.length > 1 && (
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-10">
          {images.map((_, index) => (
            <motion.button
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-blue-500 dark:bg-blue-400 scale-125' 
                  : 'bg-white/60 dark:bg-gray-400 hover:bg-blue-300 dark:hover:bg-blue-300'
              }`}
              onClick={() => goToSlide(index)}
              aria-label={`Go to image ${index + 1}`}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default SimpleImageCarousel;