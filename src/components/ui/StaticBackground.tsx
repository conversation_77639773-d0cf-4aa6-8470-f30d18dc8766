"use client";

import React from "react";

interface StaticBackgroundProps {
  className?: string;
  children?: React.ReactNode;
}

// Simple background component that exactly matches HeroSection background
export const StaticBackground: React.FC<StaticBackgroundProps> = ({
  className = "",
  children,
}) => {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Professional gradient background - exactly like HeroSection */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50/80 via-white to-blue-50/20 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 z-0" />

      {/* Subtle geometric pattern overlay - exactly like HeroSection */}
      <div className="absolute inset-0 opacity-[0.03] dark:opacity-[0.08]">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(90deg, rgba(15, 23, 42, 0.4) 1px, transparent 1px),
              linear-gradient(0deg, rgba(15, 23, 42, 0.4) 1px, transparent 1px)
            `,
            backgroundSize: "40px 40px",
          }}
        />
      </div>

      {/* Professional accent elements - exactly like HeroSection */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 dark:from-blue-400/10 dark:to-indigo-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-violet-500/3 to-purple-500/3 dark:from-violet-400/8 dark:to-purple-400/8 rounded-full blur-3xl" />

      {/* Content layer */}
      <div className="relative z-10">{children}</div>
    </div>
  );
};

export default StaticBackground;