"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/navigation";
import { BookOpen, X, TrendingUp, Clock, Eye } from "lucide-react";
import Image from "next/image";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  readingTime: number;
  views: number;
  category: string;
  featuredImage: string;
  slug: string;
  publishedAt: string;
}

interface FloatingBlogButtonProps {
  locale: string;
}

import { blogService } from "@/lib/blog-service";

export const FloatingBlogButton: React.FC<FloatingBlogButtonProps> = ({
  locale,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentPostIndex, setCurrentPostIndex] = useState(0);
  const router = useRouter();

  // Get recent posts from service
  const recentPosts = blogService.getRecentPosts(3, locale);

  // Auto-rotate through posts
  useEffect(() => {
    if (isOpen && recentPosts.length > 1) {
      const interval = setInterval(() => {
        setCurrentPostIndex((prev) => (prev + 1) % recentPosts.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [isOpen, recentPosts.length]);

  const currentPost = recentPosts[currentPostIndex];

  // Don't render if no posts available
  if (!currentPost || recentPosts.length === 0) {
    return null;
  }

  const handleBlogClick = () => {
    router.push(`/${locale}/blog`);
  };

  const handlePostClick = (slug: string) => {
    router.push(`/${locale}/blog/${slug}`);
    setIsOpen(false);
  };

  return (
    <div className="fixed bottom-5 right-24 z-50">
      {/* Speech Bubble */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="absolute bottom-20 right-0 mb-4"
          >
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-4 w-80 relative">
              {/* Arrow pointing to the button */}
              <div className="absolute -bottom-2 right-6 w-4 h-4 bg-white dark:bg-gray-800 border-r border-b border-gray-200 dark:border-gray-700 transform rotate-45"></div>

              {/* Close button */}
              <button
                onClick={() => setIsOpen(false)}
                className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <X className="w-4 h-4 text-gray-500" />
              </button>

              {/* Header */}
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-3 h-3 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                  Aktueller Artikel
                </span>
                <div className="flex gap-1 ml-auto">
                  {recentPosts.map((_, index) => (
                    <div
                      key={index}
                      className={`w-1.5 h-1.5 rounded-full transition-colors ${
                        index === currentPostIndex
                          ? "bg-blue-500"
                          : "bg-gray-300 dark:bg-gray-600"
                      }`}
                    />
                  ))}
                </div>
              </div>

              {/* Current Post */}
              <motion.div
                key={currentPostIndex}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="cursor-pointer group"
                onClick={() => handlePostClick(currentPost.slug)}
              >
                {/* Post Image */}
                <div className="relative h-32 rounded-lg overflow-hidden mb-3">
                  <Image
                    src={currentPost.featuredImage}
                    alt={currentPost.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

                  {/* Category Badge */}
                  <div className="absolute top-2 left-2">
                    <span className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      {currentPost.category.toUpperCase()}
                    </span>
                  </div>
                </div>

                {/* Post Content */}
                <h3 className="font-bold text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {currentPost.title}
                </h3>

                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                  {currentPost.excerpt}
                </p>

                {/* Meta Info */}
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{currentPost.readingTime} Min</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      <span>{currentPost.views.toLocaleString()}</span>
                    </div>
                  </div>
                  <span>
                    {new Date(currentPost.publishedAt).toLocaleDateString(
                      "de-DE"
                    )}
                  </span>
                </div>
              </motion.div>

              {/* View All Button */}
              <button
                onClick={handleBlogClick}
                className="w-full mt-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 text-sm"
              >
                Alle Artikel ansehen
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Floating Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        className={`
          relative w-14 h-14 rounded-full shadow-2xl transition-all duration-300
          ${
            isOpen
              ? "bg-gradient-to-r from-purple-500 to-blue-500"
              : "bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
          }
        `}
      >
        <div className="flex items-center justify-center w-full h-full text-white">
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <BookOpen className="w-6 h-6" />
          </motion.div>
        </div>

        {/* Notification Dot */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 1 }}
          className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center"
        >
          <span className="text-white text-xs font-bold">
            {recentPosts.length}
          </span>
        </motion.div>

        {/* Pulse Effect */}
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.7, 0, 0.7],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"
        />
      </motion.button>

      {/* Tooltip */}
      {!isOpen && (
        <motion.div
          initial={{ opacity: 0, x: 10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 2 }}
          className="absolute top-1/2 right-16 -translate-y-1/2 bg-gray-900 text-white px-3 py-1 rounded-lg text-sm whitespace-nowrap pointer-events-none"
        >
          Tech Blog
          <div className="absolute -right-1 top-1/2 -translate-y-1/2 w-2 h-2 bg-gray-900 transform rotate-45"></div>
        </motion.div>
      )}
    </div>
  );
};
