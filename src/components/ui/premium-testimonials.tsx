'use client'

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef, useMemo } from 'react';
import { <PERSON>uote, Star, ArrowLeft, ArrowRight, Sparkles, Heart, Stethoscope, Wrench, TrendingUp, Building2, User } from 'lucide-react';
import Image from 'next/image';
import { type Dictionary } from '@/lib/dictionary';
import { UnifiedBackground } from '@/components/ui/UnifiedBackground';

interface PremiumTestimonialsProps {
  dictionary: Dictionary["testimonials"];
}

export function PremiumTestimonials({ dictionary }: PremiumTestimonialsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Real testimonials with enhanced content
  const testimonials = useMemo(
    () => (dictionary as any)?.testimonials || [
      {
        quote: "Zuverlässig und strukturiert! <PERSON> ist ein äußerst zu<PERSON>lässiger und sympathischer Mobile App Entwickler, der sowohl bei technischen Komponenten als auch bei der User Experience keine Wünsche offen lässt!",
        name: "Emin C.",
        designation: "CEO at Ultimind",
        src: "/images/testimonials/emin.jpeg",
        company: "Ultimind",
        companyLogo: "/images/companies/lumeus.png",
        industry: "Technology",
        rating: 5,
        projectType: "Mobile App Development",
        deliveryTime: "Pünktlich geliefert",
        badge: "Verifizierter Kunde",
        results: ["Perfekte User Experience", "Strukturierte Umsetzung", "Keine Wünsche offen"]
      },
      {
        quote: "Viktor lernte ich in einem Projekt als sehr kompetenten Mobile Entwickler kennen und schätzen. Seine schnelle Auffassungsgabe und sein hohes Engagement bei der Problemlösung sind bemerkenswert. Durch ihn ist das Team enorm in der Leistung, im Zusammenhalt untereinander und in der gesamten Teamentwicklung gewachsen.",
        name: "Stefanie W.",
        designation: "Project Manager at Union Investment Real Estate GmbH",
        src: "/images/testimonials/stefanie.jpeg",
        company: "Union Investment",
        companyLogo: "/images/companies/union-investment.png",
        industry: "Real Estate",
        rating: 5,
        projectType: "Team Collaboration",
        deliveryTime: "Erwartungen übertroffen",
        badge: "Enterprise Kunde",
        results: ["Team Performance gesteigert", "Besserer Zusammenhalt", "Kompetente Problemlösung"]
      },
      {
        quote: "Ich bin dankbar für die unglaubliche Zusammenarbeit, die wir beim Mobile Application Projekt hatten. Seine Expertise in der Frontend-Entwicklung war wirklich von unschätzbarem Wert, und ich bin begeistert von den hervorragenden Ergebnissen, die wir gemeinsam erzielt haben.",
        name: "Mohammed S.",
        designation: "Product UX Designer at Togg",
        src: "/images/testimonials/mohammed.jpeg",
        company: "Togg",
        companyLogo: "/images/companies/togg.png",
        industry: "Automotive",
        rating: 5,
        projectType: "UI/UX Development",
        deliveryTime: "Außergewöhnliche Qualität",
        badge: "Premium Partner",
        results: ["Unschätzbare Frontend-Expertise", "Hervorragende Ergebnisse", "Perfekte Zusammenarbeit"]
      },
      {
        quote: "Als Unternehmerin in der Immobilienbranche suchte ich professionelle Unterstützung bei portal-, website- und app-basierten Lösungen. Viktor konnte mir komplexe Zusammenhänge einfach erklären und verstand sofort meine Zielsetzungen.",
        name: "Natalia W.",
        designation: "Global Real Estate Expert at Walenwein Immobilien",
        src: "/images/testimonials/natalia.jpeg",
        company: "Lufthansa",
        companyLogo: "/images/companies/lufthansa.png",
        industry: "Aviation",
        rating: 5,
        projectType: "Full-Stack Development",
        deliveryTime: "Perfekte Timeline",
        badge: "Langzeit-Partner",
        results: ["Komplexe Themen einfach erklärt", "Sofortiges Verständnis", "Professionelle Umsetzung"]
      },
      // Anonymous testimonials
      {
        quote: "Fantastische Arbeit! Die App funktioniert genau so, wie wir sie uns vorgestellt haben. Viktor ist sehr professionell und hält sich an alle Termine.",
        name: "M. Schmidt",
        designation: "Geschäftsführer",
        src: "/images/testimonials/placeholder-male.svg",
        company: "Lokales Unternehmen",
        companyLogo: null,
        industry: "Business",
        rating: 5,
        projectType: "Business App",
        deliveryTime: "Termingerecht",
        badge: "Zufriedener Kunde",
        results: ["Funktioniert perfekt", "Professionelle Arbeit", "Alle Termine eingehalten"],
        isAnonymous: true
      },
      {
        quote: "Excellent communication and technical skills. Viktor delivered exactly what we needed for our startup. Highly recommended!",
        name: "A. Johnson",
        designation: "Startup Founder",
        src: "/images/testimonials/placeholder-female.svg",
        company: "Tech Startup",
        companyLogo: null,
        industry: "Technology",
        rating: 5,
        projectType: "MVP Development",
        deliveryTime: "Ahead of schedule",
        badge: "International Client",
        results: ["Excellent communication", "Perfect technical execution", "Startup-ready solution"],
        isAnonymous: true
      },
      {
        quote: "Sehr zufrieden mit der Entwicklung unserer Gesundheits-App. Viktor hat unsere Anforderungen perfekt umgesetzt und war immer erreichbar für Fragen.",
        name: "Dr. K. Mueller",
        designation: "Medizinischer Direktor",
        src: "/images/testimonials/placeholder-doctor.svg",
        company: "Medizinische Praxis",
        companyLogo: null,
        industry: "Healthcare",
        rating: 5,
        projectType: "Healthcare App",
        deliveryTime: "Wie versprochen",
        badge: "Medizin-Experte",
        results: ["Perfekte Umsetzung", "Immer erreichbar", "Gesundheits-App Expertise"],
        isAnonymous: true
      }
    ],
    [dictionary]
  );

  // Industry icon mapping
  const industryIcons = {
    doctor: Stethoscope,
    handwerk: Wrench,
    startup: TrendingUp,
    Technology: TrendingUp,
    "Real Estate": Building2,
    Automotive: Wrench,
    Aviation: Building2,
    Business: Building2,
    Healthcare: Stethoscope,
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setDirection(1);
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 8000);

    return () => clearInterval(timer);
  }, [testimonials.length]);

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.9,
      rotateY: direction > 0 ? 20 : -20
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
      scale: 1,
      rotateY: 0
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.9,
      rotateY: direction < 0 ? 20 : -20
    })
  };

  const fadeInUp = {
    hidden: { opacity: 0, y: 60 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8, 
        ease: [0.23, 0.86, 0.39, 0.96] 
      }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };

  const nextTestimonial = () => {
    setDirection(1);
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setDirection(-1);
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <UnifiedBackground className="py-20 lg:py-32 bg-white dark:bg-gray-900 overflow-hidden">
      <section id="testimonials" className="relative">

      <motion.div 
        ref={containerRef}
        className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8"
        variants={staggerContainer}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
      >
        {/* Header */}
        <motion.div 
          className="text-center mb-16"
          variants={fadeInUp}
        >
          <motion.div
            className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 border border-purple-100 dark:border-purple-800 mb-8"
            whileHover={{ scale: 1.05, borderColor: "rgba(147, 51, 234, 0.3)" }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            >
              <Heart className="h-4 w-4" />
            </motion.div>
            <span className="text-sm font-bold">
              {dictionary?.badge || "Kundenstimmen"}
            </span>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          </motion.div>

          <motion.h2 
            className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight"
            variants={fadeInUp}
          >
            {dictionary?.title || "Das sagen unsere Kunden"}
          </motion.h2>
          
          <motion.p 
            className="text-lg lg:text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed mb-12"
            variants={fadeInUp}
          >
            {dictionary?.subtitle || "Echte Erfahrungen von Ärzten, Handwerkern, Unternehmern"}
          </motion.p>

          {/* Companies with ratings - horizontal scroll */}
          <motion.div 
          
            className="w-full overflow-hidden"
            variants={fadeInUp}
          >
            <div className="flex space-x-8 lg:space-x-12 animate-scroll">
              {/* First set of companies */}
              {[
                { name: "Adesso", logo: "/images/companies/adesso.png" },
                { name: "Hegla", logo: "/images/companies/hegla.png" },
                { name: "Innovate", logo: "/images/companies/innovate.png" },
                { name: "Lufthansa", logo: "/images/companies/lufthansa.png" },
                { name: "Lumeus", logo: "/images/companies/lumeus.png" },
                { name: "Togg", logo: "/images/companies/togg.png" },
                { name: "Union Investment", logo: "/images/companies/union-investment.png" },
              ].map((company, index) => (
                <motion.div
                  key={`first-${index}`}
                  className="flex-shrink-0 text-center min-w-[140px]"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                >
                  <img
                    src={company.logo}
                    alt={`${company.name} Logo`}
                    className="h-12 w-auto object-contain mx-auto mb-3 opacity-80 hover:opacity-100 transition-opacity duration-300 rounded-lg"
                  />
                  <div className="flex justify-center gap-1 mb-2">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-sm font-semibold text-gray-700 dark:text-gray-300">4.9/5</p>
                </motion.div>
              ))}

              {/* Duplicate set for infinite effect */}
              {[
                { name: "Adesso", logo: "/images/companies/adesso.png" },
                { name: "Hegla", logo: "/images/companies/hegla.png" },
                { name: "Innovate", logo: "/images/companies/innovate.png" },
                { name: "Lufthansa", logo: "/images/companies/lufthansa.png" },
                { name: "Lumeus", logo: "/images/companies/lumeus.png" },
                { name: "Togg", logo: "/images/companies/togg.png" },
                { name: "Union Investment", logo: "/images/companies/union-investment.png" },
              ].map((company, index) => (
                <motion.div
                  key={`second-${index}`}
                  className="flex-shrink-0 text-center min-w-[140px]"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                >
                  <img
                    src={company.logo}
                    alt={`${company.name} Logo`}
                    className="h-12 w-auto object-contain mx-auto mb-3 opacity-80 hover:opacity-100 transition-opacity duration-300 rounded-lg"
                  />
                  <div className="flex justify-center gap-1 mb-2">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-sm font-semibold text-gray-700 dark:text-gray-300">4.9/5</p>
                </motion.div>
              ))}

              {/* Third set for seamless loop */}
              {[
                { name: "Adesso", logo: "/images/companies/adesso.png" },
                { name: "Hegla", logo: "/images/companies/hegla.png" },
                { name: "Innovate", logo: "/images/companies/innovate.png" },
                { name: "Lufthansa", logo: "/images/companies/lufthansa.png" },
                { name: "Lumeus", logo: "/images/companies/lumeus.png" },
                { name: "Togg", logo: "/images/companies/togg.png" },
                { name: "Union Investment", logo: "/images/companies/union-investment.png" },
              ].map((company, index) => (
                <motion.div
                  key={`third-${index}`}
                  className="flex-shrink-0 text-center min-w-[140px]"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                >
                  <img
                    src={company.logo}
                    alt={`${company.name} Logo`}
                    className="h-12 w-auto object-contain mx-auto mb-3 opacity-80 hover:opacity-100 transition-opacity duration-300 rounded-lg"
                  />
                  <div className="flex justify-center gap-1 mb-2">
                    {[...Array(4)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-sm font-semibold text-gray-700 dark:text-gray-300">4.9/5</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>

        {/* Main Testimonial Display */}
        <div className="relative max-w-5xl mx-auto mb-16">
          <div className="relative h-auto min-h-[400px] perspective-1000">
            <AnimatePresence initial={false} custom={direction}>
              <motion.div
                key={currentIndex}
                custom={direction}
                variants={slideVariants}
                initial="enter"
                animate="center"
                exit="exit"
                transition={{
                  x: { type: "spring", stiffness: 300, damping: 30 },
                  opacity: { duration: 0.4 },
                  scale: { duration: 0.4 },
                  rotateY: { duration: 0.6 }
                }}
                className="absolute inset-0"
              >
                <div className="relative bg-white dark:bg-gray-900/50 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 md:p-10 shadow-lg hover:shadow-xl transition-all duration-500 overflow-hidden group">
                  {/* Subtle gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 dark:from-blue-900/10 dark:via-transparent dark:to-purple-900/10 rounded-2xl" />

                  {/* Elegant quote mark */}
                  <motion.div
                    className="absolute top-6 right-6 opacity-10 dark:opacity-5"
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 4, repeat: Infinity }}
                  >
                    <Quote className="w-12 h-12 text-blue-600 dark:text-blue-400" />
                  </motion.div>

                  <div className="relative z-10 space-y-6">
                    {/* User Information - Clean Layout */}
                    <div className="flex items-start gap-4">
                      {/* Avatar */}
                      <motion.div
                        className="relative flex-shrink-0"
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="w-16 h-16 rounded-2xl overflow-hidden border-2 border-gray-200 dark:border-gray-600 shadow-sm">
                          {testimonials[currentIndex].isAnonymous ? (
                            <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                              <User className="w-7 h-7 text-gray-400 dark:text-gray-500" />
                            </div>
                          ) : (
                            <Image 
                              src={testimonials[currentIndex].src} 
                              alt={testimonials[currentIndex].name}
                              width={64}
                              height={64}
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>
                      </motion.div>

                      {/* User Details */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                          {testimonials[currentIndex].name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 leading-relaxed">
                          {testimonials[currentIndex].designation}
                        </p>
                        
                        {/* Company with Logo */}
                        <div className="flex items-center gap-2 mb-3">
                          {testimonials[currentIndex].companyLogo ? (
                            <div className="w-5 h-5 rounded-md overflow-hidden border border-gray-200 dark:border-gray-600 flex items-center justify-center bg-white dark:bg-gray-800">
                              <img
                                src={testimonials[currentIndex].companyLogo}
                                alt={`${testimonials[currentIndex].company} Logo`}
                                className="w-full h-full object-contain"
                              />
                            </div>
                          ) : (
                            <Building2 className="w-4 h-4 text-gray-400" />
                          )}
                          <span className="text-sm text-gray-500 dark:text-gray-400 font-medium">
                            {testimonials[currentIndex].company}
                          </span>
                        </div>

                        {/* Rating */}
                        <div className="flex items-center gap-2">
                          <div className="flex gap-0.5">
                            {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                              <motion.div
                                key={i}
                                initial={{ opacity: 0, scale: 0 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: 0.6 + i * 0.1, duration: 0.3 }}
                              >
                                <Star className="w-4 h-4 fill-amber-400 text-amber-400" />
                              </motion.div>
                            ))}
                          </div>
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            {testimonials[currentIndex].rating}.0
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Elegant separator */}
                    <motion.div 
                      className="w-16 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: 64 }}
                      transition={{ delay: 0.5, duration: 0.8 }}
                    />

                    {/* Quote Content - Now below profile */}
                    <motion.blockquote 
                      className="text-lg md:text-xl lg:text-2xl text-gray-800 dark:text-gray-100 leading-relaxed font-normal tracking-wide"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2, duration: 0.6 }}
                    >
                      "{testimonials[currentIndex].quote}"
                    </motion.blockquote>

                    {/* Key Results - Minimal Tags */}
                    <motion.div 
                      className="flex flex-wrap gap-2"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.8, duration: 0.6 }}
                    >
                      {testimonials[currentIndex].results?.slice(0, 3).map((result: string, i: number) => (
                        <motion.span
                          key={i}
                          className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200/50 dark:border-blue-800/50"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.9 + i * 0.1, duration: 0.4 }}
                          whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.1)" }}
                        >
                          {result}
                        </motion.span>
                      ))}
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Controls */}
          <div className="flex justify-center items-center gap-6 mt-8">
            <motion.button
              onClick={prevTestimonial}
              className="p-3 rounded-full bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:bg-accent hover:text-white hover:border-accent transition-all"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft className="w-5 h-5" />
            </motion.button>

            {/* Dots Indicator */}
            <div className="flex gap-3">
              {testimonials.map((_: any, index: number) => (
                <motion.button
                  key={index}
                  onClick={() => {
                    setDirection(index > currentIndex ? 1 : -1);
                    setCurrentIndex(index);
                  }}
                  className={`w-3 h-3 rounded-full transition-all ${
                    index === currentIndex 
                      ? 'bg-accent scale-125' 
                      : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                  }`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                />
              ))}
            </div>

            <motion.button
              onClick={nextTestimonial}
              className="p-3 rounded-full bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:bg-accent hover:text-white hover:border-accent transition-all"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowRight className="w-5 h-5" />
            </motion.button>
          </div>
        </div>

        {/* Trust Indicators - Project themed */}
        <motion.div 
          className="grid grid-cols-3 gap-4 sm:gap-8 max-w-4xl mx-auto"
          variants={staggerContainer}
        >
          {[
            { 
              number: "100%", 
              labelKey: "satisfiedCustomers",
              label: dictionary?.stats?.satisfiedCustomers || "Satisfied Customers", 
              icon: Heart, 
              gradient: "from-green-500 to-emerald-600" 
            },
            { 
              number: "5⭐", 
              labelKey: "averageRating",
              label: dictionary?.stats?.averageRating || "Average Rating", 
              icon: Star, 
              gradient: "from-yellow-500 to-orange-600" 
            },
            { 
              number: "15+", 
              labelKey: "successfulApps",
              label: dictionary?.stats?.successfulApps || "Successful Apps", 
              icon: Sparkles, 
              gradient: "from-accent to-blue-600" 
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="text-center group"
              variants={fadeInUp}
              whileHover={{ scale: 1.05 }}
            >
              <motion.div
                className={`w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br ${stat.gradient} rounded-xl sm:rounded-2xl flex items-center justify-center mb-2 sm:mb-4 shadow-lg mx-auto`}
                animate={{ rotate: [0, 5, 0] }}
                transition={{ duration: 4, repeat: Infinity, delay: index * 0.5 }}
              >
                <stat.icon className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </motion.div>
              <motion.div
                className="text-2xl sm:text-3xl md:text-4xl font-bold text-primary dark:text-white mb-1 sm:mb-2"
                animate={{ opacity: [0.8, 1, 0.8] }}
                transition={{ duration: 2, repeat: Infinity, delay: index * 0.3 }}
              >
                {stat.number}
              </motion.div>
              <div className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm font-medium group-hover:text-accent transition-colors leading-tight">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
        
      </motion.div>
      </section>
    </UnifiedBackground>
  );
}