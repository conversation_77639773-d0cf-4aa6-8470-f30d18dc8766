"use client";

import React from "react";
import Image from "next/image";
import { Card } from "./CardSwap";

interface MockupCardProps {
  imageSrc: string;
  imageAlt: string;
  title?: string;
  description?: string;
}

const MockupCard: React.FC<MockupCardProps> = ({
  imageSrc,
  imageAlt,
  title,
  description,
}) => {
  return (
    <Card className="overflow-hidden">
      <div className="relative w-full h-full">
        {/* Image */}
        <Image
          src={imageSrc}
          alt={imageAlt}
          fill
          className="object-cover object-center rounded-xl"
          sizes="(max-width: 640px) 240px, (max-width: 1024px) 384px, 500px"
          quality={85}
          loading="lazy"
        />
        
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent rounded-xl" />
        
        {/* Content overlay if title or description provided */}
        {(title || description) && (
          <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
            {title && (
              <h3 className="text-lg font-semibold mb-1 drop-shadow-lg">
                {title}
              </h3>
            )}
            {description && (
              <p className="text-sm opacity-90 drop-shadow-lg">
                {description}
              </p>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export default MockupCard;