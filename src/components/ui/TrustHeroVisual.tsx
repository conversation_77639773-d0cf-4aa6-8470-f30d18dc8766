"use client";

import React from "react";
import { motion, useReducedMotion, type Variants } from "framer-motion";
import { CheckCircle, Users, Zap, Heart, Star, ArrowRight } from "lucide-react";
import Image from "next/image";

interface TrustHeroVisualProps {
  className?: string;
  dictionary?: {
    satisfaction?: string;
    appsLaunched?: string;
    fromTo?: string;
    rating?: string;
    response?: string;
    trust?: string;
    additional?: string;
    industries?: {
      doctors?: string;
      crafts?: string;
      startups?: string;
    };
  };
}

export function TrustHeroVisual({ className = "", dictionary }: TrustHeroVisualProps) {
  const shouldReduceMotion = useReducedMotion();

  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  const floatingVariants: Variants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
    float: shouldReduceMotion
      ? {}
      : {
          y: [-10, 10, -10],
          transition: {
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          },
        },
  };

  return (
    <div className={`relative w-full h-full flex items-center justify-center ${className}`}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative w-full max-w-lg mx-auto"
      >
        {/* Central Trust Hub */}
        <motion.div
          variants={itemVariants}
          className="relative bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-gray-200 dark:border-gray-600"
        >
          {/* Success Metrics */}
          <div className="text-center mb-6">
            <motion.div
              variants={itemVariants}
              className="inline-flex items-center gap-2 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-4 py-2 rounded-full text-sm font-semibold mb-4"
            >
              <CheckCircle className="w-4 h-4" />
              {dictionary?.satisfaction || "100% Customer Satisfaction"}
            </motion.div>
            
            <motion.h3
              variants={itemVariants}
              className="text-xl font-bold text-gray-900 dark:text-white mb-2"
            >
              {dictionary?.appsLaunched || "15+ Apps Successfully Launched"}
            </motion.h3>
            
            <motion.p
              variants={itemVariants}
              className="text-gray-600 dark:text-gray-300 text-sm"
            >
              {dictionary?.fromTo || "From Startups to Enterprise Solutions"}
            </motion.p>
          </div>

          {/* Industry Success Stories */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-3 gap-4 mb-6"
          >
            {[
              { icon: Users, label: dictionary?.industries?.startups || "Startups", count: "25+" },
              { icon: Zap, label: dictionary?.industries?.crafts || "Businesses", count: "20+" },
              { icon: Heart, label: dictionary?.industries?.doctors || "Enterprises", count: "15+" },
            ].map((item, index) => (
              <div
                key={index}
                className="text-center p-3 bg-gray-50/80 dark:bg-gray-700/80 rounded-xl backdrop-blur-sm"
              >
                <item.icon className="w-8 h-8 mx-auto mb-2 text-blue-500" />
                <div className="text-lg font-bold text-gray-900 dark:text-white">
                  {item.count}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {item.label}
                </div>
              </div>
            ))}
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            variants={itemVariants}
            className="border-t border-gray-100 dark:border-gray-600 pt-4"
          >
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                <span className="font-semibold text-gray-900 dark:text-white">4.9/5</span>
                <span className="text-gray-600 dark:text-gray-400">{dictionary?.rating || "Rating"}</span>
              </div>
              <div className="text-gray-600 dark:text-gray-400">
                {dictionary?.response || "24h Response"}
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Floating Success Indicators */}
        <motion.div
          variants={floatingVariants}
          animate={["visible", "float"]}
          className="absolute -top-4 -right-4 bg-green-500 text-white p-3 rounded-full shadow-lg"
        >
          <CheckCircle className="w-6 h-6" />
        </motion.div>

        <motion.div
          variants={floatingVariants}
          animate={["visible", "float"]}
          transition={{ delay: 0.2 }}
          className="absolute -bottom-4 -left-4 bg-blue-600 dark:bg-blue-500 text-white px-4 py-2 rounded-full shadow-lg text-sm font-semibold"
        >
          {dictionary?.trust || "No Stress!"}
        </motion.div>

        <motion.div
          variants={floatingVariants}
          animate={["visible", "float"]}
          transition={{ delay: 0.4 }}
          className="absolute top-1/2 -left-8 bg-blue-500 text-white p-2 rounded-full shadow-lg"
        >
          <ArrowRight className="w-4 h-4" />
        </motion.div>

        {/* Customer Avatar Group */}
        <motion.div
          variants={itemVariants}
          className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg border border-gray-200 dark:border-gray-600"
        >
          <div className="flex -space-x-2">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-white text-xs font-bold"
              >
                {String.fromCharCode(64 + i)}
              </div>
            ))}
          </div>
          <span className="text-sm font-semibold text-gray-900 dark:text-white">
            {dictionary?.additional || "+47 more"}
          </span>
        </motion.div>
      </motion.div>
    </div>
  );
}