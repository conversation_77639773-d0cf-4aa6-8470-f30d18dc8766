"use client";

import React from "react";
import { motion, useReducedMotion } from "framer-motion";

// Animated Network Node Component
const NetworkNode = React.memo(({ x, y, delay = 0, size = 4 }: { x: number; y: number; delay?: number; size?: number }) => {
  return (
    <motion.div
      className="absolute rounded-full bg-blue-400/60 dark:bg-blue-300/40"
      style={{
        left: `${x}%`,
        top: `${y}%`,
        width: `${size}px`,
        height: `${size}px`,
      }}
      initial={{ opacity: 0, scale: 0 }}
      animate={{
        opacity: [0.3, 0.8, 0.3],
        scale: [0.8, 1.2, 0.8],
        x: [0, Math.random() * 20 - 10, 0],
        y: [0, Math.random() * 20 - 10, 0],
      }}
      transition={{
        duration: 4 + Math.random() * 2,
        delay,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    />
  );
});

NetworkNode.displayName = "NetworkNode";

// Animated Connection Line Component
const ConnectionLine = React.memo(({ 
  x1, y1, x2, y2, delay = 0 
}: { 
  x1: number; y1: number; x2: number; y2: number; delay?: number; 
}) => {
  const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
  
  return (
    <motion.div
      className="absolute origin-left bg-gradient-to-r from-blue-400/20 to-transparent dark:from-blue-300/15 dark:to-transparent"
      style={{
        left: `${x1}%`,
        top: `${y1}%`,
        width: `${length}px`,
        height: '1px',
        transform: `rotate(${angle}deg)`,
      }}
      initial={{ scaleX: 0, opacity: 0 }}
      animate={{
        scaleX: [0, 1, 0.7, 1],
        opacity: [0, 0.6, 0.3, 0.6],
      }}
      transition={{
        duration: 3 + Math.random(),
        delay,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    />
  );
});

ConnectionLine.displayName = "ConnectionLine";

// Floating Geometric Shape Component
const FloatingShape = React.memo(({ 
  x, y, delay = 0, shape = 'circle' 
}: { 
  x: number; y: number; delay?: number; shape?: 'circle' | 'square' | 'triangle'; 
}) => {
  const shapeClasses = {
    circle: 'rounded-full',
    square: 'rounded-sm',
    triangle: 'rounded-sm transform rotate-45',
  };
  
  return (
    <motion.div
      className={`absolute w-3 h-3 bg-blue-500/30 dark:bg-blue-400/20 ${shapeClasses[shape]}`}
      style={{
        left: `${x}%`,
        top: `${y}%`,
      }}
      initial={{ opacity: 0, rotate: 0 }}
      animate={{
        opacity: [0.2, 0.6, 0.2],
        rotate: [0, 180, 360],
        x: [0, Math.random() * 30 - 15, 0],
        y: [0, Math.random() * 30 - 15, 0],
      }}
      transition={{
        duration: 6 + Math.random() * 2,
        delay,
        repeat: Infinity,
        ease: "linear",
      }}
    />
  );
});

FloatingShape.displayName = "FloatingShape";

// Floating Code Symbols Component
const FloatingCode = React.memo(({ 
  x, y, delay = 0, symbol = '<>' 
}: { 
  x: number; y: number; delay?: number; symbol?: string; 
}) => {
  return (
    <motion.div
      className="absolute text-blue-400/30 dark:text-blue-300/20 font-mono text-sm font-bold select-none"
      style={{
        left: `${x}%`,
        top: `${y}%`,
      }}
      initial={{ opacity: 0, y: 10 }}
      animate={{
        opacity: [0.2, 0.5, 0.2],
        y: [0, -20, 0],
        rotate: [0, 5, -5, 0],
      }}
      transition={{
        duration: 8 + Math.random() * 4,
        delay,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    >
      {symbol}
    </motion.div>
  );
});

FloatingCode.displayName = "FloatingCode";

// Floating Particles Component
const FloatingParticle = React.memo(({ 
  x, y, delay = 0, size = 2 
}: { 
  x: number; y: number; delay?: number; size?: number; 
}) => {
  return (
    <motion.div
      className="absolute rounded-full bg-gradient-to-r from-blue-400/40 to-purple-400/40 dark:from-blue-300/30 dark:to-purple-300/30"
      style={{
        left: `${x}%`,
        top: `${y}%`,
        width: `${size}px`,
        height: `${size}px`,
      }}
      initial={{ opacity: 0, scale: 0 }}
      animate={{
        opacity: [0, 0.6, 0],
        scale: [0, 1.5, 0],
        x: [0, Math.random() * 40 - 20, 0],
        y: [0, -Math.random() * 40 - 20, 0],
      }}
      transition={{
        duration: 12 + Math.random() * 8,
        delay,
        repeat: Infinity,
        ease: "easeOut",
      }}
    />
  );
});

FloatingParticle.displayName = "FloatingParticle";

// Digital Hexagon Component
const DigitalHexagon = React.memo(({ 
  x, y, delay = 0, size = 12 
}: { 
  x: number; y: number; delay?: number; size?: number; 
}) => {
  return (
    <motion.div
      className="absolute border border-blue-400/30 dark:border-blue-300/20"
      style={{
        left: `${x}%`,
        top: `${y}%`,
        width: `${size}px`,
        height: `${size}px`,
        clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
      }}
      initial={{ opacity: 0, scale: 0 }}
      animate={{
        opacity: [0.2, 0.6, 0.2],
        scale: [0.8, 1.2, 0.8],
        rotate: [0, 120, 240, 360],
      }}
      transition={{
        duration: 15 + Math.random() * 5,
        delay,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    />
  );
});

DigitalHexagon.displayName = "DigitalHexagon";

// Binary Code Component
const BinaryCode = React.memo(({ 
  x, y, delay = 0 
}: { 
  x: number; y: number; delay?: number; 
}) => {
  const binaryString = React.useMemo(() => {
    return Array.from({ length: 6 }, () => Math.random() > 0.5 ? '1' : '0').join('');
  }, []);

  return (
    <motion.div
      className="absolute text-blue-400/25 dark:text-blue-300/15 font-mono text-xs font-medium select-none"
      style={{
        left: `${x}%`,
        top: `${y}%`,
      }}
      initial={{ opacity: 0, y: 10 }}
      animate={{
        opacity: [0.1, 0.4, 0.1],
        y: [0, -30, 0],
      }}
      transition={{
        duration: 12 + Math.random() * 6,
        delay,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    >
      {binaryString}
    </motion.div>
  );
});

BinaryCode.displayName = "BinaryCode";

// Professional Background Elements Component with Network Animation
const BackgroundElements = React.memo(() => {
  const shouldReduceMotion = useReducedMotion();

  // Network nodes positions
  const networkNodes = React.useMemo(() => [
    { x: 15, y: 20, size: 4 },
    { x: 25, y: 35, size: 3 },
    { x: 45, y: 25, size: 5 },
    { x: 65, y: 40, size: 4 },
    { x: 75, y: 15, size: 3 },
    { x: 85, y: 30, size: 4 },
    { x: 20, y: 60, size: 3 },
    { x: 40, y: 70, size: 4 },
    { x: 60, y: 65, size: 5 },
    { x: 80, y: 75, size: 3 },
    { x: 10, y: 80, size: 4 },
    { x: 90, y: 85, size: 3 },
  ], []);

  // Connection lines between nearby nodes
  const connections = React.useMemo(() => {
    const lines = [];
    for (let i = 0; i < networkNodes.length; i++) {
      for (let j = i + 1; j < networkNodes.length; j++) {
        const distance = Math.sqrt(
          Math.pow(networkNodes[j].x - networkNodes[i].x, 2) + 
          Math.pow(networkNodes[j].y - networkNodes[i].y, 2)
        );
        // Only connect nodes that are close enough
        if (distance < 40) {
          lines.push({
            x1: networkNodes[i].x,
            y1: networkNodes[i].y,
            x2: networkNodes[j].x,
            y2: networkNodes[j].y,
          });
        }
      }
    }
    return lines;
  }, [networkNodes]);

  // Floating shapes positions
  const floatingShapes = React.useMemo(() => [
    { x: 30, y: 15, shape: 'circle' as const },
    { x: 70, y: 25, shape: 'square' as const },
    { x: 50, y: 45, shape: 'triangle' as const },
    { x: 20, y: 75, shape: 'circle' as const },
    { x: 85, y: 60, shape: 'square' as const },
    { x: 15, y: 45, shape: 'triangle' as const },
    { x: 90, y: 20, shape: 'circle' as const },
  ], []);

  // Floating code symbols
  const codeSymbols = React.useMemo(() => [
    { x: 25, y: 30, symbol: '</>' },
    { x: 75, y: 20, symbol: '{}' },
    { x: 55, y: 60, symbol: '()' },
    { x: 35, y: 80, symbol: '[]' },
    { x: 80, y: 50, symbol: '<>' },
    { x: 10, y: 70, symbol: '/>' },
    { x: 65, y: 35, symbol: '=>' },
    { x: 45, y: 85, symbol: '&&' },
  ], []);

  // Floating particles
  const particles = React.useMemo(() => [
    { x: 12, y: 25, size: 2 },
    { x: 28, y: 55, size: 3 },
    { x: 42, y: 20, size: 2 },
    { x: 58, y: 75, size: 3 },
    { x: 72, y: 40, size: 2 },
    { x: 88, y: 65, size: 3 },
    { x: 18, y: 85, size: 2 },
    { x: 38, y: 10, size: 3 },
    { x: 62, y: 90, size: 2 },
    { x: 82, y: 15, size: 3 },
    { x: 5, y: 50, size: 2 },
    { x: 95, y: 80, size: 3 },
  ], []);

  // Digital hexagons
  const digitalHexagons = React.useMemo(() => [
    { x: 22, y: 40, size: 12 },
    { x: 68, y: 15, size: 10 },
    { x: 48, y: 70, size: 14 },
    { x: 78, y: 80, size: 11 },
    { x: 32, y: 25, size: 13 },
    { x: 85, y: 35, size: 10 },
    { x: 15, y: 60, size: 12 },
    { x: 90, y: 45, size: 11 },
  ], []);

  // Binary code elements
  const binaryElements = React.useMemo(() => [
    { x: 18, y: 35 },
    { x: 52, y: 25 },
    { x: 72, y: 55 },
    { x: 28, y: 75 },
    { x: 62, y: 80 },
    { x: 88, y: 25 },
    { x: 8, y: 65 },
    { x: 42, y: 15 },
    { x: 75, y: 35 },
    { x: 35, y: 90 },
  ], []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Professional gradient background - keep light */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50/80 via-white to-blue-50/20 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 z-0" />

      {/* Subtle geometric pattern overlay */}
      <div className="absolute inset-0 opacity-[0.03] dark:opacity-[0.08]">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(90deg, rgba(15, 23, 42, 0.4) 1px, transparent 1px),
              linear-gradient(0deg, rgba(15, 23, 42, 0.4) 1px, transparent 1px)
            `,
            backgroundSize: "40px 40px",
          }}
        />
      </div>

      {/* Professional accent elements */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-blue-500/5 to-blue-600/5 dark:from-blue-400/10 dark:to-blue-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-blue-500/3 to-blue-600/3 dark:from-blue-400/8 dark:to-blue-500/8 rounded-full blur-3xl" />

      {/* Animated Network Elements - only if motion is not reduced */}
      {!shouldReduceMotion && (
        <>
          {/* Connection Lines */}
          {connections.map((connection, index) => (
            <ConnectionLine
              key={`connection-${index}`}
              x1={connection.x1}
              y1={connection.y1}
              x2={connection.x2}
              y2={connection.y2}
              delay={index * 0.2}
            />
          ))}

          {/* Network Nodes */}
          {networkNodes.map((node, index) => (
            <NetworkNode
              key={`node-${index}`}
              x={node.x}
              y={node.y}
              size={node.size}
              delay={index * 0.1}
            />
          ))}

          {/* Floating Geometric Shapes */}
          {floatingShapes.map((shape, index) => (
            <FloatingShape
              key={`shape-${index}`}
              x={shape.x}
              y={shape.y}
              shape={shape.shape}
              delay={index * 0.3}
            />
          ))}

          {/* Floating Code Symbols */}
          {codeSymbols.map((code, index) => (
            <FloatingCode
              key={`code-${index}`}
              x={code.x}
              y={code.y}
              symbol={code.symbol}
              delay={index * 0.4 + 1}
            />
          ))}

          {/* Floating Particles */}
          {particles.map((particle, index) => (
            <FloatingParticle
              key={`particle-${index}`}
              x={particle.x}
              y={particle.y}
              size={particle.size}
              delay={index * 0.2 + 2}
            />
          ))}

          {/* Digital Hexagons */}
          {digitalHexagons.map((hexagon, index) => (
            <DigitalHexagon
              key={`hexagon-${index}`}
              x={hexagon.x}
              y={hexagon.y}
              size={hexagon.size}
              delay={index * 0.6 + 3}
            />
          ))}

          {/* Binary Code Elements */}
          {binaryElements.map((binary, index) => (
            <BinaryCode
              key={`binary-${index}`}
              x={binary.x}
              y={binary.y}
              delay={index * 0.8 + 4}
            />
          ))}
        </>
      )}
    </div>
  );
});

BackgroundElements.displayName = "BackgroundElements";

interface UnifiedBackgroundProps {
  children: React.ReactNode;
  className?: string;
}

export const UnifiedBackground: React.FC<UnifiedBackgroundProps> = ({ children, className = "" }) => {
  return (
    <div className={`relative ${className}`}>
      <BackgroundElements />
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default UnifiedBackground;