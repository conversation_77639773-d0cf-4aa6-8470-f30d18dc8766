"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Phone, Mail, Share2, ChevronLeft, ChevronRight } from "lucide-react";
import { type Dictionary } from "@/lib/dictionary";
import Image from "next/image";

// Contact details from Flutter code (copied from ContactSection)
const contactInfo = {
  email: "<EMAIL>",
  phone: "+49 175 9918357",
  location: "Wyoming",
  calendlyLink:
    "https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01",
  whatsapp: "+49 175 9918357",
  linkedin: "https://www.linkedin.com/in/viktor-hermann-103125245/",
  github: "https://github.com/<PERSON>-Hermann/",
};

interface FloatingContactProps {
  dictionary?: Dictionary["contact"];
}

export const FloatingContact = ({ dictionary }: FloatingContactProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHeroVisible, setIsHeroVisible] = useState(false);
  const [isSolutionsVisible, setIsSolutionsVisible] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Setup intersection observers for hero and solutions sections
  useEffect(() => {
    // Hero section observer (to collapse)
    const heroObserver = new IntersectionObserver(
      ([entry]) => {
        setIsHeroVisible(entry.isIntersecting);
        if (entry.isIntersecting) {
          setIsExpanded(false);
        }
      },
      { threshold: 0.3 }
    );

    // Solutions section observer (to expand)
    const solutionsObserver = new IntersectionObserver(
      ([entry]) => {
        setIsSolutionsVisible(entry.isIntersecting);
        // Only expand automatically on non-mobile screens
        if (
          window.innerWidth >= 768 &&
          entry.isIntersecting &&
          !isHeroVisible
        ) {
          setIsExpanded(true);
        }
      },
      { threshold: 0.3 }
    );

    // Observe sections
    const heroSection = document.getElementById("hero");
    const solutionsSection = document.getElementById("solutions-portfolio");

    if (heroSection) {
      heroObserver.observe(heroSection);
    }

    if (solutionsSection) {
      solutionsObserver.observe(solutionsSection);
    }

    return () => {
      if (heroSection) heroObserver.unobserve(heroSection);
      if (solutionsSection) solutionsObserver.unobserve(solutionsSection);
    };
  }, [isHeroVisible]);

  const contactLinks = [
    {
      icon: <Phone className="w-5 h-5" />,
      label: dictionary?.phone ?? "Phone",
      href: `tel:${contactInfo.phone}`,
      bgColor: "bg-blue-500 hover:bg-blue-600",
    },
    {
      icon: <Mail className="w-5 h-5" />,
      label: dictionary?.email ?? "Email",
      href: `mailto:${contactInfo.email}`,
      bgColor: "bg-red-500 hover:bg-red-600",
    },
    {
      icon: (
        <div className="flex items-center justify-center rounded-full">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="#FFFFFFFF" /* WhatsApp brand color for better visibility */
            className="w-7 h-7" /* Slightly smaller to fit in the white background */
            stroke="#FFFFFFFF"
            strokeWidth="0.3"
          >
            <path d="M17.6 6.31999C16.8669 5.58141 15.9943 4.99596 15.033 4.59767C14.0716 4.19938 13.0406 3.99602 12 3.99999C10.6089 4.00138 9.24248 4.36819 8.03771 5.06377C6.83294 5.75935 5.83208 6.75926 5.13534 7.96335C4.4386 9.16745 4.07046 10.5335 4.06776 11.9246C4.06507 13.3158 4.42793 14.6832 5.12 15.89L4 20L8.2 18.9C9.35975 19.5452 10.6629 19.8891 11.99 19.9C14.0997 19.9001 16.124 19.0668 17.6222 17.5816C19.1205 16.0965 19.9715 14.0796 19.99 11.97C19.983 10.9173 19.7682 9.87634 19.3581 8.9068C18.948 7.93725 18.3514 7.05819 17.6 6.31999ZM12 18.53C10.8177 18.5308 9.65701 18.213 8.64 17.61L8.4 17.46L5.91 18.12L6.57 15.69L6.41 15.44C5.55925 14.0667 5.24174 12.429 5.51762 10.8372C5.7935 9.24545 6.64361 7.81015 7.9069 6.80322C9.1702 5.79628 10.7589 5.28765 12.3721 5.37368C13.9853 5.4597 15.512 6.13441 16.66 7.26999C17.916 8.49818 18.635 10.1735 18.66 11.93C18.6442 13.6859 17.9355 15.3645 16.6882 16.6006C15.441 17.8366 13.756 18.5301 12 18.53ZM15.61 13.59C15.41 13.49 14.44 13.01 14.26 12.95C14.08 12.89 13.94 12.85 13.81 13.05C13.6144 13.3181 13.404 13.5751 13.18 13.82C13.07 13.96 12.95 13.97 12.75 13.82C11.6097 13.3095 10.6597 12.4329 10.06 11.32C9.85 10.95 10.1 10.77 10.34 10.58C10.6903 10.2381 10.9087 9.78978 10.96 9.30999C10.98 9.12999 10.94 8.98999 10.87 8.87999C10.8 8.76999 10.13 7.80999 9.97 7.41999C9.81 7.02999 9.65 7.07999 9.5 7.07999H9.12C8.93326 7.08651 8.75391 7.15532 8.61 7.27499C8.43562 7.4297 8.29412 7.61956 8.19322 7.83194C8.09233 8.04433 8.03406 8.2747 8.02 8.50999C8.02 9.35999 8.59 10.18 8.69 10.31C9.47735 11.7309 10.7199 12.8484 12.2 13.5C12.9185 13.7901 13.5467 14.2458 14.05 14.83C14.12 14.93 14.18 14.97 14.25 15C14.3 15 14.5 15 14.78 14.82C14.8584 14.766 14.9333 14.7032 15.0037 14.6328C15.0741 14.5624 15.139 14.4848 15.197 14.401C15.3354 14.1935 15.4087 13.9478 15.4087 13.696C15.4087 13.4441 15.3354 13.1985 15.197 12.991C15.1367 12.9323 15.0699 12.8807 14.9978 12.8375C14.9257 12.7943 14.849 12.76 14.77 12.735C14.38 12.635 15.61 13.59 15.61 13.59Z" />
          </svg>
        </div>
      ),
      label: dictionary?.whatsapp ?? "WhatsApp",
      href: `https://wa.me/${contactInfo.whatsapp.replace(/\D/g, "")}`,
      bgColor: "bg-green-500 hover:bg-green-600",
    },
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-5 h-5"
        >
          <path d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.68 1.68 0 0 0-1.68 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z" />
        </svg>
      ),
      label: dictionary?.linkedin ?? "LinkedIn",
      href: contactInfo.linkedin,
      bgColor: "bg-blue-700 hover:bg-blue-800",
    },
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="currentColor"
          className="w-5 h-5"
        >
          <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.237 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
        </svg>
      ),
      label: dictionary?.github ?? "GitHub",
      href: contactInfo.github,
      bgColor:
        "bg-gray-800 hover:bg-gray-900 dark:bg-gray-700 dark:hover:bg-gray-800",
    },
  ];

  const buttonHeight = "h-14"; // approx 3.5rem
  const bottomSpacing = "bottom-5"; // 1.25rem
  const gapBetween = "gap-3"; // 0.75rem
  // Calculate bottom offset for links container: bottomSpacing + buttonHeight + gapBetween
  const linksContainerBottomOffset = "bottom-[5.5rem]"; // 1.25 + 3.5 + 0.75 = 5.5rem

  return (
    // Main container - vertical layout on right side
    <div
      className={`fixed bottom-5 right-5 z-50 flex flex-col items-center gap-3`}
    >
      {/* Vertical stack of contact links */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            className="flex flex-col items-center gap-4 mb-3"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            {contactLinks.map((link, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{
                  duration: 0.2,
                  delay: index * 0.05,
                  type: "spring",
                  stiffness: 260,
                  damping: 20,
                }}
              >
                <Link
                  href={link.href}
                  target={link.href.startsWith("http") ? "_blank" : undefined}
                  rel={
                    link.href.startsWith("http")
                      ? "noopener noreferrer"
                      : undefined
                  }
                  className="relative block w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 group"
                  title={link.label}
                >
                  <div
                    className={cn(
                      "absolute inset-0 rounded-full flex items-center justify-center text-white transition-all duration-300",
                      link.bgColor
                    )}
                  >
                    {link.icon}
                  </div>
                  {/* Tooltip */}
                  <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 px-2 py-1 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 text-xs rounded shadow-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap">
                    {link.label}
                    <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-2 h-2 bg-white dark:bg-gray-800 transform rotate-45"></div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Toggle Button */}
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        onClick={toggleExpanded}
        className={`w-14 h-14 rounded-full bg-gray-800/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg flex items-center justify-center overflow-hidden z-50`}
        aria-label={
          isExpanded ? "Hide contact options" : "Show contact options"
        }
      >
        {/* Status indicator dot */}
        <motion.span
          className={cn(
            "absolute top-1 right-1 w-3 h-3 rounded-full",
            isExpanded ? "bg-green-500" : "bg-blue-500"
          )}
          animate={{
            scale: isExpanded ? [1, 1.2, 1] : 1,
          }}
          transition={{
            repeat: isExpanded ? Infinity : 0,
            duration: 2,
          }}
        />
        {/* Company logo icon */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-7 h-7 text-white"
        >
          <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2v5Z" />
          <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1" />
        </svg>
      </motion.button>
    </div>
  );
};
