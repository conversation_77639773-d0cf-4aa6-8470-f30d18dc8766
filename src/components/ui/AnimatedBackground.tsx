"use client";

import React from "react";
import { motion, useReducedMotion } from "framer-motion";
import { useTheme } from "next-themes";

interface AnimatedBackgroundProps {
  variant?: "primary" | "secondary" | "tertiary" | "quaternary" | "hero" | "light";
  className?: string;
  children?: React.ReactNode;
}

export const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  variant = "primary",
  className = "",
  children,
}) => {
  const shouldReduceMotion = useReducedMotion();
  const { theme, resolvedTheme } = useTheme();
  // Use resolvedTheme as fallback and handle SSR properly
  const isDarkMode = resolvedTheme === "dark" || theme === "dark";

  // Define gradient patterns for both light and dark modes
  const gradientStyles = {
    hero: {
      light: {
        background: `
          linear-gradient(135deg, 
            rgba(37, 99, 235, 0.05) 0%, 
            rgba(59, 130, 246, 0.08) 25%, 
            rgba(147, 197, 253, 0.04) 50%, 
            rgba(37, 99, 235, 0.06) 75%, 
            rgba(29, 78, 216, 0.05) 100%
          ),
          radial-gradient(circle at 20% 20%, 
            rgba(37, 99, 235, 0.1) 0%, 
            transparent 50%
          ),
          radial-gradient(circle at 80% 80%, 
            rgba(59, 130, 246, 0.08) 0%, 
            transparent 50%
          )
        `,
        backgroundSize: "400% 400%, 600px 600px, 800px 800px",
      },
      dark: {
        background: `
          linear-gradient(135deg, 
            rgba(37, 99, 235, 0.12) 0%, 
            rgba(59, 130, 246, 0.15) 25%, 
            rgba(147, 197, 253, 0.08) 50%, 
            rgba(37, 99, 235, 0.12) 75%, 
            rgba(29, 78, 216, 0.1) 100%
          ),
          radial-gradient(circle at 20% 20%, 
            rgba(37, 99, 235, 0.2) 0%, 
            transparent 50%
          ),
          radial-gradient(circle at 80% 80%, 
            rgba(59, 130, 246, 0.15) 0%, 
            transparent 50%
          )
        `,
        backgroundSize: "400% 400%, 600px 600px, 800px 800px",
      },
    },
    primary: {
      light: {
        background: `
          linear-gradient(45deg, 
            rgba(37, 99, 235, 0.04) 0%, 
            rgba(59, 130, 246, 0.06) 25%, 
            rgba(147, 197, 253, 0.03) 50%, 
            rgba(37, 99, 235, 0.05) 75%, 
            rgba(29, 78, 216, 0.04) 100%
          ),
          radial-gradient(ellipse at 30% 70%, 
            rgba(59, 130, 246, 0.08) 0%, 
            transparent 60%
          )
        `,
        backgroundSize: "600% 600%, 1000px 1000px",
      },
      dark: {
        background: `
          linear-gradient(45deg, 
            rgba(37, 99, 235, 0.1) 0%, 
            rgba(59, 130, 246, 0.12) 25%, 
            rgba(147, 197, 253, 0.06) 50%, 
            rgba(37, 99, 235, 0.1) 75%, 
            rgba(29, 78, 216, 0.08) 100%
          ),
          radial-gradient(ellipse at 30% 70%, 
            rgba(59, 130, 246, 0.15) 0%, 
            transparent 60%
          )
        `,
        backgroundSize: "600% 600%, 1000px 1000px",
      },
    },
    secondary: {
      light: {
        background: `
          linear-gradient(-45deg, 
            rgba(71, 85, 105, 0.04) 0%, 
            rgba(100, 116, 139, 0.06) 25%, 
            rgba(148, 163, 184, 0.03) 50%, 
            rgba(71, 85, 105, 0.05) 75%, 
            rgba(51, 65, 85, 0.04) 100%
          ),
          radial-gradient(circle at 70% 30%, 
            rgba(100, 116, 139, 0.06) 0%, 
            transparent 50%
          )
        `,
        backgroundSize: "500% 500%, 900px 900px",
      },
      dark: {
        background: `
          linear-gradient(-45deg, 
            rgba(71, 85, 105, 0.08) 0%, 
            rgba(100, 116, 139, 0.12) 25%, 
            rgba(148, 163, 184, 0.06) 50%, 
            rgba(71, 85, 105, 0.1) 75%, 
            rgba(51, 65, 85, 0.08) 100%
          ),
          radial-gradient(circle at 70% 30%, 
            rgba(100, 116, 139, 0.12) 0%, 
            transparent 50%
          )
        `,
        backgroundSize: "500% 500%, 900px 900px",
      },
    },
    tertiary: {
      light: {
        background: `
          linear-gradient(90deg, 
            rgba(37, 99, 235, 0.03) 0%, 
            rgba(147, 197, 253, 0.05) 30%, 
            rgba(59, 130, 246, 0.04) 60%, 
            rgba(37, 99, 235, 0.035) 100%
          ),
          radial-gradient(ellipse at 50% 50%, 
            rgba(147, 197, 253, 0.06) 0%, 
            transparent 70%
          )
        `,
        backgroundSize: "800% 800%, 1200px 1200px",
      },
      dark: {
        background: `
          linear-gradient(90deg, 
            rgba(37, 99, 235, 0.08) 0%, 
            rgba(147, 197, 253, 0.12) 30%, 
            rgba(59, 130, 246, 0.1) 60%, 
            rgba(37, 99, 235, 0.08) 100%
          ),
          radial-gradient(ellipse at 50% 50%, 
            rgba(147, 197, 253, 0.12) 0%, 
            transparent 70%
          )
        `,
        backgroundSize: "800% 800%, 1200px 1200px",
      },
    },
    quaternary: {
      light: {
        background: `
          linear-gradient(180deg, 
            rgba(59, 130, 246, 0.04) 0%, 
            rgba(37, 99, 235, 0.05) 40%, 
            rgba(147, 197, 253, 0.03) 70%, 
            rgba(29, 78, 216, 0.04) 100%
          ),
          radial-gradient(circle at 20% 80%, 
            rgba(37, 99, 235, 0.07) 0%, 
            transparent 60%
          )
        `,
        backgroundSize: "700% 700%, 800px 800px",
      },
      dark: {
        background: `
          linear-gradient(180deg, 
            rgba(59, 130, 246, 0.1) 0%, 
            rgba(37, 99, 235, 0.12) 40%, 
            rgba(147, 197, 253, 0.06) 70%, 
            rgba(29, 78, 216, 0.1) 100%
          ),
          radial-gradient(circle at 20% 80%, 
            rgba(37, 99, 235, 0.15) 0%, 
            transparent 60%
          )
        `,
        backgroundSize: "700% 700%, 800px 800px",
      },
    },
    light: {
      light: {
        background: `
          linear-gradient(135deg, 
            rgba(248, 250, 252, 0.8) 0%, 
            rgba(241, 245, 249, 0.9) 25%, 
            rgba(255, 255, 255, 0.95) 50%, 
            rgba(248, 250, 252, 0.85) 75%, 
            rgba(241, 245, 249, 0.8) 100%
          ),
          radial-gradient(circle at 40% 60%, 
            rgba(37, 99, 235, 0.04) 0%, 
            transparent 50%
          )
        `,
        backgroundSize: "400% 400%, 600px 600px",
      },
      dark: {
        background: `
          linear-gradient(135deg, 
            rgba(15, 23, 42, 0.3) 0%, 
            rgba(30, 41, 59, 0.4) 25%, 
            rgba(51, 65, 85, 0.5) 50%, 
            rgba(30, 41, 59, 0.35) 75%, 
            rgba(15, 23, 42, 0.3) 100%
          ),
          radial-gradient(circle at 40% 60%, 
            rgba(37, 99, 235, 0.08) 0%, 
            transparent 50%
          )
        `,
        backgroundSize: "400% 400%, 600px 600px",
      },
    },
  };

  const currentStyle = isDarkMode ? gradientStyles[variant].dark : gradientStyles[variant].light;

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Base animated gradient layer */}
      <motion.div
        className="absolute inset-0 opacity-100"
        style={currentStyle}
        animate={
          shouldReduceMotion
            ? {}
            : {
                backgroundPosition: [
                  "0% 0%, 0% 0%",
                  "100% 100%, 50% 50%",
                  "0% 0%, 100% 100%",
                ],
              }
        }
        transition={{
          duration: variant === "hero" ? 25 : 30,
          repeat: Infinity,
          ease: "linear",
        }}
      />

      {/* Additional floating gradient orbs for extra depth - theme aware */}
      <motion.div
        className="absolute inset-0 opacity-60"
        animate={
          shouldReduceMotion
            ? {}
            : {
                background: isDarkMode ? [
                  `radial-gradient(circle at 10% 20%, rgba(37, 99, 235, 0.15) 0%, transparent 50%),
                   radial-gradient(circle at 90% 80%, rgba(59, 130, 246, 0.12) 0%, transparent 50%)`,
                  `radial-gradient(circle at 80% 30%, rgba(37, 99, 235, 0.15) 0%, transparent 50%),
                   radial-gradient(circle at 20% 70%, rgba(59, 130, 246, 0.12) 0%, transparent 50%)`,
                  `radial-gradient(circle at 50% 90%, rgba(37, 99, 235, 0.15) 0%, transparent 50%),
                   radial-gradient(circle at 50% 10%, rgba(59, 130, 246, 0.12) 0%, transparent 50%)`,
                  `radial-gradient(circle at 10% 20%, rgba(37, 99, 235, 0.15) 0%, transparent 50%),
                   radial-gradient(circle at 90% 80%, rgba(59, 130, 246, 0.12) 0%, transparent 50%)`,
                ] : [
                  `radial-gradient(circle at 10% 20%, rgba(37, 99, 235, 0.08) 0%, transparent 50%),
                   radial-gradient(circle at 90% 80%, rgba(59, 130, 246, 0.06) 0%, transparent 50%)`,
                  `radial-gradient(circle at 80% 30%, rgba(37, 99, 235, 0.08) 0%, transparent 50%),
                   radial-gradient(circle at 20% 70%, rgba(59, 130, 246, 0.06) 0%, transparent 50%)`,
                  `radial-gradient(circle at 50% 90%, rgba(37, 99, 235, 0.08) 0%, transparent 50%),
                   radial-gradient(circle at 50% 10%, rgba(59, 130, 246, 0.06) 0%, transparent 50%)`,
                  `radial-gradient(circle at 10% 20%, rgba(37, 99, 235, 0.08) 0%, transparent 50%),
                   radial-gradient(circle at 90% 80%, rgba(59, 130, 246, 0.06) 0%, transparent 50%)`,
                ],
              }
        }
        transition={{
          duration: 35,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Fallback CSS-based dark mode gradients */}
      <div className="absolute inset-0 opacity-0 dark:opacity-100 transition-opacity duration-300">
        <div 
          className={`absolute inset-0 ${
            variant === 'hero' 
              ? 'bg-gradient-to-br from-blue-600/10 via-blue-500/15 to-indigo-600/8'
              : variant === 'primary'
              ? 'bg-gradient-to-r from-blue-600/8 via-blue-500/12 to-blue-700/6'
              : variant === 'secondary' 
              ? 'bg-gradient-to-l from-slate-600/8 via-slate-500/12 to-slate-700/6'
              : variant === 'tertiary'
              ? 'bg-gradient-to-t from-blue-600/6 via-blue-400/10 to-blue-600/8'
              : variant === 'quaternary'
              ? 'bg-gradient-to-b from-blue-500/8 via-blue-600/10 to-blue-700/6'
              : 'bg-gradient-to-br from-slate-800/20 via-slate-700/30 to-slate-900/20'
          }`}
        />
      </div>

      {/* Theme enhancement overlay */}
      <div className={`absolute inset-0 ${
        isDarkMode 
          ? "bg-gradient-to-br from-slate-900/10 via-transparent to-slate-800/10" 
          : "bg-gradient-to-br from-transparent via-transparent to-slate-950/[0.02]"
      }`} />

      {/* Content layer */}
      <div className="relative z-10">{children}</div>
    </div>
  );
};

export default AnimatedBackground;