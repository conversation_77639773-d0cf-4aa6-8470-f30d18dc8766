"use client";

import React from "react";
import { motion, useReducedMotion } from "framer-motion";
import {
  Code,
  Smartphone,
  Zap,
  Layers,
  Database,
  Cloud,
  Settings,
  Users,
  TrendingUp,
  Shield,
  Palette,
  Cpu,
  Globe,
  Lock,
} from "lucide-react";
import Image from "next/image";

interface AnimatedHeroPhoneProps {
  className?: string;
  dualPhones?: boolean;
}

const AnimatedHeroPhone: React.FC<AnimatedHeroPhoneProps> = ({
  className = "",
  dualPhones = true,
}) => {
  const shouldReduceMotion = useReducedMotion();
  const [currentScreen, setCurrentScreen] = React.useState(0);
  const [currentScreenSecond, setCurrentScreenSecond] = React.useState(1);

  // Auto-cycle through screens
  React.useEffect(() => {
    if (!shouldReduceMotion) {
      const interval = setInterval(() => {
        setCurrentScreen((prev) => (prev + 1) % 3);
        setCurrentScreenSecond((prev) => (prev + 1) % 3);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [shouldReduceMotion]);

  // Flutter-focused technology stack
  const flutterTechnologies = [
    {
      name: "Flutter",
      icon: "/icons/flutter.svg",
      color: "from-blue-400 to-blue-600",
    },
    { name: "Dart", icon: Code, color: "from-indigo-400 to-indigo-600" },
    {
      name: "Firebase",
      icon: Database,
      color: "from-orange-400 to-orange-600",
    },
    {
      name: "Supabase",
      icon: "/icons/supabase.svg",
      color: "from-green-400 to-green-600",
    },
    {
      name: "Material Design",
      icon: Palette,
      color: "from-purple-400 to-purple-600",
    },
    { name: "Native Performance", icon: Cpu, color: "from-red-400 to-red-600" },
    { name: "Cross Platform", icon: Globe, color: "from-teal-400 to-teal-600" },
    { name: "Secure", icon: Lock, color: "from-gray-400 to-gray-600" },
  ];

  // Helper component for rendering individual phone
  const PhoneComponent = ({
    screenState,
    delay = 0,
    rotateDirection = 1,
    xOffset = 0,
  }: {
    screenState: number;
    delay?: number;
    rotateDirection?: number;
    xOffset?: number;
  }) => (
    <motion.div
      className="relative w-64 lg:w-72 h-[520px] lg:h-[580px]"
      initial={{ opacity: 0, rotateY: -25 * rotateDirection, x: 50 + xOffset }}
      animate={{
        opacity: 1,
        rotateY: shouldReduceMotion
          ? 0
          : [-6 * rotateDirection, 6 * rotateDirection, -6 * rotateDirection],
        x: xOffset,
      }}
      transition={{
        duration: shouldReduceMotion ? 0.6 : 1.2,
        delay: delay,
        rotateY: {
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: delay,
        },
      }}
      style={{ perspective: "1000px" }}
      whileHover={
        shouldReduceMotion
          ? {}
          : {
              scale: 1.05,
              rotateY: 0,
              transition: { duration: 0.3 },
            }
      }
    >
      {/* Enhanced iPhone Body with more realistic design */}
      <motion.div
        className="relative w-full h-full bg-gradient-to-br from-gray-900 via-black to-gray-800 rounded-[2.8rem] p-2 shadow-2xl"
        style={{
          transformStyle: "preserve-3d",
          boxShadow:
            "0 25px 50px -12px rgba(0, 0, 0, 0.8), inset 0 1px 0 rgba(255, 255, 255, 0.1)",
        }}
      >
        {/* Realistic metallic frame */}
        <div className="absolute inset-0 rounded-[2.8rem] bg-gradient-to-r from-gray-400 via-gray-200 to-gray-400 p-[2px]">
          <div className="w-full h-full bg-gradient-to-br from-gray-900 via-black to-gray-800 rounded-[2.6rem]"></div>
        </div>

        {/* Screen with improved realism */}
        <div className="relative w-full h-full bg-black rounded-[2.4rem] overflow-hidden z-10 border border-gray-700">
          {/* Enhanced Dynamic Island */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-28 h-7 bg-black rounded-b-3xl z-30 shadow-2xl border-x border-b border-gray-800">
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-10 h-1.5 bg-gray-800 rounded-full"></div>
            <div className="absolute top-2 right-3 w-1.5 h-1.5 bg-gray-800 rounded-full"></div>
          </div>

          {/* Improved Status Bar */}
          <div className="h-10 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-between px-6 text-sm font-semibold text-black dark:text-white pt-3 relative z-20">
            <span className="font-bold">12:35</span>
            <div className="flex items-center gap-1">
              <div className="w-6 h-3 border border-black dark:border-white rounded-sm">
                <div className="w-4 h-2 bg-green-500 rounded-sm m-0.5"></div>
              </div>
            </div>
          </div>

          {/* Screen Content Container */}
          <div
            className="relative w-full bg-white dark:bg-gray-900 overflow-hidden"
            style={{ height: "calc(100% - 2.5rem)" }}
          >
            {renderScreenContent(screenState)}
          </div>
        </div>

        {/* Enhanced glow effect */}
        <motion.div
          className="absolute inset-0 rounded-[2.8rem] opacity-0"
          style={{
            background:
              "linear-gradient(45deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2))",
            filter: "blur(25px)",
          }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />
      </motion.div>

      {/* Enhanced shadow */}
      <div className="absolute inset-0 bg-black/30 dark:bg-black/50 rounded-[2.8rem] blur-3xl -z-10 scale-110 translate-y-6"></div>
    </motion.div>
  );

  // Function to render screen content based on state
  const renderScreenContent = (screenState: number) => {
    switch (screenState) {
      case 0:
        return (
          <motion.div
            className="absolute inset-0 w-full h-full bg-gradient-to-br from-blue-900 to-indigo-900 text-white p-4"
            initial={{ opacity: 1, x: 0 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          >
            {/* Flutter Code Editor */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 relative">
                  <Image
                    src="/icons/flutter.svg"
                    alt="Flutter"
                    fill
                    className="object-contain"
                  />
                </div>
                <span className="text-sm font-medium">main.dart</span>
              </div>
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              </div>
            </div>

            {/* Animated Flutter Code */}
            <div className="space-y-1.5 text-xs font-mono">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="text-purple-300"
              >
                import 'package:flutter/material.dart';
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="text-blue-300"
              >
                class MyApp extends StatelessWidget &#123;
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
                className="text-yellow-300 ml-2"
              >
                @override
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
                className="text-green-300 ml-2"
              >
                Widget build(BuildContext context) &#123;
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.0 }}
                className="text-orange-300 ml-4"
              >
                return MaterialApp(
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.2 }}
                className="text-pink-300 ml-6"
              >
                home: Scaffold(
              </motion.div>
            </div>

            {/* Animated Cursor */}
            <motion.div
              className="w-0.5 h-3 bg-white mt-2"
              animate={{ opacity: [1, 0, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
            />
          </motion.div>
        );

      case 1:
        return (
          <motion.div
            className="absolute inset-0 w-full h-full bg-white dark:bg-gray-900 p-4"
            initial={{ opacity: 1, x: 0 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          >
            {/* Flutter App Analytics */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                Flutter Analytics
              </h3>
              <TrendingUp className="w-5 h-5 text-green-500" />
            </div>

            {/* Flutter Performance Stats */}
            <div className="grid grid-cols-2 gap-2 mb-4">
              <motion.div
                className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg"
                whileHover={{ scale: 1.05 }}
              >
                <div className="flex items-center space-x-1 mb-1">
                  <Zap className="w-3 h-3 text-blue-600" />
                  <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                    FPS
                  </span>
                </div>
                <div className="text-lg font-bold text-gray-900 dark:text-white">
                  60
                </div>
                <div className="text-xs text-green-600">Smooth</div>
              </motion.div>

              <motion.div
                className="bg-green-50 dark:bg-green-900/20 p-2 rounded-lg"
                whileHover={{ scale: 1.05 }}
              >
                <div className="flex items-center space-x-1 mb-1">
                  <Users className="w-3 h-3 text-green-600" />
                  <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                    Users
                  </span>
                </div>
                <div className="text-lg font-bold text-gray-900 dark:text-white">
                  25K
                </div>
                <div className="text-xs text-green-600">+15%</div>
              </motion.div>
            </div>

            {/* Flutter Technologies */}
            <div className="space-y-2">
              {flutterTechnologies.slice(0, 4).map((tech, i) => (
                <motion.div
                  key={tech.name}
                  className="flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  initial={{ x: -30, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: i * 0.1 }}
                >
                  {typeof tech.icon === "string" ? (
                    <div className="w-4 h-4 relative">
                      <Image
                        src={tech.icon}
                        alt={tech.name}
                        fill
                        className="object-contain"
                      />
                    </div>
                  ) : (
                    <tech.icon className="w-4 h-4 text-blue-600" />
                  )}
                  <span className="text-xs font-medium text-gray-900 dark:text-white">
                    {tech.name}
                  </span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            className="absolute inset-0 w-full h-full bg-white dark:bg-gray-900 p-4"
            initial={{ opacity: 1, x: 0 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          >
            {/* Flutter Architecture */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                Flutter Stack
              </h3>
              <Layers className="w-5 h-5 text-purple-500" />
            </div>

            {/* Architecture Components */}
            <div className="space-y-2">
              <motion.div
                className="flex items-center space-x-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
                initial={{ x: -40, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <div className="w-4 h-4 relative">
                  <Image
                    src="/icons/flutter.svg"
                    alt="Flutter"
                    fill
                    className="object-contain"
                  />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    Flutter UI
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Cross-platform widgets
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="flex items-center space-x-2 p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg"
                initial={{ x: -40, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                <Database className="w-4 h-4 text-orange-600" />
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    Firebase
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Backend & Auth
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="flex items-center space-x-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg"
                initial={{ x: -40, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                <div className="w-4 h-4 relative">
                  <Image
                    src="/icons/supabase.svg"
                    alt="Supabase"
                    fill
                    className="object-contain"
                  />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    Supabase
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Real-time database
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="flex items-center space-x-2 p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg"
                initial={{ x: -40, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                <Shield className="w-4 h-4 text-purple-600" />
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    Security
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    End-to-end encryption
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={`relative w-full h-full flex items-center justify-center ${className}`}
    >
      {dualPhones ? (
        // Dual Phone Layout
        <div className="flex items-center justify-center space-x-8 lg:space-x-12">
          {/* First Phone */}
          <PhoneComponent
            screenState={currentScreen}
            delay={0}
            rotateDirection={1}
            xOffset={-20}
          />

          {/* Second Phone */}
          <PhoneComponent
            screenState={currentScreenSecond}
            delay={0.3}
            rotateDirection={-1}
            xOffset={20}
          />
        </div>
      ) : (
        // Single Phone Layout (fallback)
        <PhoneComponent
          screenState={currentScreen}
          delay={0}
          rotateDirection={1}
          xOffset={0}
        />
      )}

      {/* Enhanced Floating Elements */}
      {!shouldReduceMotion && (
        <>
          {/* Flutter-themed floating elements */}
          <motion.div
            className="absolute top-1/4 left-1/6 w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center"
            animate={{
              y: [0, -25, 0],
              opacity: [0.3, 0.8, 0.3],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <div className="w-5 h-5 relative">
              <Image
                src="/icons/flutter.svg"
                alt="Flutter"
                fill
                className="object-contain"
              />
            </div>
          </motion.div>

          <motion.div
            className="absolute bottom-1/4 right-1/6 w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center"
            animate={{
              y: [0, 20, 0],
              opacity: [0.2, 0.7, 0.2],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1,
            }}
          >
            <div className="w-4 h-4 relative">
              <Image
                src="/icons/supabase.svg"
                alt="Supabase"
                fill
                className="object-contain"
              />
            </div>
          </motion.div>

          <motion.div
            className="absolute top-1/3 right-1/4 w-6 h-6 bg-purple-500/20 rounded-full"
            animate={{
              y: [0, -15, 0],
              x: [0, 10, 0],
              opacity: [0.4, 0.9, 0.4],
            }}
            transition={{
              duration: 3.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          />

          <motion.div
            className="absolute bottom-1/3 left-1/4 w-7 h-7 bg-orange-500/20 rounded-full"
            animate={{
              y: [0, 18, 0],
              x: [0, -8, 0],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 4.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.5,
            }}
          />
        </>
      )}
    </div>
  );
};

AnimatedHeroPhone.displayName = "AnimatedHeroPhone";

export default AnimatedHeroPhone;