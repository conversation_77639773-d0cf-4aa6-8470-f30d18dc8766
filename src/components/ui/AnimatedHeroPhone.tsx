"use client";

import React from "react";
import { motion, useReducedMotion } from "framer-motion";
import { Users, TrendingUp, Zap } from "lucide-react";
import Image from "next/image";

interface AnimatedHeroPhoneProps {
  className?: string;
  dualPhones?: boolean;
}

const AnimatedHeroPhone: React.FC<AnimatedHeroPhoneProps> = ({
  className = "",
  dualPhones = false, // Single phone by default
}) => {
  const shouldReduceMotion = useReducedMotion();

  // Function to render modern, user-friendly app content
  const renderModernAppContent = () => {
    return (
      <div className="relative w-full h-full p-6">
        {/* Modern App Dashboard */}
        <motion.div
          className="w-full h-full"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
        >
          {/* Header with greeting */}
          <motion.div
            className="mb-6"
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-1">
              Guten <PERSON>!
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              Hier ist Ihr Dashboard
            </p>
          </motion.div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <motion.div
              className="bg-gradient-to-br from-blue-500 to-blue-600 p-4 rounded-2xl text-white shadow-lg"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center justify-between mb-2">
                <Users className="w-6 h-6" />
                <span className="text-xs bg-white/20 px-2 py-1 rounded-full">
                  +12%
                </span>
              </div>
              <div className="text-2xl font-bold">2.4K</div>
              <div className="text-xs opacity-90">Aktive Nutzer</div>
            </motion.div>

            <motion.div
              className="bg-gradient-to-br from-green-500 to-green-600 p-4 rounded-2xl text-white shadow-lg"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center justify-between mb-2">
                <TrendingUp className="w-6 h-6" />
                <span className="text-xs bg-white/20 px-2 py-1 rounded-full">
                  +8%
                </span>
              </div>
              <div className="text-2xl font-bold">98%</div>
              <div className="text-xs opacity-90">Zufriedenheit</div>
            </motion.div>
          </div>

          {/* Activity Chart */}
          <motion.div
            className="bg-white dark:bg-gray-800 p-4 rounded-2xl shadow-lg mb-6"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
              Aktivität
            </h3>
            <div className="flex items-end justify-between h-24 space-x-2">
              {[65, 45, 80, 60, 90, 75, 85].map((height, i) => (
                <motion.div
                  key={i}
                  className="bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-lg flex-1"
                  initial={{ height: 0 }}
                  animate={{ height: `${height}%` }}
                  transition={{
                    delay: 0.8 + i * 0.1,
                    duration: 0.6,
                    ease: "easeOut",
                  }}
                />
              ))}
            </div>
          </motion.div>

          {/* Floating Action Elements */}
          <div className="absolute bottom-6 right-6">
            <motion.div
              className="w-14 h-14 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg"
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              <Zap className="w-6 h-6 text-white" />
            </motion.div>
          </div>

          {/* Floating notification */}
          <motion.div
            className="absolute top-20 right-4 bg-white dark:bg-gray-800 p-3 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700"
            animate={{
              y: [0, -5, 0],
              opacity: [0.8, 1, 0.8],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-600 dark:text-gray-300">
                Neue Nachricht
              </span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    );
  };

  // Single enhanced iPhone component
  const PhoneComponent = () => {
    // Fixed 3D rotation to the left - no scroll animation

    return (
      <motion.div
        className="relative w-80 lg:w-96 h-[650px] lg:h-[750px]" // Larger size
        initial={{ opacity: 0, rotateY: -35, scale: 0.8 }}
        animate={{
          opacity: 1,
          rotateY: -20, // Fixed rotation to the left for 3D effect
          scale: 1,
        }}
        transition={{
          duration: 1.2,
          ease: "easeOut",
        }}
        style={{
          perspective: "1200px",
          transformStyle: "preserve-3d",
        }}
        whileHover={
          shouldReduceMotion
            ? {}
            : {
                scale: 1.03,
                rotateY: -15, // Slightly less rotation on hover
                transition: { duration: 0.4, ease: "easeOut" },
              }
        }
      >
        {/* Enhanced iPhone Body with more realistic design */}
        <motion.div
          className="relative w-full h-full bg-gradient-to-br from-gray-900 via-black to-gray-800 rounded-[3rem] p-3 shadow-2xl"
          style={{
            transformStyle: "preserve-3d",
            boxShadow:
              "0 30px 60px -12px rgba(0, 0, 0, 0.9), inset 0 1px 0 rgba(255, 255, 255, 0.1)",
          }}
        >
          {/* Realistic metallic frame */}
          <div className="absolute inset-0 rounded-[3rem] bg-gradient-to-r from-gray-500 via-gray-300 to-gray-500 p-[3px]">
            <div className="w-full h-full bg-gradient-to-br from-gray-900 via-black to-gray-800 rounded-[2.8rem]"></div>
          </div>

          {/* Screen with improved realism */}
          <div className="relative w-full h-full bg-black rounded-[2.6rem] overflow-hidden z-10 border border-gray-600">
            {/* Enhanced Dynamic Island */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-8 bg-black rounded-b-3xl z-30 shadow-2xl border-x border-b border-gray-700">
              <div className="absolute top-3 left-1/2 transform -translate-x-1/2 w-12 h-1.5 bg-gray-700 rounded-full"></div>
              <div className="absolute top-3 right-4 w-1.5 h-1.5 bg-gray-700 rounded-full"></div>
            </div>

            {/* Improved Status Bar */}
            <div className="h-12 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-between px-8 text-sm font-semibold text-black dark:text-white pt-4 relative z-20">
              <span className="font-bold">12:35</span>
              <div className="flex items-center gap-1">
                <div className="w-6 h-3 border border-black dark:border-white rounded-sm">
                  <div className="w-4 h-2 bg-green-500 rounded-sm m-0.5"></div>
                </div>
              </div>
            </div>

            {/* Screen Content Container */}
            <div
              className="relative w-full bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 overflow-hidden"
              style={{ height: "calc(100% - 3rem)" }}
            >
              {renderModernAppContent()}
            </div>
          </div>

          {/* Enhanced glow effect */}
          <motion.div
            className="absolute inset-0 rounded-[3rem] opacity-0"
            style={{
              background:
                "linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3))",
              filter: "blur(30px)",
            }}
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.4 }}
          />
        </motion.div>

        {/* Enhanced shadow with 3D depth */}
        <div className="absolute inset-0 bg-black/40 dark:bg-black/60 rounded-[3rem] blur-3xl -z-10 scale-110 translate-y-8 translate-x-2"></div>
      </motion.div>
    );
  };

  return (
    <div
      className={`relative w-full h-full flex items-center justify-center ${className}`}
    >
      {/* Single Phone Layout */}
      <PhoneComponent />

      {/* Enhanced Floating Elements */}
      {!shouldReduceMotion && (
        <>
          {/* Flutter-themed floating elements */}
          <motion.div
            className="absolute top-1/4 left-1/6 w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center"
            animate={{
              y: [0, -25, 0],
              opacity: [0.3, 0.8, 0.3],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <div className="w-6 h-6 relative">
              <Image
                src="/icons/flutter.svg"
                alt="Flutter"
                fill
                className="object-contain"
              />
            </div>
          </motion.div>

          <motion.div
            className="absolute bottom-1/4 right-1/6 w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center"
            animate={{
              y: [0, 20, 0],
              opacity: [0.2, 0.7, 0.2],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1,
            }}
          >
            <div className="w-5 h-5 relative">
              <Image
                src="/icons/supabase.svg"
                alt="Supabase"
                fill
                className="object-contain"
              />
            </div>
          </motion.div>

          <motion.div
            className="absolute top-1/3 right-1/4 w-8 h-8 bg-purple-500/20 rounded-full"
            animate={{
              y: [0, -15, 0],
              x: [0, 10, 0],
              opacity: [0.4, 0.9, 0.4],
            }}
            transition={{
              duration: 3.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          />

          <motion.div
            className="absolute bottom-1/3 left-1/4 w-9 h-9 bg-orange-500/20 rounded-full"
            animate={{
              y: [0, 18, 0],
              x: [0, -8, 0],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 4.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.5,
            }}
          />
        </>
      )}
    </div>
  );
};

AnimatedHeroPhone.displayName = "AnimatedHeroPhone";

export default AnimatedHeroPhone;