"use client";

import React from "react";
import { motion, useReducedMotion } from "framer-motion";
import { 
  Code, 
  Smartphone, 
  Zap, 
  Layers, 
  Database, 
  Cloud, 
  Settings, 
  Users,
  TrendingUp,
  Shield
} from "lucide-react";

interface AnimatedHeroPhoneProps {
  className?: string;
}

const AnimatedHeroPhone: React.FC<AnimatedHeroPhoneProps> = ({
  className = "",
}) => {
  const shouldReduceMotion = useReducedMotion();
  const [currentScreen, setCurrentScreen] = React.useState(0);

  // Auto-cycle through screens
  React.useEffect(() => {
    if (!shouldReduceMotion) {
      const interval = setInterval(() => {
        setCurrentScreen((prev) => (prev + 1) % 3);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [shouldReduceMotion]);

  return (
    <div className={`relative w-full h-full flex items-center justify-center ${className}`}>
      {/* 3D Phone Container */}
      <motion.div
        className="relative w-72 lg:w-80 h-[600px] lg:h-[640px]"
        initial={{ opacity: 0, rotateY: -25, x: 50 }}
        animate={{
          opacity: 1,
          rotateY: shouldReduceMotion ? 0 : [-8, 8, -8],
          x: 0,
        }}
        transition={{
          duration: shouldReduceMotion ? 0.6 : 1.2,
          rotateY: {
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          },
        }}
        style={{ perspective: "1000px" }}
        whileHover={
          shouldReduceMotion
            ? {}
            : {
                scale: 1.02,
                rotateY: 0,
                transition: { duration: 0.3 },
              }
        }
      >
        {/* Phone Body */}
        <motion.div
          className="relative w-full h-full bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-[3rem] p-3 shadow-2xl border border-gray-700"
          style={{
            transformStyle: "preserve-3d",
          }}
        >
          {/* Metallic Edge Effect */}
          <div className="absolute inset-0 rounded-[3rem] bg-gradient-to-r from-gray-600 via-gray-300 to-gray-600 p-[1px]">
            <div className="w-full h-full bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-[3rem]"></div>
          </div>

          {/* Screen */}
          <div className="relative w-full h-full bg-black rounded-[2.8rem] overflow-hidden z-10">
            {/* Dynamic Island */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black rounded-b-3xl z-20 shadow-lg">
              <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gray-700 rounded-full"></div>
              <div className="absolute top-2 right-4 w-2 h-2 bg-gray-700 rounded-full"></div>
            </div>

            {/* Status Bar */}
            <div className="h-8 bg-gradient-to-b from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 flex items-center justify-between px-8 text-sm font-semibold text-black dark:text-white pt-2 relative z-10">
              <span className="font-bold">12:35</span>
              <div className="flex items-center gap-1">
                <div className="w-6 h-3 border border-black dark:border-white rounded-sm">
                  <div className="w-4 h-2 bg-green-500 rounded-sm m-0.5"></div>
                </div>
              </div>
            </div>

            {/* Screen Content */}
            <div className="relative w-full h-full bg-white dark:bg-gray-900 overflow-hidden" style={{ height: "calc(100% - 2rem)" }}>
              
              {/* Screen 1: Code Editor */}
              <motion.div
                className="absolute inset-0 w-full h-full bg-gradient-to-br from-gray-900 to-black text-white p-6"
                initial={{ opacity: currentScreen === 0 ? 1 : 0, x: 0 }}
                animate={{ 
                  opacity: currentScreen === 0 ? 1 : 0,
                  x: currentScreen === 0 ? 0 : currentScreen > 0 ? -100 : 100
                }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                {/* Code Editor Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <Code className="w-5 h-5 text-blue-400" />
                    <span className="text-sm font-medium">app.tsx</span>
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  </div>
                </div>

                {/* Animated Code Lines */}
                <div className="space-y-2 text-xs font-mono">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className="text-purple-400"
                  >
                    import React from 'react';
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-blue-400"
                  >
                    const App = () =&gt; &#123;
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                    className="text-yellow-400 ml-4"
                  >
                    return (
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.8 }}
                    className="text-green-400 ml-8"
                  >
                    &lt;div&gt;Innovation&lt;/div&gt;
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.0 }}
                    className="text-yellow-400 ml-4"
                  >
                    );
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.2 }}
                    className="text-blue-400"
                  >
                    &#125;;
                  </motion.div>
                </div>

                {/* Animated Cursor */}
                <motion.div
                  className="w-0.5 h-4 bg-white mt-2"
                  animate={{ opacity: [1, 0, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                />
              </motion.div>

              {/* Screen 2: App Analytics */}
              <motion.div
                className="absolute inset-0 w-full h-full bg-white dark:bg-gray-900 p-6"
                initial={{ opacity: currentScreen === 1 ? 1 : 0, x: 100 }}
                animate={{ 
                  opacity: currentScreen === 1 ? 1 : 0,
                  x: currentScreen === 1 ? 0 : currentScreen > 1 ? -100 : 100
                }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">Analytics</h3>
                  <TrendingUp className="w-5 h-5 text-green-500" />
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                  <motion.div
                    className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-xl"
                    whileHover={{ scale: 1.05 }}
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="w-4 h-4 text-blue-600" />
                      <span className="text-xs font-medium text-gray-600 dark:text-gray-300">Users</span>
                    </div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white">12.4K</div>
                    <div className="text-xs text-green-600">+23%</div>
                  </motion.div>

                  <motion.div
                    className="bg-green-50 dark:bg-green-900/20 p-3 rounded-xl"
                    whileHover={{ scale: 1.05 }}
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <Zap className="w-4 h-4 text-green-600" />
                      <span className="text-xs font-medium text-gray-600 dark:text-gray-300">Performance</span>
                    </div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white">98%</div>
                    <div className="text-xs text-green-600">+5%</div>
                  </motion.div>
                </div>

                {/* Chart Visualization */}
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-xl">
                  <div className="flex items-end justify-between h-20 space-x-1">
                    {[40, 65, 45, 80, 60, 90, 75].map((height, i) => (
                      <motion.div
                        key={i}
                        className="bg-gradient-to-t from-blue-600 to-purple-600 rounded-t-sm flex-1"
                        initial={{ height: 0 }}
                        animate={{ height: `${height}%` }}
                        transition={{ delay: i * 0.1, duration: 0.6 }}
                      />
                    ))}
                  </div>
                </div>
              </motion.div>

              {/* Screen 3: Architecture */}
              <motion.div
                className="absolute inset-0 w-full h-full bg-white dark:bg-gray-900 p-6"
                initial={{ opacity: currentScreen === 2 ? 1 : 0, x: 100 }}
                animate={{ 
                  opacity: currentScreen === 2 ? 1 : 0,
                  x: currentScreen === 2 ? 0 : currentScreen < 2 ? 100 : -100
                }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">Architecture</h3>
                  <Layers className="w-5 h-5 text-purple-500" />
                </div>

                {/* Architecture Components */}
                <div className="space-y-4">
                  <motion.div
                    className="flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
                    initial={{ x: -50, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    <Smartphone className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">Frontend</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">React Native / Flutter</div>
                    </div>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg"
                    initial={{ x: -50, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    <Cloud className="w-5 h-5 text-green-600" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">Backend</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">Node.js / Microservices</div>
                    </div>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg"
                    initial={{ x: -50, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.6 }}
                  >
                    <Database className="w-5 h-5 text-purple-600" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">Database</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">PostgreSQL / MongoDB</div>
                    </div>
                  </motion.div>

                  <motion.div
                    className="flex items-center space-x-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg"
                    initial={{ x: -50, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.8 }}
                  >
                    <Shield className="w-5 h-5 text-orange-600" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">Security</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">End-to-end encryption</div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Subtle glow effect */}
          <motion.div
            className="absolute inset-0 rounded-[3rem] opacity-0"
            style={{
              background: "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1))",
              filter: "blur(20px)",
            }}
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          />
        </motion.div>

        {/* Shadow */}
        <div className="absolute inset-0 bg-black/20 dark:bg-black/40 rounded-[3rem] blur-2xl -z-10 scale-110 translate-y-4"></div>
      </motion.div>

      {/* Floating Elements */}
      {!shouldReduceMotion && (
        <>
          <motion.div
            className="absolute top-1/4 left-1/4 w-8 h-8 bg-blue-500/20 rounded-full"
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.7, 0.3],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          <motion.div
            className="absolute bottom-1/4 right-1/4 w-6 h-6 bg-purple-500/20 rounded-full"
            animate={{
              y: [0, 15, 0],
              opacity: [0.2, 0.6, 0.2],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1,
            }}
          />
        </>
      )}
    </div>
  );
};

AnimatedHeroPhone.displayName = "AnimatedHeroPhone";

export default AnimatedHeroPhone;