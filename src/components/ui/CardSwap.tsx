"use client";

import React, {
  Children,
  cloneElement,
  forwardRef,
  isValidElement,
  ReactElement,
  ReactNode,
  RefObject,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

// Conditional GSAP import with error handling
let gsap: any;
try {
  gsap = require("gsap");
} catch (error) {
  console.error("GSAP not found, using fallback animations");
  gsap = null;
}

export interface CardSwapProps {
  width?: number | string;
  height?: number | string;
  cardDistance?: number;
  verticalDistance?: number;
  delay?: number;
  pauseOnHover?: boolean;
  onCardClick?: (idx: number) => void;
  skewAmount?: number;
  easing?: "linear" | "elastic";
  children: ReactNode;
}

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  customClass?: string;
}

export const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ customClass, ...rest }, ref) => (
    <div
      ref={ref}
      {...rest}
      className={`absolute top-1/2 left-1/2 rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-2xl [transform-style:preserve-3d] [will-change:transform] [backface-visibility:hidden] ${customClass ?? ""} ${rest.className ?? ""}`.trim()}
    />
  )
);
Card.displayName = "Card";

type CardRef = RefObject<HTMLDivElement>;
interface Slot {
  x: number;
  y: number;
  z: number;
  zIndex: number;
}

const makeSlot = (
  i: number,
  distX: number,
  distY: number,
  total: number
): Slot => ({
  x: i * distX,
  y: -i * distY,
  z: -i * distX * 1.5,
  zIndex: total - i,
});

const placeNow = (el: HTMLElement, slot: Slot, skew: number) => {
  if (gsap) {
    gsap.set(el, {
      x: slot.x,
      y: slot.y,
      z: slot.z,
      xPercent: -50,
      yPercent: -50,
      skewY: skew,
      transformOrigin: "center center",
      zIndex: slot.zIndex,
      force3D: true,
    });
  } else {
    // Fallback CSS positioning
    el.style.transform = `translate(-50%, -50%) translate3d(${slot.x}px, ${slot.y}px, ${slot.z}px) skewY(${skew}deg)`;
    el.style.zIndex = slot.zIndex.toString();
  }
};

const CardSwap: React.FC<CardSwapProps> = ({
  width = 500,
  height = 400,
  cardDistance = 60,
  verticalDistance = 70,
  delay = 5000,
  pauseOnHover = false,
  onCardClick,
  skewAmount = 6,
  easing = "elastic",
  children,
}) => {
  const [isGsapReady, setIsGsapReady] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const config = useMemo(() => {
    if (!gsap) return null;

    return easing === "elastic"
      ? {
          ease: "elastic.out(0.6,0.9)",
          durDrop: 2,
          durMove: 2,
          durReturn: 2,
          promoteOverlap: 0.9,
          returnDelay: 0.05,
        }
      : {
          ease: "power1.inOut",
          durDrop: 0.8,
          durMove: 0.8,
          durReturn: 0.8,
          promoteOverlap: 0.45,
          returnDelay: 0.2,
        };
  }, [easing]);

  const childArr = useMemo(
    () => Children.toArray(children) as ReactElement<CardProps>[],
    [children]
  );
  const refs = useMemo<CardRef[]>(
    () => childArr.map(() => React.createRef<HTMLDivElement>()),
    [childArr.length]
  );

  const order = useRef<number[]>(
    Array.from({ length: childArr.length }, (_, i) => i)
  );

  const tlRef = useRef<any>(null);
  const intervalRef = useRef<number>();
  const container = useRef<HTMLDivElement>(null);

  // Debug logging
  useEffect(() => {
    console.log("CardSwap mounted, children count:", childArr.length);
    console.log("GSAP available:", !!gsap);
    console.log("Container ref:", container.current);
    console.log(
      "Child refs:",
      refs.map((r) => r.current)
    );
  }, []);

  useEffect(() => {
    if (!gsap) {
      console.log("Using fallback animation mode");
      setIsGsapReady(false);
      // Simple fallback animation
      const fallbackInterval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % childArr.length);
      }, delay);

      return () => clearInterval(fallbackInterval);
    }

    const total = refs.length;

    // Ensure all refs are available before starting
    const allRefsReady = refs.every((ref) => ref.current);
    if (!allRefsReady) {
      console.log("Not all refs ready, waiting...");
      return;
    }

    console.log("Initializing GSAP animations...");
    setIsGsapReady(true);

    refs.forEach((r, i) => {
      if (r.current) {
        placeNow(
          r.current!,
          makeSlot(i, cardDistance, verticalDistance, total),
          skewAmount
        );
      }
    });

    const swap = () => {
      if (order.current.length < 2) return;

      // Check if all elements are still available
      const allElementsReady = order.current.every((idx) => refs[idx].current);
      if (!allElementsReady) return;

      const [front, ...rest] = order.current;
      const elFront = refs[front].current!;
      const tl = gsap.timeline();
      tlRef.current = tl;

      tl.to(elFront, {
        y: "+=500",
        duration: config!.durDrop,
        ease: config!.ease,
      });

      tl.addLabel("promote", `-=${config!.durDrop * config!.promoteOverlap}`);
      rest.forEach((idx, i) => {
        const el = refs[idx].current!;
        const slot = makeSlot(i, cardDistance, verticalDistance, refs.length);
        tl.set(el, { zIndex: slot.zIndex }, "promote");
        tl.to(
          el,
          {
            x: slot.x,
            y: slot.y,
            z: slot.z,
            duration: config!.durMove,
            ease: config!.ease,
          },
          `promote+=${i * 0.15}`
        );
      });

      const backSlot = makeSlot(
        refs.length - 1,
        cardDistance,
        verticalDistance,
        refs.length
      );
      tl.addLabel(
        "return",
        `promote+=${config!.durMove * config!.returnDelay}`
      );
      tl.call(
        () => {
          gsap.set(elFront, { zIndex: backSlot.zIndex });
        },
        undefined,
        "return"
      );
      tl.set(elFront, { x: backSlot.x, z: backSlot.z }, "return");
      tl.to(
        elFront,
        {
          y: backSlot.y,
          duration: config!.durReturn,
          ease: config!.ease,
        },
        "return"
      );

      tl.call(() => {
        order.current = [...rest, front];
      });
    };

    // Initial swap with delay
    const initialTimeout = setTimeout(() => {
      swap();
    }, 1000);

    // Set up interval
    intervalRef.current = window.setInterval(swap, delay);

    if (pauseOnHover && container.current) {
      const node = container.current;
      const pause = () => {
        tlRef.current?.pause();
        clearInterval(intervalRef.current);
      };
      const resume = () => {
        tlRef.current?.play();
        intervalRef.current = window.setInterval(swap, delay);
      };
      node.addEventListener("mouseenter", pause);
      node.addEventListener("mouseleave", resume);
      return () => {
        clearTimeout(initialTimeout);
        node.removeEventListener("mouseenter", pause);
        node.removeEventListener("mouseleave", resume);
        clearInterval(intervalRef.current);
        tlRef.current?.kill();
      };
    }

    return () => {
      clearTimeout(initialTimeout);
      clearInterval(intervalRef.current);
      tlRef.current?.kill();
    };
  }, [
    cardDistance,
    verticalDistance,
    delay,
    pauseOnHover,
    skewAmount,
    easing,
    refs.length,
    config,
  ]);

  const rendered = childArr.map((child, i) =>
    isValidElement<CardProps>(child)
      ? cloneElement(child, {
          key: i,
          ref: refs[i],
          style: {
            width: typeof width === "number" ? `${width}px` : width,
            height: typeof height === "number" ? `${height}px` : height,
            opacity: !gsap && i !== currentIndex ? 0.3 : 1,
            transition: !gsap ? "opacity 0.5s ease-in-out" : "none",
            ...(child.props.style ?? {}),
          },
          onClick: (e) => {
            child.props.onClick?.(e as React.MouseEvent<HTMLDivElement>);
            onCardClick?.(i);
          },
        } as CardProps & React.RefAttributes<HTMLDivElement>)
      : child
  );

  return (
    <div
      ref={container}
      className="relative w-full h-full flex items-center justify-center perspective-[900px] overflow-visible"
      style={{ width, height }}
    >
      {rendered}
      {/* Debug info */}
      {process.env.NODE_ENV === "development" && (
        <div className="absolute top-0 left-0 text-xs text-red-500 bg-white p-1 rounded">
          GSAP: {gsap ? "OK" : "NO"} | Cards: {childArr.length} | Ready:{" "}
          {isGsapReady ? "YES" : "NO"}
        </div>
      )}
    </div>
  );
};

export default CardSwap;
