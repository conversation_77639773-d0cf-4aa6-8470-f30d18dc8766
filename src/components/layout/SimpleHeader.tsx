'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { motion, AnimatePresence } from 'framer-motion'
import { usePathname } from "next/navigation";
import { useI18n, type Locale } from "@/providers/I18nProvider";
import { LanguageSelector } from "../ui/LanguageSelector";
import { Menu, X, Sparkles, Code, Zap, Users, Award, MessageCircle, Phone, DollarSign } from "lucide-react";
import { type LucideIcon } from "lucide-react";

// Interface for header navigation links with React component icons
interface HeaderNavigationLink {
  key: string;
  href: string;
  icon: LucideIcon;
}

// Updated navigation links with translation keys - matching active sections
const defaultNavigationLinks: HeaderNavigationLink[] = [
  { key: "home", href: "#hero", icon: Sparkles },
  { key: "usp", href: "#usp", icon: Award },
  { key: "services", href: "#services", icon: Code },
  { key: "aienhanced", href: "#ai-enhanced", icon: Zap },
  { key: "portfolio", href: "#portfolio", icon: Users },
  { key: "pricing", href: "#pricing", icon: DollarSign },
  { key: "testimonials", href: "#testimonials", icon: MessageCircle },
  { key: "contact", href: "#contact", icon: Phone },
];

interface HeaderProps {
  navigation?: HeaderNavigationLink[];
}

export function SimpleHeader({
  navigation = defaultNavigationLinks,
}: HeaderProps) {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { theme, setTheme } = useTheme();
  const { locale, setLocale, t, dir, loading } = useI18n();
  const pathname = usePathname();
  const supportedLocales = ["en", "de", "ru", "tr", "ar"];

  // Toggle theme function
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // Check if we're in dark mode
  const isDarkMode = theme === "dark";

  // RTL support
  const rtlClass = dir === "rtl" ? "rtl-layout" : "";

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Close mobile menu when clicking on a link and handle smooth scrolling
  const handleLinkClick = (
    event: React.MouseEvent<HTMLAnchorElement>,
    href: string
  ) => {
    setMobileMenuOpen(false);

    // Handle smooth scrolling for anchor links
    if (href.startsWith("#")) {
      event.preventDefault();
      const targetId = href.substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        setTimeout(() => {
          targetElement.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }, 100); // Small delay to allow mobile menu to close first
      }
    }
  };

  // Generate proper href with locale
  const getHrefWithLocale = (href: string) => {
    if (href.startsWith("#")) {
      // For anchor links, check if we're on a page that's not the homepage
      const isOnHomepage =
        pathname === `/${locale}` ||
        pathname === `/${locale}/` ||
        pathname === "/" ||
        pathname === "";

      if (!isOnHomepage) {
        // Navigate to homepage with anchor
        return `/${locale}${href}`;
      }

      return href; // Stay on current page with anchor
    }
    return `/${locale}${href}`; // Add locale prefix for page routes
  };

  // Handle scroll events to change header appearance with throttling
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const offset = window.scrollY;
          setScrolled(offset > 10);
          ticking = false;
        });
        ticking = true;
      }
    };

    handleScroll();
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Wenn die Übersetzungen noch geladen werden, zeigen wir nichts an
  if (loading) {
    return null;
  }

  // Modern glassmorphism header with dynamic backgrounds
  const headerStyle = scrolled 
    ? "bg-white/10 dark:bg-gray-900/10 backdrop-blur-xl border-b border-white/10 dark:border-gray-700/10 shadow-lg" 
    : "bg-gradient-to-r from-slate-50/80 via-white/60 to-blue-50/40 dark:from-gray-900/80 dark:via-gray-800/60 dark:to-gray-900/80";

  // Enhanced effects for modern look
  const headerEffects = scrolled 
    ? "backdrop-blur-xl shadow-2xl shadow-blue-500/10 dark:shadow-blue-400/5" 
    : "backdrop-blur-sm";

  // Dynamic text color based on scroll and theme
  const textColor = scrolled 
    ? "text-gray-900 dark:text-white" 
    : "text-gray-900 dark:text-white";

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-[100] transition-all duration-500 ${headerStyle} ${headerEffects} ${textColor} ${rtlClass}`}
      style={{ transform: "translateZ(0)" }}
    >
      {/* Animated background glow */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-blue-500/5 opacity-0 hover:opacity-100 transition-opacity duration-700"></div>
      
      <div className="w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20 3xl:px-24 4xl:px-32 flex items-center justify-between h-16 relative z-10">
        <div className="flex items-center">
          <Link href={`/${locale}/#hero`} className="group flex items-center gap-3 hover:scale-105 transition-transform duration-300">
            {/* Enhanced logo with modern styling */}
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="flex items-center gap-3"
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur opacity-20 group-hover:opacity-40 transition-opacity duration-300"></div>
                <Image
                  src="/images/w_company_logo.png"
                  alt="Innovatio Logo"
                  width={40}
                  height={40}
                  priority
                  className="h-auto relative z-10 transition-transform duration-300 group-hover:rotate-12"
                />
              </div>
              <div className="flex flex-col">
                <span className="font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent whitespace-nowrap">
                  Innovatio-Pro
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap -mt-1">
                  Digital Excellence
                </span>
              </div>
            </motion.div>
          </Link>
        </div>

        {/* Enhanced Mobile Menu Button */}
        <div className="md:hidden">
          <motion.button
            onClick={toggleMobileMenu}
            className="relative flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-200/20 dark:border-blue-400/20 hover:from-blue-500/20 hover:to-purple-500/20 transition-all duration-300 shadow-lg hover:shadow-xl"
            aria-label="Toggle mobile menu"
            whileTap={{ scale: 0.95 }}
            whileHover={{ scale: 1.05 }}
          >
            <motion.div
              animate={mobileMenuOpen ? { rotate: 180 } : { rotate: 0 }}
              transition={{ duration: 0.3 }}
            >
              {mobileMenuOpen ? (
                <X className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              ) : (
                <Menu className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              )}
            </motion.div>
            
            {/* Active indicator */}
            {mobileMenuOpen && (
              <motion.div
                className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl blur opacity-30"
                initial={{ opacity: 0 }}
                animate={{ opacity: 0.3 }}
                transition={{ duration: 0.3 }}
              />
            )}
          </motion.button>
        </div>

        {/* Enhanced Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-2">
          {navigation.map((link, index) => {
            const IconComponent = link.icon;
            return (
              <motion.div
                key={link.key || ""}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Link
                  href={getHrefWithLocale(link.href || "#")}
                  className="group relative flex items-center gap-2 px-3 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 hover:backdrop-blur-sm overflow-hidden"
                  onClick={(e) => handleLinkClick(e, link.href || "#")}
                >
                  {/* Background glow on hover */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  {/* Icon */}
                  {IconComponent && (
                    <IconComponent className="w-5 h-5 text-blue-600 dark:text-blue-400 group-hover:text-blue-500 dark:group-hover:text-blue-300 transition-colors duration-300 flex-shrink-0" />
                  )}
                  
                  {/* Text - Hidden by default, shown on hover */}
                  <span className="relative text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-all duration-300 whitespace-nowrap overflow-hidden max-w-0 group-hover:max-w-xs group-hover:ml-2">
                    {t(`nav.${link.key || ""}`) ||
                      (link.key
                        ? link.key.charAt(0).toUpperCase() + link.key.slice(1)
                        : "Link")}
                  </span>
                  
                  {/* Active indicator */}
                  <motion.div
                    className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full group-hover:w-full transition-all duration-300"
                    whileHover={{ width: "100%" }}
                  />
                </Link>
              </motion.div>
            );
          })}

          {/* Enhanced Theme Toggle Button */}
          <motion.button
            onClick={toggleTheme}
            className="relative flex items-center justify-center p-3 rounded-xl bg-gradient-to-r from-yellow-500/10 to-blue-500/10 backdrop-blur-sm border border-yellow-200/20 dark:border-blue-400/20 hover:from-yellow-500/20 hover:to-blue-500/20 transition-all duration-300 shadow-lg hover:shadow-xl"
            aria-label={isDarkMode ? "Switch to light mode" : "Switch to dark mode"}
            whileTap={{ scale: 0.95 }}
            whileHover={{ scale: 1.05 }}
          >
            <motion.div
              animate={{ rotate: isDarkMode ? 180 : 0 }}
              transition={{ duration: 0.5 }}
            >
              {isDarkMode ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5 text-yellow-500"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5 text-blue-600"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z"
                  />
                </svg>
              )}
            </motion.div>
            
            {/* Glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-blue-500/10 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
          </motion.button>

          {/* Enhanced Language Selector */}
          <div className="relative">
            <LanguageSelector
              currentLocale={locale}
              locales={supportedLocales}
              onChange={(code) => setLocale(code as Locale)}
              className="text-gray-700 dark:text-gray-300"
            />
          </div>
        </nav>
      </div>

      {/* Enhanced Mobile Menu Dropdown */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="md:hidden absolute top-full left-0 right-0 z-50"
          >
            <div className="mx-4 mt-2 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-gray-700/30 overflow-hidden">
              {/* Navigation Links */}
              <div className="py-4">
                {navigation.map((link, index) => {
                  const IconComponent = link.icon;
                  return (
                    <motion.div
                      key={link.key || ""}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1, duration: 0.3 }}
                    >
                      <Link
                        href={getHrefWithLocale(link.href || "#")}
                        className="group flex items-center px-6 py-4 text-gray-800 dark:text-gray-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20 transition-all duration-300 border-r-4 border-transparent hover:border-blue-500"
                        onClick={(e) => handleLinkClick(e, link.href || "#")}
                      >
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-3">
                            {IconComponent && (
                              <IconComponent className="w-5 h-5 text-blue-500 dark:text-blue-400 group-hover:text-blue-600 dark:group-hover:text-blue-300 transition-colors duration-300" />
                            )}
                            <span className="font-medium text-lg group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                              {t(`nav.${link.key || ""}`) ||
                                (link.key
                                  ? link.key.charAt(0).toUpperCase() +
                                    link.key.slice(1)
                                  : "Link")}
                            </span>
                          </div>
                          <motion.div
                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                            whileHover={{ x: 5 }}
                          >
                            <svg
                              className="w-5 h-5 text-blue-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 5l7 7-7 7"
                              />
                            </svg>
                          </motion.div>
                        </div>
                      </Link>
                    </motion.div>
                  );
                })}
              </div>

              {/* Divider */}
              <div className="border-t border-gray-200 dark:border-gray-700"></div>

              {/* Enhanced Controls Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.3 }}
                className="p-6 bg-gradient-to-r from-gray-50/50 to-blue-50/30 dark:from-gray-800/50 dark:to-blue-900/30"
              >
                <div className="flex items-center justify-between gap-4">
                  {/* Enhanced Theme Toggle */}
                  <motion.button
                    onClick={toggleTheme}
                    className="flex items-center gap-3 px-4 py-3 rounded-xl bg-gradient-to-r from-yellow-500/10 to-blue-500/10 dark:from-yellow-500/20 dark:to-blue-500/20 shadow-md hover:shadow-lg transition-all duration-300 border border-yellow-200/20 dark:border-blue-400/20 backdrop-blur-sm"
                    aria-label={
                      isDarkMode
                        ? "Switch to light mode"
                        : "Switch to dark mode"
                    }
                    whileTap={{ scale: 0.95 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <motion.div
                      animate={{ rotate: isDarkMode ? 180 : 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      {isDarkMode ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-5 h-5 text-yellow-500"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-5 h-5 text-blue-600"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z"
                          />
                        </svg>
                      )}
                    </motion.div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {isDarkMode ? "Light" : "Dark"}
                    </span>
                  </motion.button>

                  {/* Enhanced Language Selector */}
                  <div className="flex items-center px-4 py-3 bg-white/50 dark:bg-gray-700/50 rounded-xl border border-gray-200/50 dark:border-gray-600/50 backdrop-blur-sm">
                    <LanguageSelector
                      currentLocale={locale}
                      locales={supportedLocales}
                      onChange={(code) => setLocale(code as Locale)}
                      className="text-gray-700 dark:text-gray-300"
                    />
                  </div>
                </div>

                {/* Enhanced Additional Info */}
                <div className="mt-4 pt-4 border-t border-gray-200/50 dark:border-gray-600/50">
                  <div className="flex items-center justify-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full animate-pulse"></div>
                    <span>Innovatio-Pro • Ready to innovate</span>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}
