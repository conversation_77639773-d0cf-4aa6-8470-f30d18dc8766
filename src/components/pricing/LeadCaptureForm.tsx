"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  User,
  Mail,
  Building,
  Target,
  Calendar,
  DollarSign,
  MessageSquare,
  CheckCircle,
  Phone,
  Send,
  Sparkles,
  Shield,
  Clock,
  MessageCircle,
} from "lucide-react";
import { type Dictionary } from "@/lib/dictionary";

interface LeadCaptureFormProps {
  dictionary: Dictionary["leadCaptureForm"];
}

// Default fallback data in case dictionary is missing
const defaultDictionary = {
  headline: "Start Your Flutter Journey in 3 Steps",
  introduction: "Book your free discovery call in just 2 minutes",
  steps: {
    personal: "Your Details",
    project: "Project Info", 
    final: "Final Details",
  },
  stepDescriptions: {
    personal: "Tell us who you are",
    project: "Share your vision",
    final: "Add final touches",
  },
  form: {
    fullName: "Full Name",
    email: "Email Address",
    company: "Company",
    mvpGoal: {
      label: "What's your primary goal?",
      placeholder: "Select your main objective",
      options: {
        prototyping: "Validate my idea with a prototype",
        mvpLaunch: "Build and launch my MVP",
        saasScaling: "Scale my existing SaaS product",
        aiIntegration: "Add AI features to my app",
        consulting: "Get technical guidance and strategy",
        notSure: "Not sure yet - need guidance",
      },
    },
    timeline: {
      label: "Timeline",
      placeholder: "When to start?",
      options: {
        asap: "As soon as possible",
        fourToSix: "In 4-6 weeks",
        threeMonths: "In 2-3 months",
        undecided: "Timeline is flexible",
      },
    },
    budget: {
      label: "Budget",
      placeholder: "Budget range",
      options: {
        below10k: "Below €10,000",
        "10to20k": "€10,000 - €20,000",
        "20to50k": "€20,000 - €50,000",
        above50k: "€50,000+",
        notSure: "Not sure yet",
      },
    },
    additionalNotes: {
      label: "Tell us about your project",
      placeholder: "Briefly describe your app idea, target users, or any specific requirements...",
    },
    whatHappensNext: {
      title: "What happens next?",
      steps: [
        "We'll review your project within 24 hours",
        "Schedule a 15-30 minute discovery call",
        "Get personalized guidance for your MVP",
      ],
    },
    validation: {
      nameRequired: "Name is required",
      emailRequired: "Email is required",
      emailInvalid: "Please enter a valid email address",
      goalRequired: "Please select your primary goal",
      timelineRequired: "Please select your preferred timeline",
    },
  },
  navigation: {
    back: "← Back",
    next: "Next →",
    submit: "Book Discovery Call",
    submitting: "Submitting...",
    stepOf: "Step",
  },
  successMessage: {
    title: "Thank You!",
    description: "We'll reach out within 24 hours to schedule your free discovery call.",
    scheduleCall: "Schedule Call Now",
    whatsapp: "WhatsApp",
    backToSite: "← Back to Homepage",
    nextSteps: {
      title: "What happens next:",
      steps: [
        "Review your project details",
        "Personal reach out within 24h",
        "Schedule 15-30 min discovery call",
        "Explore your vision & provide guidance",
      ],
    },
  },
  footer: {
    preferDirectContact: "Prefer direct contact?",
    whatsapp: "WhatsApp",
    email: "Email",
  },
};

interface FormData {
  fullName: string;
  email: string;
  company: string;
  mvpGoal: string;
  timeline: string;
  budget: string;
  additionalNotes: string;
}

const LeadCaptureForm: React.FC<LeadCaptureFormProps> = ({ dictionary }) => {
  // Use provided dictionary or fallback to default
  const dict = dictionary || defaultDictionary;

  // Additional safety checks for nested properties
  const safeDict = {
    ...defaultDictionary,
    ...dict,
    steps: {
      ...defaultDictionary.steps,
      ...dict.steps,
    },
    stepDescriptions: {
      ...defaultDictionary.stepDescriptions,
      ...dict.stepDescriptions,
    },
    form: {
      ...defaultDictionary.form,
      ...dict.form,
      mvpGoal: {
        ...defaultDictionary.form.mvpGoal,
        ...dict.form?.mvpGoal,
        options: {
          ...defaultDictionary.form.mvpGoal.options,
          ...dict.form?.mvpGoal?.options,
        },
      },
      timeline: {
        ...defaultDictionary.form.timeline,
        ...dict.form?.timeline,
        options: {
          ...defaultDictionary.form.timeline.options,
          ...dict.form?.timeline?.options,
        },
      },
      budget: {
        ...defaultDictionary.form.budget,
        ...dict.form?.budget,
        options: {
          ...defaultDictionary.form.budget.options,
          ...dict.form?.budget?.options,
        },
      },
      additionalNotes: {
        ...defaultDictionary.form.additionalNotes,
        ...dict.form?.additionalNotes,
      },
      whatHappensNext: {
        ...defaultDictionary.form.whatHappensNext,
        ...dict.form?.whatHappensNext,
      },
      validation: {
        ...defaultDictionary.form.validation,
        ...dict.form?.validation,
      },
    },
    navigation: {
      ...defaultDictionary.navigation,
      ...dict.navigation,
    },
    successMessage: {
      ...defaultDictionary.successMessage,
      ...dict.successMessage,
      nextSteps: {
        ...defaultDictionary.successMessage.nextSteps,
        ...dict.successMessage?.nextSteps,
      },
    },
    footer: {
      ...defaultDictionary.footer,
      ...dict.footer,
    },
  };

  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    fullName: "",
    email: "",
    company: "",
    mvpGoal: "",
    timeline: "",
    budget: "",
    additionalNotes: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});

  const totalSteps = 3;

  const stepTitles = [safeDict.steps.personal, safeDict.steps.project, safeDict.steps.final];

  const stepDescriptions = [
    safeDict.stepDescriptions.personal,
    safeDict.stepDescriptions.project,
    safeDict.stepDescriptions.final,
  ];

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Partial<FormData> = {};

    if (step === 1) {
      if (!formData.fullName.trim()) {
        newErrors.fullName = safeDict.form.validation.nameRequired;
      }
      if (!formData.email.trim()) {
        newErrors.email = safeDict.form.validation.emailRequired;
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = safeDict.form.validation.emailInvalid;
      }
    }

    if (step === 2) {
      if (!formData.mvpGoal) {
        newErrors.mvpGoal = safeDict.form.validation.goalRequired;
      }
      if (!formData.timeline) {
        newErrors.timeline = safeDict.form.validation.timelineRequired;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, totalSteps));
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateStep(currentStep)) return;

    setIsSubmitting(true);

    try {
      // Submit to lead capture API
      const response = await fetch("/api/lead-capture", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        console.log("✅ Lead captured successfully:", result.data?.leadId);
        setIsSubmitted(true);

        // Auto-redirect to Calendly after showing success message
        setTimeout(() => {
          window.open(
            "https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01",
            "_blank"
          );
        }, 2000);
      } else {
        throw new Error(result.error || "Failed to submit form");
      }
    } catch (error) {
      console.error("Form submission error:", error);

      // Fallback: Still show success message and redirect
      // This ensures good UX even if Notion is down
      console.log("📝 Fallback: Lead capture form submitted locally");
      setIsSubmitted(true);

      setTimeout(() => {
        window.open(
          "https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01",
          "_blank"
        );
      }, 2000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const createWhatsAppMessage = () => {
    const message = encodeURIComponent(
      `Hi! I'm interested in discussing my Flutter MVP project.

Name: ${formData.fullName}
${formData.company ? `Company: ${formData.company}` : ""}
Goal: ${formData.mvpGoal}
Timeline: ${formData.timeline}
${formData.budget ? `Budget: ${formData.budget}` : ""}
${formData.additionalNotes ? `Additional Notes: ${formData.additionalNotes}` : ""}

I'd like to schedule a free discovery call to discuss my project requirements.

Thank you!`
    );
    return `https://wa.me/491759918357?text=${message}`;
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center py-8 max-w-lg mx-auto"
      >
        <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
        </div>

        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          {safeDict.successMessage.title}
        </h3>

        <p className="text-gray-600 dark:text-gray-400 mb-6 text-sm">
          {safeDict.successMessage.description}
        </p>

        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">
            {safeDict.successMessage.nextSteps.title}
          </h4>
          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1 text-left">
            {safeDict.successMessage.nextSteps.steps.map(
              (step: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <div className="w-1 h-1 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                  {step}
                </li>
              )
            )}
          </ul>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
          <a
            href="https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 rounded-lg font-medium transition-colors text-sm"
          >
            <Calendar className="w-4 h-4" />
            {safeDict.successMessage.scheduleCall}
          </a>

          <a
            href={createWhatsAppMessage()}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2.5 rounded-lg font-medium transition-colors text-sm"
          >
            <MessageCircle className="w-4 h-4" />
            {safeDict.successMessage.whatsapp}
          </a>
        </div>

        <button
          onClick={() => setIsSubmitted(false)}
          className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors mt-4 text-xs"
        >
          {safeDict.successMessage.backToSite}
        </button>
      </motion.div>
    );
  }

  return (
    <div className="w-100 mx-auto">
      {/* Header - Compact Design */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center gap-3 mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-100 dark:border-blue-800/30 shadow-sm">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div className="text-left">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
              {safeDict.headline}
            </h3>
            <p className="text-blue-600 dark:text-blue-400 text-sm font-medium">
              {safeDict.introduction}
            </p>
          </div>
        </div>
      </div>

      {/* Two Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
        {/* Left Column - Animated Steps */}
        <div className="space-y-6">
          {stepTitles.map((title, index) => {
            const stepNum = index + 1;
            const isActive = stepNum === currentStep;
            const isCompleted = stepNum < currentStep;
            
            return (
              <motion.div
                key={stepNum}
                initial={{ opacity: 0, x: -20 }}
                animate={{ 
                  opacity: 1, 
                  x: 0,
                  scale: isActive ? 1.02 : 1
                }}
                transition={{ 
                  duration: 0.4, 
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 200,
                  damping: 20
                }}
                className={`relative p-6 rounded-xl border-2 transition-all duration-300 ${
                  isCompleted
                    ? "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-300 dark:border-green-600 shadow-md"
                    : isActive
                      ? "bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border-blue-400 dark:border-blue-500 shadow-lg"
                      : "bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700"
                }`}
              >
                {/* Step Number Circle */}
                <div className="flex items-start gap-4">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                      isCompleted
                        ? "bg-green-500 border-green-500 text-white"
                        : isActive
                          ? "bg-blue-500 border-blue-500 text-white"
                          : "bg-gray-200 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400"
                    }`}
                  >
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : (
                      <span className="text-lg font-bold">{stepNum}</span>
                    )}
                  </div>
                  
                  {/* Step Content */}
                  <div className="flex-1">
                    <h4 className={`text-lg font-bold mb-2 transition-colors ${
                      isCompleted
                        ? "text-green-700 dark:text-green-300"
                        : isActive
                          ? "text-blue-700 dark:text-blue-300"
                          : "text-gray-600 dark:text-gray-400"
                    }`}>
                      {title}
                    </h4>
                    <p className={`text-sm transition-colors ${
                      isCompleted
                        ? "text-green-600 dark:text-green-400"
                        : isActive
                          ? "text-blue-600 dark:text-blue-400"
                          : "text-gray-500 dark:text-gray-500"
                    }`}>
                      {stepDescriptions[index]}
                    </p>
                    
                    {/* Progress indicator for active step */}
                    {isActive && (
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 0.5 }}
                        className="mt-3 h-1 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full"
                      />
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Right Column - Form */}
        <div className="lg:sticky lg:top-8 lg:self-start">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -15 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-xl border border-gray-200 dark:border-gray-700"
          >
            <form
              onSubmit={
                currentStep === totalSteps
                  ? handleSubmit
                  : (e) => {
                      e.preventDefault();
                      nextStep();
                    }
              }
            >
              {/* Step 1: Basic Info */}
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4" />
                          {safeDict.form.fullName} *
                        </div>
                      </label>
                      <input
                        type="text"
                        value={formData.fullName}
                        onChange={(e) =>
                          handleInputChange("fullName", e.target.value)
                        }
                        placeholder={safeDict.form.fullName}
                        className={`w-full px-4 py-3 border rounded-lg transition-colors ${
                          errors.fullName
                            ? "border-red-300 focus:border-red-500"
                            : "border-gray-200 dark:border-gray-600 focus:border-blue-500"
                        } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                      />
                      {errors.fullName && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.fullName}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4" />
                          {safeDict.form.email} *
                        </div>
                      </label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) =>
                          handleInputChange("email", e.target.value)
                        }
                        placeholder={safeDict.form.email}
                        className={`w-full px-4 py-3 border rounded-lg transition-colors ${
                          errors.email
                            ? "border-red-300 focus:border-red-500"
                            : "border-gray-200 dark:border-gray-600 focus:border-blue-500"
                        } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                      />
                      {errors.email && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.email}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      <div className="flex items-center gap-2">
                        <Building className="w-4 h-4" />
                        {safeDict.form.company}{" "}
                        <span className="text-gray-400 text-sm">(Optional)</span>
                      </div>
                    </label>
                    <input
                      type="text"
                      value={formData.company}
                      onChange={(e) =>
                        handleInputChange("company", e.target.value)
                      }
                      placeholder={safeDict.form.company}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                    />
                  </div>
                </div>
              )}

              {/* Step 2: Project Details */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      <div className="flex items-center gap-2">
                        <Target className="w-4 h-4" />
                        {safeDict.form.mvpGoal.label} *
                      </div>
                    </label>
                    <select
                      value={formData.mvpGoal}
                      onChange={(e) =>
                        handleInputChange("mvpGoal", e.target.value)
                      }
                      className={`w-full px-4 py-3 border rounded-lg transition-colors ${
                        errors.mvpGoal
                          ? "border-red-300 focus:border-red-500"
                          : "border-gray-200 dark:border-gray-600 focus:border-blue-500"
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                    >
                      <option value="">{safeDict.form.mvpGoal.placeholder}</option>
                      {Object.entries(safeDict.form.mvpGoal.options).map(([key, value]) => (
                        <option key={key} value={key}>
                          {value as string}
                        </option>
                      ))}
                    </select>
                    {errors.mvpGoal && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors.mvpGoal}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          {safeDict.form.timeline.label} *
                        </div>
                      </label>
                      <select
                        value={formData.timeline}
                        onChange={(e) =>
                          handleInputChange("timeline", e.target.value)
                        }
                        className={`w-full px-4 py-3 border rounded-lg transition-colors ${
                          errors.timeline
                            ? "border-red-300 focus:border-red-500"
                            : "border-gray-200 dark:border-gray-600 focus:border-blue-500"
                        } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                      >
                        <option value="">{safeDict.form.timeline.placeholder}</option>
                        {Object.entries(safeDict.form.timeline.options).map(([key, value]) => (
                          <option key={key} value={key}>
                            {value as string}
                          </option>
                        ))}
                      </select>
                      {errors.timeline && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.timeline}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <div className="flex items-center gap-2">
                          <DollarSign className="w-4 h-4" />
                          {safeDict.form.budget.label}{" "}
                          <span className="text-gray-400 text-sm">
                            (Optional)
                          </span>
                        </div>
                      </label>
                      <select
                        value={formData.budget}
                        onChange={(e) =>
                          handleInputChange("budget", e.target.value)
                        }
                        className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                      >
                        <option value="">{safeDict.form.budget.placeholder}</option>
                        {Object.entries(safeDict.form.budget.options).map(([key, value]) => (
                          <option key={key} value={key}>
                            {value as string}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Final Details */}
              {currentStep === 3 && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      <div className="flex items-center gap-2">
                        <MessageSquare className="w-4 h-4" />
                        {safeDict.form.additionalNotes.label}{" "}
                        <span className="text-gray-400 text-sm">(Optional)</span>
                      </div>
                    </label>
                    <textarea
                      value={formData.additionalNotes}
                      onChange={(e) =>
                        handleInputChange("additionalNotes", e.target.value)
                      }
                      placeholder={safeDict.form.additionalNotes.placeholder}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                    />
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      <span className="font-medium text-blue-900 dark:text-blue-100 text-sm">
                        {safeDict.form.whatHappensNext.title}
                      </span>
                    </div>
                    <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                      {safeDict.form.whatHappensNext.steps.map((step, index) => (
                        <li key={index}>• {step}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Navigation */}
              <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div>
                  {currentStep > 1 && (
                    <button
                      type="button"
                      onClick={prevStep}
                      className="inline-flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                    >
                      {safeDict.navigation.back}
                    </button>
                  )}
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {safeDict.navigation.stepOf} {currentStep} of {totalSteps}
                  </div>

                  {currentStep < totalSteps ? (
                    <button
                      type="submit"
                      className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-6 rounded-lg transition-colors"
                    >
                      {safeDict.navigation.next}
                    </button>
                  ) : (
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg hover:shadow-xl"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          {safeDict.navigation.submitting}
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4" />
                          {safeDict.navigation.submit}
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </form>
          </motion.div>

          {/* Quick Contact Footer */}
          <div className="text-center mt-6">
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
              {safeDict.footer.preferDirectContact}
            </p>
            <div className="flex justify-center gap-4">
              <a
                href={createWhatsAppMessage()}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 transition-colors text-sm"
              >
                <Phone className="w-3 h-3" />
                {safeDict.footer.whatsapp}
              </a>
              <span className="text-gray-300 dark:text-gray-600">•</span>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center gap-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors text-sm"
              >
                <Mail className="w-3 h-3" />
                {safeDict.footer.email}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export { LeadCaptureForm };
export default LeadCaptureForm;
