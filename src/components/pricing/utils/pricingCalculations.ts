// Pricing calculation utilities

// Type definitions
export type Industry = 'ecommerce' | 'health' | 'finance' | 'social' | 'productivity' | 'other';
export type Timeline = 'normal' | 'fast' | 'urgent';
export type Complexity = 'simple' | 'medium' | 'complex';

// Feature types
export type StandardFeature = keyof typeof standardFeaturePricing;
export type PrototypeFeature = keyof typeof prototypeFeaturePricing;

// Base prices for different service types
export const basePrices = {
  mvp: 8500, // Base price for MVP development
  prototype: 4200, // Base price for rapid prototype
  homepage: 3000, // Base price for homepage/landing page
  landingpage: 3000, // Base price for accessible landing page
  architecture: 3800, // Base price for project architecture
  consulting: 85 // Hourly rate for consulting
};

// Hourly rate range (80-90€)
export const hourlyRate = {
  min: 80,
  max: 90,
  average: 85
};

// Standard feature pricing
export const standardFeaturePricing = {
  // Authentication options
  auth_email: 300,
  auth_google: 200,
  auth_apple: 200,
  auth_facebook: 200,
  auth_phone: 250,
  
  // User profile features
  profile: 350,
  user_roles: 400,
  
  // Common features
  push: 450,
  offline: 500,
  analytics: 350,
  in_app_purchase: 600,
  subscription: 700,
  
  // Device features
  camera: 300,
  maps: 500,
  barcode: 350,
  biometric: 400,
  
  // Data & Storage
  cloud_sync: 550,
  file_upload: 400,
  
  // Social features
  social_sharing: 250,
  chat: 800
};

// Prototype feature pricing
export const prototypeFeaturePricing = {
  // UI Design features
  ui_design: 800,
  navigation: 600,
  animations: 400,
  dark_mode: 300,
  
  // Mockup features
  login_mockup: 200,
  profile_mockup: 200,
  dashboard_mockup: 250,
  settings_mockup: 150,
  
  // Interaction features
  clickable: 300,
  form_validation: 250,
  transitions: 350,
  gestures: 400,
  
  // Presentation features
  demo_mode: 300,
  shareable: 200,
  feedback: 250
};

// Industry complexity factors for MVP and Prototype
export const industryFactors = {
  ecommerce: 1.2,
  health: 1.3,
  finance: 1.4,
  social: 1.1,
  productivity: 1.0,
  other: 1.0
};

// Timeline acceleration factors
export const timelineFactors = {
  normal: 1.0,
  fast: 1.3,
  urgent: 1.6
};

// Complexity factors
export const complexityFactors = {
  simple: 0.8,
  medium: 1.0,
  complex: 1.4
};

// Feature pricing for homepage/landing
export const featurePricing = {
  blog: 300,
  gallery: 200,
  analytics: 150,
  social: 100,
  newsletter: 250,
  payment: 500,
  booking: 600
};

// Interface for MVP calculator data
export interface MVPCalculatorData {
  industry: Industry;
  featureCount: number;
  timeline: Timeline;
  selectedFeatures?: string[];
  customFeatures?: string;
  needsReview?: boolean;
}

// Interface for price breakdown item
export interface PriceBreakdownItem {
  label: string;
  price: number;
}

// Interface for calculation result
export interface CalculationResult {
  basePrice: number;
  maintenance?: number;
  developmentTime: string;
  totalPrice: number;
  formattedPrice: string;
  formattedMaintenance?: string;
  hourlyRate?: number;
  formattedHourlyRate?: string;
  requiresReview?: boolean;
  customFeatures?: string;
  breakdown: PriceBreakdownItem[];
  isAppStoreReady?: boolean;
  hasBackendLogic?: boolean;
  calculatorType?: string;
  timestamp?: string;
  calculationId?: string;
  price?: number;
  marketPrice?: number;
  savings?: number;
  timeline?: string;
  error?: string;
}

// Calculate MVP price
export const calculateMVPPrice = (data: MVPCalculatorData): CalculationResult => {
  const { industry, featureCount, timeline, selectedFeatures = [], customFeatures = "", needsReview = false } = data;
  
  // Base calculation
  let basePrice = basePrices.mvp;
  
  // Apply industry factor
  const industryFactor = industryFactors[industry] || industryFactors.other;
  basePrice *= industryFactor;
  
  // Apply feature count adjustment (base features)
  const featureAdjustment = Math.max(0, featureCount - 5) * 500; // Each feature beyond 5 adds $500
  basePrice += featureAdjustment;
  
  // Calculate selected features cost
  let featuresCost = 0;
  const featuresBreakdown: PriceBreakdownItem[] = [];
  
  if (selectedFeatures.length > 0) {
    selectedFeatures.forEach((feature: string) => {
      if (standardFeaturePricing[feature as keyof typeof standardFeaturePricing]) {
        featuresCost += standardFeaturePricing[feature as keyof typeof standardFeaturePricing];
        featuresBreakdown.push({
          label: `Feature: ${feature.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}`,
          price: standardFeaturePricing[feature as keyof typeof standardFeaturePricing]
        });
      } else if (prototypeFeaturePricing[feature as keyof typeof prototypeFeaturePricing]) {
        featuresCost += prototypeFeaturePricing[feature as keyof typeof prototypeFeaturePricing];
        featuresBreakdown.push({
          label: `UI Feature: ${feature.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}`,
          price: prototypeFeaturePricing[feature as keyof typeof prototypeFeaturePricing]
        });
      }
    });
  }
  
  basePrice += featuresCost;
  
  // Apply timeline factor
  const timelineFactor = timelineFactors[timeline as keyof typeof timelineFactors] || timelineFactors.normal;
  basePrice *= timelineFactor;
  
  // Round to nearest hundred
  basePrice = Math.round(basePrice / 100) * 100;
  
  // Calculate monthly maintenance (10% of project cost)
  const maintenance = Math.round(basePrice * 0.1);
  
  // Calculate development time (weeks) - with 40% faster time-to-market
  let developmentTime;
  switch (timeline) {
    case 'urgent':
      developmentTime = '1-2 weeks'; // 40% faster than standard 2-3 weeks
      break;
    case 'fast':
      developmentTime = '2-3 weeks'; // 40% faster than standard 3-4 weeks
      break;
    default:
      developmentTime = '3-4 weeks'; // 40% faster than standard 4-6 weeks
  }
  
  // Calculate hourly rate based on project complexity
  const calculatedHourlyRate = hourlyRate.min + 
    (industryFactor > 1 ? 5 : 0) + 
    (timelineFactor > 1 ? 5 : 0);
  
  // If custom features are specified, mark for review
  const requiresReview = needsReview || customFeatures.trim().length > 0;
  
  const breakdown = [
    { label: 'Base MVP Development (App Store Ready)', price: basePrices.mvp },
    { label: `Industry Factor (${industry})`, price: Math.round((industryFactor - 1) * basePrices.mvp) },
    { label: `Additional Core Features (${Math.max(0, featureCount - 5)})`, price: featureAdjustment },
    ...featuresBreakdown,
    { label: `Timeline Adjustment (${timeline})`, price: Math.round((timelineFactor - 1) * (basePrices.mvp * industryFactor + featureAdjustment + featuresCost)) }
  ];
  
  return {
    basePrice: basePrice,
    maintenance,
    developmentTime,
    totalPrice: basePrice,
    formattedPrice: formatPrice(basePrice),
    formattedMaintenance: formatPrice(maintenance),
    hourlyRate: calculatedHourlyRate,
    formattedHourlyRate: formatPrice(calculatedHourlyRate, true),
    requiresReview,
    customFeatures,
    breakdown,
    isAppStoreReady: true,
    hasBackendLogic: true
  };
};

// Interface for Prototype calculator data
export interface PrototypeCalculatorData {
  complexity: Complexity;
  userRoles: number;
  integrations: string[];
  selectedFeatures?: string[];
  timeline?: Timeline;
  customFeatures?: string;
  needsReview?: boolean;
}

// Calculate Prototype price
export const calculatePrototypePrice = (data: PrototypeCalculatorData): CalculationResult => {
  const { complexity, userRoles, integrations } = data;
  
  // Base calculation
  const basePrice = basePrices.prototype;
  
  // Adjust for complexity
  const complexityFactor = complexityFactors[complexity as keyof typeof complexityFactors] || 1.0;
  
  // Adjust for user roles (each role beyond 1 adds 10% to the price)
  const userRoleFactor = 1 + Math.max(0, userRoles - 1) * 0.1;
  
  // Adjust for integrations (each integration beyond the required one adds 5% to the price)
  const integrationFactor = 1 + Math.max(0, integrations.length - 1) * 0.05;
  
  // Calculate final price
  const calculatedPrice = basePrice * complexityFactor * userRoleFactor * integrationFactor;
  
  // Round to nearest hundred
  const roundedPrice = Math.round(calculatedPrice / 100) * 100;
  
  // Calculate market price (typically 2-3x higher)
  const marketPrice = Math.round(roundedPrice * 2.2 / 100) * 100;
  
  // Calculate timeline based on complexity
  let timelineWeeks = "";
  switch (complexity) {
    case "simple":
      timelineWeeks = "1-2 weeks";
      break;
    case "medium":
      timelineWeeks = "2-3 weeks";
      break;
    case "complex":
      timelineWeeks = "3-4 weeks";
      break;
    default:
      timelineWeeks = "2-3 weeks";
  }
  
  return {
    basePrice: roundedPrice,
    developmentTime: timelineWeeks,
    totalPrice: roundedPrice,
    formattedPrice: formatPrice(roundedPrice),
    price: roundedPrice,
    marketPrice: marketPrice,
    timeline: timelineWeeks,
    savings: marketPrice - roundedPrice,
    breakdown: [
      { label: 'Base Prototype Development', price: basePrices.prototype },
      { label: `Complexity Adjustment (${complexity})`, price: Math.round((complexityFactor - 1) * basePrices.prototype) },
      { label: `User Roles Adjustment`, price: Math.round((userRoleFactor - 1) * basePrices.prototype * complexityFactor) },
      { label: `Integrations Adjustment`, price: Math.round((integrationFactor - 1) * basePrices.prototype * complexityFactor * userRoleFactor) }
    ],
    isAppStoreReady: false,
    hasBackendLogic: false
  };
};

// Interface for Homepage calculator data
export interface HomepageCalculatorData {
  features: string[];
  pageCount: number;
  seo?: boolean;
  multilingual?: boolean;
  timeline?: Timeline;
  customFeatures?: string;
}

// Calculate Homepage price
export const calculateHomepagePrice = (data: HomepageCalculatorData): CalculationResult => {
  const { pageCount, features, seo, multilingual } = data;
  
  // Base calculation
  const basePrice = basePrices.homepage;
  
  // Adjust for page count (each page beyond 1 adds 15% to the price)
  const pageFactor = 1 + Math.max(0, pageCount - 1) * 0.15;
  
  // Add feature costs
  let featuresCost = 0;
  features.forEach((feature: string) => {
    if (feature !== 'responsive' && feature !== 'contact') { // These are included in base price
      featuresCost += featurePricing[feature as keyof typeof featurePricing] || 0;
    }
  });
  
  // Add SEO and multilingual costs
  const seoCost = seo ? 400 : 0;
  const multilingualCost = multilingual ? 600 : 0;
  
  // Calculate final price
  const calculatedPrice = (basePrice * pageFactor) + featuresCost + seoCost + multilingualCost;
  
  // Round to nearest hundred
  const roundedPrice = Math.round(calculatedPrice / 100) * 100;
  
  // Calculate market price (typically 2-3x higher)
  const marketPrice = Math.round(roundedPrice * 3 / 100) * 100;
  
  // Calculate timeline based on complexity
  let timelineWeeks = "";
  if (pageCount <= 3) {
    timelineWeeks = "2-3 days";
  } else if (pageCount <= 7) {
    timelineWeeks = "4-7 days";
  } else {
    timelineWeeks = "1-2 weeks";
  }
  
  return {
    basePrice: roundedPrice,
    developmentTime: timelineWeeks,
    totalPrice: roundedPrice,
    formattedPrice: formatPrice(roundedPrice),
    price: roundedPrice,
    marketPrice: marketPrice,
    timeline: timelineWeeks,
    savings: marketPrice - roundedPrice,
    breakdown: [
      { label: 'Base Homepage Development', price: basePrices.homepage },
      { label: `Page Count Adjustment`, price: Math.round((pageFactor - 1) * basePrices.homepage) },
      { label: `Features Adjustment`, price: featuresCost },
      { label: `SEO Adjustment`, price: seoCost },
      { label: `Multilingual Adjustment`, price: multilingualCost }
    ]
  };
};

// Interface for Consulting calculator data
export interface ConsultingCalculatorData {
  hours?: number;
  expertise?: string;
  projectPhase?: string;
  teamSize?: number;
  expertiseNeeded?: string;
  duration?: number;
  timeline?: Timeline;
}

// Calculate Consulting price
export const calculateConsultingPrice = (data: ConsultingCalculatorData): CalculationResult => {
  const { projectPhase, teamSize, expertiseNeeded, duration } = data;
  
  // Base hourly rate
  const hourlyRate = basePrices.consulting;
  
  // Estimate hours per week based on team size
  const hoursPerWeek = Math.min(40, (teamSize || 1) * 5); // Cap at 40 hours per week
  
  // Adjust for project phase
  let phaseFactor = 1.0;
  switch (projectPhase) {
    case "planning":
      phaseFactor = 0.9;
      break;
    case "development":
      phaseFactor = 1.0;
      break;
    case "maintenance":
      phaseFactor = 0.8;
      break;
    case "optimization":
      phaseFactor = 1.1;
      break;
    default:
      phaseFactor = 1.0;
  }
  
  // Adjust for expertise complexity
  const expertiseFactor = 1 + ((expertiseNeeded || '').length * 0.05);
  
  // Calculate total hours
  const totalHours = hoursPerWeek * (duration || 1);
  
  // Calculate final price
  const calculatedPrice = hourlyRate * totalHours * phaseFactor * expertiseFactor;
  
  // Round to nearest hundred
  const roundedPrice = Math.round(calculatedPrice / 100) * 100;
  
  // Calculate market price (typically 1.5x higher)
  const marketPrice = Math.round(roundedPrice * 1.5 / 100) * 100;
  
  return {
    basePrice: roundedPrice,
    developmentTime: `${duration || 1} ${(duration || 1) === 1 ? 'week' : 'weeks'}`,
    totalPrice: roundedPrice,
    formattedPrice: formatPrice(roundedPrice),
    price: roundedPrice,
    marketPrice: marketPrice,
    hourlyRate: hourlyRate,
    formattedHourlyRate: formatPrice(hourlyRate, true),
    timeline: `${duration || 1} ${(duration || 1) === 1 ? 'week' : 'weeks'}`,
    savings: marketPrice - roundedPrice,
    breakdown: [
      { label: 'Base Consulting Rate', price: hourlyRate * totalHours },
      { label: `Project Phase Adjustment`, price: Math.round((phaseFactor - 1) * hourlyRate * totalHours) },
      { label: `Expertise Adjustment`, price: Math.round((expertiseFactor - 1) * hourlyRate * totalHours * phaseFactor) }
    ]
  };
};

// Calculate price based on calculator type
export const calculatePrice = (calculatorType: string, data: MVPCalculatorData | PrototypeCalculatorData | HomepageCalculatorData | ConsultingCalculatorData): CalculationResult => {
  switch (calculatorType) {
    case 'mvp':
      return calculateMVPPrice(data as MVPCalculatorData);
    case 'prototype':
      return calculatePrototypePrice(data as PrototypeCalculatorData);
    case 'homepage':
      return calculateHomepagePrice(data as HomepageCalculatorData);
    case 'landingpage':
      // Use the same calculator as homepage but with WCAG compliance factors
      const landingpageData = data as HomepageCalculatorData;
      const result = calculateHomepagePrice(landingpageData);

      // Add accessibility features
      if (!result.breakdown.some(item => item.label.includes('WCAG'))) {
        result.breakdown.push({
          label: 'WCAG 2.0 AA Compliance',
          price: 800
        });
        result.totalPrice += 800;
      }

      if (!result.breakdown.some(item => item.label.includes('Screen Reader'))) {
        result.breakdown.push({
          label: 'Screen Reader Compatibility',
          price: 600
        });
        result.totalPrice += 600;
      }

      result.formattedPrice = formatPrice(result.totalPrice);
      result.developmentTime = '2-4 weeks';
      result.calculatorType = 'landingpage';

      return result;
    case 'consulting':
      return calculateConsultingPrice(data as ConsultingCalculatorData);
    case 'architecture':
      // Simple calculation for architecture - flat rate with timeline adjustment
      const architectureData = {
        basePrice: basePrices.architecture,
        totalPrice: basePrices.architecture,
        formattedPrice: formatPrice(basePrices.architecture),
        developmentTime: '1-2 weeks',
        breakdown: [
          { label: 'Project Architecture Services', price: basePrices.architecture }
        ],
        calculatorType: 'architecture'
      };
      return architectureData as CalculationResult;
    default:
      return {
        basePrice: 0,
        totalPrice: 0,
        formattedPrice: '€0',
        developmentTime: 'Not available',
        breakdown: [],
        error: 'Invalid calculator type'
      };
  }
};

// Format price as currency
export const formatPrice = (price: number, isHourly = false): string => {
  if (isHourly) {
    return `€${price}/hour`;
  }
  return `€${price.toLocaleString('en-US')}`;
};
