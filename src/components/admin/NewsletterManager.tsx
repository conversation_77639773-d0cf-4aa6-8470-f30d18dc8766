"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Mail,
  Send,
  Users,
  CheckCircle,
  AlertCircle,
  Clock,
} from "lucide-react";
import { blogService } from "@/lib/blog-service";

interface NewsletterStats {
  totalSubscribers: number;
  successCount: number;
  failCount: number;
  postTitle: string;
  postUrl: string;
}

export const NewsletterManager: React.FC = () => {
  const [selectedPost, setSelectedPost] = useState("");
  const [selectedLocale, setSelectedLocale] = useState("de");
  const [apiSecret, setApiSecret] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<NewsletterStats | null>(null);
  const [error, setError] = useState("");

  // Get all available posts
  const posts = blogService.getAllPosts(selectedLocale);
  const locales = ["de", "en", "ru", "tr", "ar"];

  const handleSendNewsletter = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setResult(null);

    try {
      const response = await fetch("/api/newsletter/notify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiSecret}`,
        },
        body: JSON.stringify({
          postSlug: selectedPost,
          locale: selectedLocale,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data.stats);
      } else {
        setError(data.error || "Failed to send newsletter");
      }
    } catch (err) {
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-8">
        {/* Header */}
        <div className="flex items-center gap-3 mb-8">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <Mail className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Newsletter Manager
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Versenden Sie Newsletter-Benachrichtigungen für neue Blog-Artikel
            </p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSendNewsletter} className="space-y-6">
          {/* API Secret */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              API Secret
            </label>
            <input
              type="password"
              value={apiSecret}
              onChange={(e) => setApiSecret(e.target.value)}
              placeholder="Newsletter API Secret"
              required
              className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Locale Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Sprache
            </label>
            <select
              value={selectedLocale}
              onChange={(e) => setSelectedLocale(e.target.value)}
              className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {locales.map((locale) => (
                <option key={locale} value={locale}>
                  {locale.toUpperCase()}
                </option>
              ))}
            </select>
          </div>

          {/* Post Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Blog-Artikel auswählen
            </label>
            <select
              value={selectedPost}
              onChange={(e) => setSelectedPost(e.target.value)}
              required
              className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">-- Artikel auswählen --</option>
              {posts.map((post) => (
                <option key={post.id} value={post.slug}>
                  {post.title} (
                  {new Date(post.publishedAt).toLocaleDateString()})
                </option>
              ))}
            </select>
          </div>

          {/* Preview */}
          {selectedPost && (
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                Vorschau
              </h3>
              {(() => {
                const post = posts.find((p) => p.slug === selectedPost);
                return post ? (
                  <div className="space-y-2">
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      <strong>Titel:</strong> {post.title}
                    </p>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      <strong>Kategorie:</strong> {post.category}
                    </p>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      <strong>Autor:</strong> {post.author.name}
                    </p>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      <strong>Lesezeit:</strong> {post.readingTime} Minuten
                    </p>
                  </div>
                ) : null;
              })()}
            </div>
          )}

          {/* Submit Button */}
          <motion.button
            type="submit"
            disabled={isLoading || !selectedPost || !apiSecret}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <Clock className="w-5 h-5 animate-spin" />
                Newsletter wird versendet...
              </>
            ) : (
              <>
                <Send className="w-5 h-5" />
                Newsletter versenden
              </>
            )}
          </motion.button>
        </form>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg flex items-center gap-3"
          >
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-red-800 dark:text-red-200">
                Fehler beim Versenden
              </h4>
              <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
            </div>
          </motion.div>
        )}

        {/* Success Message */}
        {result && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 p-6 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg"
          >
            <div className="flex items-center gap-3 mb-4">
              <CheckCircle className="w-6 h-6 text-green-500" />
              <h4 className="font-semibold text-green-800 dark:text-green-200">
                Newsletter erfolgreich versendet!
              </h4>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-green-900/50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="w-4 h-4 text-green-600" />
                  <span className="font-medium text-green-800 dark:text-green-200">
                    Abonnenten
                  </span>
                </div>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {result.totalSubscribers}
                </p>
              </div>

              <div className="bg-white dark:bg-green-900/50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="font-medium text-green-800 dark:text-green-200">
                    Erfolgreich versendet
                  </span>
                </div>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {result.successCount}
                </p>
              </div>
            </div>

            <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-800 dark:text-green-200">
                <strong>Artikel:</strong> {result.postTitle}
              </p>
              <p className="text-sm text-green-800 dark:text-green-200">
                <strong>URL:</strong>{" "}
                <a
                  href={result.postUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline hover:no-underline"
                >
                  {result.postUrl}
                </a>
              </p>
            </div>

            {result.failCount > 0 && (
              <div className="mt-4 p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  <strong>Fehlgeschlagen:</strong> {result.failCount} E-Mails
                  konnten nicht versendet werden.
                </p>
              </div>
            )}
          </motion.div>
        )}

        {/* Instructions */}
        <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
            Anweisungen
          </h3>
          <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
            <p>
              1. Stellen Sie sicher, dass RESEND_API_KEY und RESEND_AUDIENCE_ID
              in den Umgebungsvariablen gesetzt sind
            </p>
            <p>
              2. Geben Sie das Newsletter API Secret ein (NEWSLETTER_API_SECRET)
            </p>
            <p>3. Wählen Sie die gewünschte Sprache aus</p>
            <p>
              4. Wählen Sie den Blog-Artikel aus, für den der Newsletter
              versendet werden soll
            </p>
            <p>5. Klicken Sie auf "Newsletter versenden"</p>
          </div>
        </div>
      </div>
    </div>
  );
};
