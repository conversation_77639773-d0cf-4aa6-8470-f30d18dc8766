'use client'

import React, { useState, useEffect } from 'react'
import { useCMS } from '@/providers/CMSProvider'
import { useI18n } from '@/providers/I18nProvider'
import { Button } from '@/components/ui/Button'
import { HeroContent } from '@/lib/cms/types'
import { Loader2, Save } from 'lucide-react'

export function HeroEditor() {
  const { adapter, currentLocale } = useCMS()
  const { t } = useI18n()
  const [content, setContent] = useState<HeroContent | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Laden des Hero-Contents
  useEffect(() => {
    const loadContent = async () => {
      if (!adapter) return
      
      setIsLoading(true)
      setError(null)
      
      try {
        const heroContent = await adapter.getHeroContent(currentLocale)
        setContent(heroContent)
      } catch (err) {
        console.error('Error loading hero content:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setIsLoading(false)
      }
    }
    
    loadContent()
  }, [adapter, currentLocale])

  // Aktualisieren eines Feldes
  const handleFieldChange = (field: string, value: any) => {
    if (!content) return
    
    // Für verschachtelte Objekte
    if (field.includes('.')) {
      const [parent, child, grandchild] = field.split('.')
      
      if (grandchild) {
        // Für doppelt verschachtelte Objekte (z.B. solutions.streamlinedOperations.title)
        setContent({
          ...content,
          [parent]: {
            ...content[parent as keyof HeroContent],
            [child]: {
              ...(content[parent as keyof HeroContent] as any)[child],
              [grandchild]: value
            }
          }
        })
      } else {
        // Für einfach verschachtelte Objekte (z.B. typing.businessGrowth)
        setContent({
          ...content,
          [parent]: {
            ...content[parent as keyof HeroContent],
            [child]: value
          }
        })
      }
    } else {
      // Für einfache Felder
      setContent({
        ...content,
        [field]: value
      })
    }
  }

  // Speichern des Contents
  const handleSave = async () => {
    if (!adapter || !content) return
    
    setIsSaving(true)
    setError(null)
    
    try {
      await adapter.updateHeroContent(currentLocale, content)
      alert(t('Hero-Content erfolgreich gespeichert!'))
    } catch (err) {
      console.error('Error saving hero content:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsSaving(false)
    }
  }

  // Wenn noch geladen wird
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">{t('Lade Hero-Content...')}</span>
      </div>
    )
  }

  // Wenn ein Fehler aufgetreten ist
  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">{t('Fehler')}</h3>
        <p>{error}</p>
        <Button onClick={() => window.location.reload()} className="mt-4">
          {t('Erneut versuchen')}
        </Button>
      </div>
    )
  }

  // Wenn kein Content geladen wurde
  if (!content) {
    return (
      <div className="text-center p-8">
        <p>{t('Kein Hero-Content verfügbar.')}</p>
        <Button onClick={() => window.location.reload()} className="mt-4">
          {t('Erneut versuchen')}
        </Button>
      </div>
    )
  }

  // Hauptansicht des Editors
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">{t('Hero-Bereich bearbeiten')}</h2>
        
        <Button onClick={handleSave} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              {t('Speichern...')}
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {t('Speichern')}
            </>
          )}
        </Button>
      </div>
      
      {/* Formular */}
      <div className="space-y-6">
        {/* Haupttitel */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium mb-1">
            {t('Haupttitel')}
          </label>
          <input
            type="text"
            id="title"
            value={content.title}
            onChange={(e) => handleFieldChange('title', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
          />
        </div>
        
        {/* Untertitel */}
        <div>
          <label htmlFor="subtitle" className="block text-sm font-medium mb-1">
            {t('Untertitel')}
          </label>
          <input
            type="text"
            id="subtitle"
            value={content.subtitle}
            onChange={(e) => handleFieldChange('subtitle', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
          />
        </div>
        
        {/* Beschreibung */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium mb-1">
            {t('Beschreibung')}
          </label>
          <textarea
            id="description"
            value={content.description}
            onChange={(e) => handleFieldChange('description', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
          />
        </div>
        
        {/* CTA-Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="ctaPrimary" className="block text-sm font-medium mb-1">
              {t('Primärer Button-Text')}
            </label>
            <input
              type="text"
              id="ctaPrimary"
              value={content.ctaPrimary}
              onChange={(e) => handleFieldChange('ctaPrimary', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
            />
          </div>
          
          <div>
            <label htmlFor="bookConsultation" className="block text-sm font-medium mb-1">
              {t('Beratungs-Button-Text')}
            </label>
            <input
              type="text"
              id="bookConsultation"
              value={content.bookConsultation}
              onChange={(e) => handleFieldChange('bookConsultation', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
            />
          </div>
        </div>
        
        {/* Typing-Texte */}
        <div>
          <h3 className="text-lg font-medium mb-3">{t('Typing-Animation-Texte')}</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="typing.businessGrowth" className="block text-sm font-medium mb-1">
                {t('Business Growth')}
              </label>
              <input
                type="text"
                id="typing.businessGrowth"
                value={content.typing.businessGrowth}
                onChange={(e) => handleFieldChange('typing.businessGrowth', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
              />
            </div>
            
            <div>
              <label htmlFor="typing.digitalSolution" className="block text-sm font-medium mb-1">
                {t('Digital Solution')}
              </label>
              <input
                type="text"
                id="typing.digitalSolution"
                value={content.typing.digitalSolution}
                onChange={(e) => handleFieldChange('typing.digitalSolution', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
              />
            </div>
            
            <div>
              <label htmlFor="typing.userExperience" className="block text-sm font-medium mb-1">
                {t('User Experience')}
              </label>
              <input
                type="text"
                id="typing.userExperience"
                value={content.typing.userExperience}
                onChange={(e) => handleFieldChange('typing.userExperience', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
              />
            </div>
            
            <div>
              <label htmlFor="typing.technologyInnovation" className="block text-sm font-medium mb-1">
                {t('Technology Innovation')}
              </label>
              <input
                type="text"
                id="typing.technologyInnovation"
                value={content.typing.technologyInnovation}
                onChange={(e) => handleFieldChange('typing.technologyInnovation', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
              />
            </div>
          </div>
        </div>
        
        {/* Lösungen */}
        <div>
          <h3 className="text-lg font-medium mb-3">{t('Lösungen')}</h3>
          
          {/* Streamlined Operations */}
          <div className="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded-md">
            <h4 className="font-medium mb-2">{t('Streamlined Operations')}</h4>
            
            <div className="space-y-3">
              <div>
                <label htmlFor="solutions.streamlinedOperations.title" className="block text-sm font-medium mb-1">
                  {t('Titel')}
                </label>
                <input
                  type="text"
                  id="solutions.streamlinedOperations.title"
                  value={content.solutions.streamlinedOperations.title}
                  onChange={(e) => handleFieldChange('solutions.streamlinedOperations.title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
              </div>
              
              <div>
                <label htmlFor="solutions.streamlinedOperations.description" className="block text-sm font-medium mb-1">
                  {t('Beschreibung')}
                </label>
                <input
                  type="text"
                  id="solutions.streamlinedOperations.description"
                  value={content.solutions.streamlinedOperations.description}
                  onChange={(e) => handleFieldChange('solutions.streamlinedOperations.description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
              </div>
            </div>
          </div>
          
          {/* Enhanced User Experience */}
          <div className="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded-md">
            <h4 className="font-medium mb-2">{t('Enhanced User Experience')}</h4>
            
            <div className="space-y-3">
              <div>
                <label htmlFor="solutions.enhancedUserExperience.title" className="block text-sm font-medium mb-1">
                  {t('Titel')}
                </label>
                <input
                  type="text"
                  id="solutions.enhancedUserExperience.title"
                  value={content.solutions.enhancedUserExperience.title}
                  onChange={(e) => handleFieldChange('solutions.enhancedUserExperience.title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
              </div>
              
              <div>
                <label htmlFor="solutions.enhancedUserExperience.description" className="block text-sm font-medium mb-1">
                  {t('Beschreibung')}
                </label>
                <input
                  type="text"
                  id="solutions.enhancedUserExperience.description"
                  value={content.solutions.enhancedUserExperience.description}
                  onChange={(e) => handleFieldChange('solutions.enhancedUserExperience.description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
              </div>
            </div>
          </div>
          
          {/* Data Insights */}
          <div className="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded-md">
            <h4 className="font-medium mb-2">{t('Data Insights')}</h4>
            
            <div className="space-y-3">
              <div>
                <label htmlFor="solutions.dataInsights.title" className="block text-sm font-medium mb-1">
                  {t('Titel')}
                </label>
                <input
                  type="text"
                  id="solutions.dataInsights.title"
                  value={content.solutions.dataInsights.title}
                  onChange={(e) => handleFieldChange('solutions.dataInsights.title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
              </div>
              
              <div>
                <label htmlFor="solutions.dataInsights.description" className="block text-sm font-medium mb-1">
                  {t('Beschreibung')}
                </label>
                <input
                  type="text"
                  id="solutions.dataInsights.description"
                  value={content.solutions.dataInsights.description}
                  onChange={(e) => handleFieldChange('solutions.dataInsights.description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
              </div>
            </div>
          </div>
          
          {/* Scalable Architecture */}
          <div className="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded-md">
            <h4 className="font-medium mb-2">{t('Scalable Architecture')}</h4>
            
            <div className="space-y-3">
              <div>
                <label htmlFor="solutions.scalableArchitecture.title" className="block text-sm font-medium mb-1">
                  {t('Titel')}
                </label>
                <input
                  type="text"
                  id="solutions.scalableArchitecture.title"
                  value={content.solutions.scalableArchitecture.title}
                  onChange={(e) => handleFieldChange('solutions.scalableArchitecture.title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
              </div>
              
              <div>
                <label htmlFor="solutions.scalableArchitecture.description" className="block text-sm font-medium mb-1">
                  {t('Beschreibung')}
                </label>
                <input
                  type="text"
                  id="solutions.scalableArchitecture.description"
                  value={content.solutions.scalableArchitecture.description}
                  onChange={(e) => handleFieldChange('solutions.scalableArchitecture.description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
              </div>
            </div>
          </div>
        </div>
        
        {/* Speichern-Button am Ende */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button onClick={handleSave} disabled={isSaving} className="w-full">
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                {t('Speichern...')}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {t('Änderungen speichern')}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
