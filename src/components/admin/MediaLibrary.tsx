'use client'

import React, { useState, useEffect } from 'react'
import { useCMS } from '@/providers/CMSProvider'
import { useI18n } from '@/providers/I18nProvider'
import { Button } from '@/components/ui/Button'
import { ImageAsset } from '@/lib/cms/types'
import { Loader2, Upload, Trash2, Copy, Search, X } from 'lucide-react'
import Image from 'next/image'

export function MediaLibrary() {
  const { adapter } = useCMS()
  const { t } = useI18n()
  const [images, setImages] = useState<ImageAsset[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedImage, setSelectedImage] = useState<ImageAsset | null>(null)
  const fileInputRef = React.useRef<HTMLInputElement>(null)

  // Laden der Bilder
  useEffect(() => {
    const loadImages = async () => {
      if (!adapter) return
      
      setIsLoading(true)
      setError(null)
      
      try {
        const imageAssets = await adapter.getImages()
        setImages(imageAssets)
      } catch (err) {
        console.error('Error loading images:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setIsLoading(false)
      }
    }
    
    loadImages()
  }, [adapter])

  // Bild hochladen
  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!adapter || !e.target.files || e.target.files.length === 0) return
    
    setIsUploading(true)
    setError(null)
    
    try {
      const file = e.target.files[0]
      const alt = file.name.split('.')[0] // Dateiname ohne Erweiterung als Alt-Text
      
      const newImage = await adapter.uploadImage(file, alt)
      setImages([...images, newImage])
      
      // Datei-Input zurücksetzen
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (err) {
      console.error('Error uploading image:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsUploading(false)
    }
  }

  // Bild löschen
  const handleDelete = async (id: string) => {
    if (!adapter) return
    
    if (!window.confirm(t('Sind Sie sicher, dass Sie dieses Bild löschen möchten?'))) {
      return
    }
    
    try {
      const success = await adapter.deleteImage(id)
      
      if (success) {
        setImages(images.filter(img => img.id !== id))
        
        // Wenn das ausgewählte Bild gelöscht wurde, Auswahl zurücksetzen
        if (selectedImage && selectedImage.id === id) {
          setSelectedImage(null)
        }
      }
    } catch (err) {
      console.error('Error deleting image:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }

  // URL in die Zwischenablage kopieren
  const copyUrlToClipboard = (url: string) => {
    navigator.clipboard.writeText(url)
      .then(() => {
        alert(t('URL in die Zwischenablage kopiert!'))
      })
      .catch(err => {
        console.error('Error copying URL:', err)
        setError(t('URL konnte nicht kopiert werden.'))
      })
  }

  // Gefilterte Bilder basierend auf der Suche
  const filteredImages = images.filter(img => 
    img.alt.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Wenn noch geladen wird
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">{t('Lade Bilder...')}</span>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">{t('Medienbibliothek')}</h2>
        
        <div className="flex items-center">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleUpload}
            accept="image/*"
            className="hidden"
            id="image-upload"
          />
          <label htmlFor="image-upload">
            <Button as="span" disabled={isUploading}>
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {t('Hochladen...')}
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  {t('Bild hochladen')}
                </>
              )}
            </Button>
          </label>
        </div>
      </div>
      
      {/* Fehleranzeige */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 p-4 rounded-lg mb-4">
          <p>{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setError(null)} 
            className="mt-2"
          >
            {t('Schließen')}
          </Button>
        </div>
      )}
      
      {/* Suchleiste */}
      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder={t('Bilder durchsuchen...')}
          className="w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
        />
        {searchTerm && (
          <button
            onClick={() => setSearchTerm('')}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <X className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
          </button>
        )}
      </div>
      
      {/* Bildergalerie */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {filteredImages.length > 0 ? (
          filteredImages.map((image) => (
            <div
              key={image.id}
              className={`relative group border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden ${
                selectedImage?.id === image.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setSelectedImage(image)}
            >
              <div className="aspect-square relative">
                <Image
                  src={image.url}
                  alt={image.alt}
                  fill
                  className="object-cover"
                />
              </div>
              
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation()
                      copyUrlToClipboard(image.url)
                    }}
                    className="bg-white dark:bg-gray-800 h-8 w-8"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDelete(image.id)
                    }}
                    className="bg-white dark:bg-gray-800 h-8 w-8 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="p-2 text-sm truncate">{image.alt}</div>
            </div>
          ))
        ) : (
          <div className="col-span-full text-center py-8 text-gray-500 dark:text-gray-400">
            {searchTerm ? t('Keine Bilder gefunden.') : t('Keine Bilder vorhanden.')}
          </div>
        )}
      </div>
      
      {/* Bilddetails */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-3xl w-full max-h-[90vh] overflow-auto">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h3 className="text-lg font-medium">{t('Bilddetails')}</h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSelectedImage(null)}
                className="h-8 w-8"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            
            <div className="p-4">
              <div className="mb-4 max-h-[50vh] overflow-hidden flex items-center justify-center">
                <Image
                  src={selectedImage.url}
                  alt={selectedImage.alt}
                  width={500}
                  height={500}
                  className="max-w-full max-h-[50vh] object-contain"
                />
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {t('Alt-Text')}
                  </label>
                  <input
                    type="text"
                    value={selectedImage.alt}
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {t('URL')}
                  </label>
                  <div className="flex">
                    <input
                      type="text"
                      value={selectedImage.url}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md bg-gray-50 dark:bg-gray-700"
                    />
                    <Button
                      onClick={() => copyUrlToClipboard(selectedImage.url)}
                      className="rounded-l-none"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      {t('Kopieren')}
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {t('Abmessungen')}
                    </label>
                    <input
                      type="text"
                      value={`${selectedImage.width} × ${selectedImage.height} px`}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {t('Dateigröße')}
                    </label>
                    <input
                      type="text"
                      value={`${Math.round(selectedImage.fileSize / 1024)} KB`}
                      readOnly
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                </div>
                
                <div className="pt-4 flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setSelectedImage(null)}
                  >
                    {t('Schließen')}
                  </Button>
                  
                  <Button
                    variant="destructive"
                    onClick={() => {
                      handleDelete(selectedImage.id)
                      setSelectedImage(null)
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t('Löschen')}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
