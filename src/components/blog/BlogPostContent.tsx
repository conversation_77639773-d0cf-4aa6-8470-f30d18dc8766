"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import ReactMarkdown from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import {
  Share2,
  Bookmark,
  Heart,
  Copy,
  ArrowLeft,
  ArrowRight,
  Clock,
  User,
  Eye,
  Calendar,
} from "lucide-react";
import { Dictionary } from "@/lib/dictionary";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  publishedAt: string;
  readingTime: number;
  views: number;
  category: string;
  tags: string[];
  featuredImage: string;
  slug: string;
}

interface BlogPostContentProps {
  post: BlogPost;
  locale: string;
}

export const BlogPostContent: React.FC<BlogPostContentProps> = ({
  post,
  locale,
}) => {
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(42);
  const [showLikeAnimation, setShowLikeAnimation] = useState(false);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        });
      } catch (err) {
        console.log("Error sharing:", err);
      }
    } else {
      // Fallback: copy to clipboard
      await navigator.clipboard.writeText(window.location.href);
    }
  };

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked);
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikeCount((prev) => (isLiked ? prev - 1 : prev + 1));

    // Trigger like animation
    if (!isLiked) {
      setShowLikeAnimation(true);
      setTimeout(() => setShowLikeAnimation(false), 1000);
    }
  };

  const copyToClipboard = async () => {
    await navigator.clipboard.writeText(window.location.href);
  };

  return (
    <article className="max-w-4xl mx-auto">
      {/* Back Navigation - Hidden on mobile since header has back button */}
      <div className="mb-6 sm:mb-8 hidden sm:block">
        <button
          onClick={() => window.history.back()}
          className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors group"
        >
          <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
          Zurück zum Blog
        </button>
      </div>

      {/* Article Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg sm:rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Article Body */}
        <div className="p-4 sm:p-6 lg:p-8 xl:p-12">
          <div className="prose prose-sm sm:prose-base lg:prose-lg dark:prose-invert max-w-none prose-headings:font-bold prose-headings:tracking-tight prose-h1:text-2xl sm:prose-h1:text-3xl lg:prose-h1:text-4xl prose-h1:mb-6 sm:prose-h1:mb-8 prose-h2:text-xl sm:prose-h2:text-2xl lg:prose-h2:text-3xl prose-h2:mt-8 sm:prose-h2:mt-12 prose-h2:mb-4 sm:prose-h2:mb-6 prose-h3:text-lg sm:prose-h3:text-xl lg:prose-h3:text-2xl prose-h3:mt-6 sm:prose-h3:mt-8 prose-h3:mb-3 sm:prose-h3:mb-4 prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed prose-p:mb-4 sm:prose-p:mb-6 prose-li:mb-1 sm:prose-li:mb-2 prose-strong:text-gray-900 dark:prose-strong:text-gray-100 prose-blockquote:border-l-4 prose-blockquote:border-blue-500 prose-blockquote:bg-blue-50 dark:prose-blockquote:bg-blue-900/20 prose-blockquote:p-3 sm:prose-blockquote:p-4 prose-blockquote:rounded-r-lg prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:px-1 sm:prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm">
            <ReactMarkdown
              components={{
                h1: ({ children }) => (
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-6 sm:mb-8 mt-0 border-b-2 border-gray-200 dark:border-gray-700 pb-3 sm:pb-4">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mt-8 sm:mt-12 mb-4 sm:mb-6 flex items-center gap-2 sm:gap-3">
                    <span className="w-1.5 sm:w-2 h-6 sm:h-8 bg-blue-500 rounded-full"></span>
                    <span className="flex-1">{children}</span>
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 dark:text-white mt-6 sm:mt-8 mb-3 sm:mb-4 text-blue-800 dark:text-blue-300">
                    {children}
                  </h3>
                ),
                p: ({ children }) => (
                  <p className="text-base sm:text-lg text-gray-700 dark:text-gray-300 leading-relaxed mb-4 sm:mb-6 first:mt-0">
                    {children}
                  </p>
                ),
                ul: ({ children }) => (
                  <ul className="space-y-2 sm:space-y-3 mb-4 sm:mb-6 ml-4 sm:ml-6">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="space-y-2 sm:space-y-3 mb-4 sm:mb-6 ml-4 sm:ml-6">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-sm sm:text-base text-gray-700 dark:text-gray-300 leading-relaxed flex items-start gap-2 sm:gap-3">
                    <span className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-500 rounded-full mt-2 sm:mt-3 flex-shrink-0"></span>
                    <span className="flex-1">{children}</span>
                  </li>
                ),
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 p-3 sm:p-6 rounded-r-lg my-4 sm:my-6 italic">
                    <div className="text-sm sm:text-base text-blue-800 dark:text-blue-200">
                      {children}
                    </div>
                  </blockquote>
                ),
                strong: ({ children }) => (
                  <strong className="font-semibold text-gray-900 dark:text-gray-100">
                    {children}
                  </strong>
                ),
                code: (props: any) => {
                  const { inline, className, children, ...rest } = props;
                  const match = /language-(\w+)/.exec(className || "");

                  if (!inline && match) {
                    return (
                      <div className="my-4 sm:my-6 -mx-4 sm:mx-0">
                        <div className="bg-gray-800 rounded-t-lg px-3 sm:px-4 py-2 text-gray-300 text-xs sm:text-sm font-medium border-b border-gray-700">
                          {match[1].toUpperCase()}
                        </div>
                        <div className="overflow-x-auto">
                          <SyntaxHighlighter
                            style={vscDarkPlus as any}
                            language={match[1]}
                            PreTag="div"
                            className="rounded-t-none rounded-b-lg text-xs sm:text-sm"
                            customStyle={{
                              margin: 0,
                              padding: "12px 12px",
                              fontSize: "inherit",
                            }}
                            {...rest}
                          >
                            {String(children).replace(/\n$/, "")}
                          </SyntaxHighlighter>
                        </div>
                      </div>
                    );
                  }

                  return (
                    <code
                      className="bg-gray-100 dark:bg-gray-800 px-1 sm:px-2 py-0.5 sm:py-1 rounded text-xs sm:text-sm font-mono text-blue-600 dark:text-blue-400 break-words"
                      {...rest}
                    >
                      {children}
                    </code>
                  );
                },
                hr: () => (
                  <hr className="my-8 border-t-2 border-gray-200 dark:border-gray-700" />
                ),
              }}
            >
              {post.content}
            </ReactMarkdown>
          </div>
        </div>

        {/* Article Actions */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 sm:p-6 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            {/* Social Actions */}
            <div className="flex flex-wrap items-center justify-center sm:justify-start gap-2 sm:gap-3 w-full sm:w-auto">
              <motion.button
                onClick={handleLike}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`
                  relative flex items-center gap-1 sm:gap-2 px-3 py-2 sm:px-4 sm:py-2 rounded-lg text-sm sm:text-base font-medium transition-all duration-300 overflow-hidden
                  ${
                    isLiked
                      ? "bg-red-500 text-white shadow-lg shadow-red-500/25"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400"
                  }
                `}
              >
                <motion.div
                  animate={{
                    scale: isLiked ? [1, 1.2, 1] : 1,
                    rotate: isLiked ? [0, -10, 10, 0] : 0,
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <Heart
                    className={`w-3 h-3 sm:w-4 sm:h-4 ${isLiked ? "fill-current" : ""}`}
                  />
                </motion.div>
                <span className="text-xs sm:text-sm">{likeCount}</span>

                {/* Floating Hearts Animation */}
                <AnimatePresence>
                  {showLikeAnimation && (
                    <>
                      {[...Array(5)].map((_, i) => (
                        <motion.div
                          key={i}
                          initial={{
                            opacity: 1,
                            scale: 0,
                            x: 0,
                            y: 0,
                          }}
                          animate={{
                            opacity: 0,
                            scale: [0, 1, 0],
                            x: (Math.random() - 0.5) * 100,
                            y: -60 - Math.random() * 40,
                          }}
                          exit={{ opacity: 0 }}
                          transition={{
                            duration: 1,
                            delay: i * 0.1,
                            ease: "easeOut",
                          }}
                          className="absolute pointer-events-none"
                        >
                          <Heart className="w-4 h-4 text-red-500 fill-current" />
                        </motion.div>
                      ))}
                    </>
                  )}
                </AnimatePresence>
              </motion.button>

              <motion.button
                onClick={handleBookmark}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`
                  flex items-center gap-1 sm:gap-2 px-3 py-2 sm:px-4 sm:py-2 rounded-lg text-sm sm:text-base font-medium transition-all duration-300
                  ${
                    isBookmarked
                      ? "bg-blue-500 text-white shadow-lg shadow-blue-500/25"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400"
                  }
                `}
              >
                <motion.div
                  animate={{ scale: isBookmarked ? [1, 1.2, 1] : 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Bookmark
                    className={`w-3 h-3 sm:w-4 sm:h-4 ${isBookmarked ? "fill-current" : ""}`}
                  />
                </motion.div>
                <span className="hidden sm:inline">Merken</span>
              </motion.button>

              <motion.button
                onClick={handleShare}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center gap-1 sm:gap-2 px-3 py-2 sm:px-4 sm:py-2 rounded-lg text-sm sm:text-base font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 transition-all duration-300"
              >
                <Share2 className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Teilen</span>
              </motion.button>

              <motion.button
                onClick={copyToClipboard}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center gap-1 sm:gap-2 px-3 py-2 sm:px-4 sm:py-2 rounded-lg text-sm sm:text-base font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300"
              >
                <Copy className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Link kopieren</span>
              </motion.button>
            </div>

            {/* Share Text - Hidden on mobile */}
            <div className="hidden sm:block text-sm text-gray-500 dark:text-gray-400">
              Teilen Sie diesen Artikel mit anderen
            </div>
          </div>
        </div>

        {/* Call-to-Action Section */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 sm:p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
          <div className="text-center">
            <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-2">
              Haben Sie Fragen zu diesem Artikel?
            </h3>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4">
              Kontaktieren Sie uns für eine kostenlose Beratung zu Ihrem
              nächsten Mobile-Projekt.
            </p>
            <div className="flex flex-col gap-2 sm:gap-3 sm:flex-row justify-center">
              <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-lg text-sm sm:text-base font-semibold transition-colors">
                Kostenlose Beratung buchen
              </button>
              <button className="bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800 px-4 py-2 sm:px-6 sm:py-3 rounded-lg text-sm sm:text-base font-semibold hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors">
                Mehr über unsere Services
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation to Next/Previous Posts */}
      <div className="mt-8 sm:mt-12 grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
        {/* Previous Post */}
        <div className="bg-white dark:bg-gray-800 rounded-lg sm:rounded-xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
          <div className="flex items-center gap-2 sm:gap-3 text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-2">
            <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4" />
            <span>Vorheriger Artikel</span>
          </div>
          <h4 className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer line-clamp-2">
            AI Integration in Mobile Apps: Best Practices
          </h4>
        </div>

        {/* Next Post */}
        <div className="bg-white dark:bg-gray-800 rounded-lg sm:rounded-xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-end gap-2 sm:gap-3 text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-2">
            <span>Nächster Artikel</span>
            <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />
          </div>
          <h4 className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer text-right line-clamp-2">
            Performance Optimization Strategies for Flutter
          </h4>
        </div>
      </div>
    </article>
  );
};
