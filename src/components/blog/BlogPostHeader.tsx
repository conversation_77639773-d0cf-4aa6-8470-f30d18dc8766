"use client";

import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { Clock, Eye, Calendar, Tag, ArrowLeft, User } from "lucide-react";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  publishedAt: string;
  readingTime: number;
  views: number;
  category: string;
  tags: string[];
  featuredImage: string;
  slug: string;
}

interface BlogPostHeaderProps {
  post: BlogPost;
  locale: string;
}

export const BlogPostHeader: React.FC<BlogPostHeaderProps> = ({
  post,
  locale,
}) => {
  return (
    <header className="relative">
      {/* Hero Image */}
      <div className="relative h-[50vh] lg:h-[60vh] overflow-hidden rounded-2xl">
        <Image
          src={post.featuredImage}
          alt={post.title}
          fill
          className="object-cover"
          priority
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw"
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

        {/* Back Button */}
        <div className="absolute top-4 left-4 sm:top-6 sm:left-6 z-10">
          <Link
            href={`/${locale}/blog`}
            className="inline-flex items-center gap-1 sm:gap-2 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 px-2 py-1 sm:px-4 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-all group"
          >
            <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 transition-transform group-hover:-translate-x-1" />
            <span className="hidden sm:inline">Zurück zum Blog</span>
            <span className="sm:hidden">Zurück</span>
          </Link>
        </div>

        {/* Category Badge */}
        <div className="absolute top-4 right-4 sm:top-6 sm:right-6 z-10">
          <span className="bg-blue-500 text-white px-2 py-1 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium">
            {post.category.charAt(0).toUpperCase() + post.category.slice(1)}
          </span>
        </div>

        {/* Content Overlay */}
        <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 lg:p-8">
          <div className="max-w-4xl mx-auto">
            {/* Title */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-2xl sm:text-3xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 leading-tight"
            >
              {post.title}
            </motion.h1>

            {/* Excerpt */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-sm sm:text-base lg:text-lg text-gray-200 mb-4 sm:mb-6 max-w-3xl leading-relaxed line-clamp-3 sm:line-clamp-none"
            >
              {post.excerpt}
            </motion.p>

            {/* Meta Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-wrap items-center gap-2 sm:gap-4 lg:gap-6 text-xs sm:text-sm text-gray-300"
            >
              {/* Author */}
              <div className="flex items-center gap-2">
                <div className="relative w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10">
                  <Image
                    src={post.author.avatar}
                    alt={post.author.name}
                    fill
                    className="rounded-full object-cover border-2 border-white/20"
                  />
                </div>
                <div>
                  <div className="flex items-center gap-1 sm:gap-2">
                    <User className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="font-medium">{post.author.name}</span>
                  </div>
                </div>
              </div>

              {/* Date - Hidden on very small screens */}
              <div className="hidden sm:flex items-center gap-2">
                <Calendar className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="whitespace-nowrap">
                  {new Date(post.publishedAt).toLocaleDateString("de-DE", {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}
                </span>
              </div>

              {/* Reading Time */}
              <div className="flex items-center gap-1 sm:gap-2">
                <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="whitespace-nowrap">
                  {post.readingTime} Min
                </span>
              </div>

              {/* Views - Hidden on small screens */}
              <div className="hidden md:flex items-center gap-2">
                <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="whitespace-nowrap">
                  {post.views.toLocaleString()}
                </span>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Tags */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="max-w-4xl mx-auto mt-6 sm:mt-8 px-4 sm:px-0"
      >
        <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-2">
          <Tag className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500 dark:text-gray-400" />
          <span className="text-sm sm:text-base font-medium text-gray-700 dark:text-gray-300">
            Tags:
          </span>
        </div>
        <div className="flex flex-wrap gap-1.5 sm:gap-2">
          {post.tags.map((tag, index) => (
            <span
              key={index}
              className="bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 sm:px-3 sm:py-1 rounded-full text-xs sm:text-sm font-medium hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors cursor-pointer"
            >
              {tag}
            </span>
          ))}
        </div>
      </motion.div>
    </header>
  );
};
