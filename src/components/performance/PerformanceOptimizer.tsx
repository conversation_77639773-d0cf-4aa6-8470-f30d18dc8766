"use client";

import React, { useEffect, useCallback } from 'react';
import Image from 'next/image';

// Optimized Image component with performance enhancements
export const OptimizedHeroImage = ({ 
  src, 
  alt, 
  priority = false, 
  className = "",
  onLoad 
}: {
  src: string;
  alt: string;
  priority?: boolean;
  className?: string;
  onLoad?: () => void;
}) => {
  return (
    <Image
      src={src}
      alt={alt}
      fill
      className={className}
      sizes="(max-width: 640px) 320px, (max-width: 1024px) 384px, 448px"
      priority={priority}
      loading={priority ? "eager" : "lazy"}
      quality={priority ? 90 : 75}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+on//Z"
      onLoad={onLoad}
    />
  );
};

// Performance optimization utilities
const PerformanceOptimizer = () => {
  useEffect(() => {
    // Preload critical LCP image immediately
    const preloadCriticalImages = () => {
      const criticalImages = [
        '/images/hero/togg.png', // Primary LCP image
        '/images/hero-bg.webp',
        '/images/mockups/app_main.webp'
      ];

      criticalImages.forEach((src, index) => {
        const link = document.createElement('link');
        link.rel = index === 0 ? 'preload' : 'prefetch'; // Preload first, prefetch others
        link.as = 'image';
        link.href = src;
        if (index === 0) {
          link.fetchPriority = 'high';
        }
        document.head.appendChild(link);
      });
    };

    // Optimize scroll performance
    const optimizeScrolling = () => {
      let ticking = false;
      
      const handleScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            // Debounced scroll handling
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
      
      return () => window.removeEventListener('scroll', handleScroll);
    };

    // Initialize optimizations immediately for LCP
    preloadCriticalImages();
    injectCriticalCSS();
    
    // DOM optimization after initial render
    const domTimer = setTimeout(() => {
      DOMOptimizer.monitorDOMComplexity();
      DOMOptimizer.enablePerformanceHints();
    }, 1000);
    
    // Lazy load non-critical assets after LCP
    const timer = setTimeout(() => {
      // Additional non-critical prefetching after LCP
      const nonCriticalImages = [
        '/images/hero/aigo.png',
        '/images/hero/menu.png',
        '/images/hero/detoxme.png',
        '/images/hero/reserv.png'
      ];
      
      nonCriticalImages.forEach((src) => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
      });
    }, 2000); // Delay to prioritize LCP

    const cleanupScroll = optimizeScrolling();

    return () => {
      clearTimeout(timer);
      clearTimeout(domTimer);
      cleanupScroll();
    };
  }, []);

  return null;
};

// Hook for progressive image loading in carousels
export const useProgressiveImageLoading = (images: string[]) => {
  const [loadedImages, setLoadedImages] = React.useState<Set<number>>(new Set([0]));
  
  const loadNextImage = useCallback((currentIndex: number) => {
    const nextIndex = (currentIndex + 1) % images.length;
    if (!loadedImages.has(nextIndex)) {
      setLoadedImages(prev => new Set([...prev, nextIndex]));
    }
  }, [images.length, loadedImages]);

  return { loadedImages, loadNextImage };
};

// Critical CSS injection for LCP optimization
export const injectCriticalCSS = () => {
  if (typeof document !== 'undefined' && !document.getElementById('critical-css')) {
    const style = document.createElement('style');
    style.id = 'critical-css';
    style.textContent = `
      /* Critical CSS for LCP optimization */
      .hero-container {
        contain: layout style paint;
        transform: translateZ(0);
        will-change: transform;
      }
      
      .hero-image {
        content-visibility: auto;
        contain-intrinsic-size: 448px 640px;
      }
      
      /* Prevent layout shifts */
      .mockup-container {
        aspect-ratio: 448/640;
        width: 100%;
        max-width: 448px;
      }
      
      /* Optimize hero animations */
      @media (prefers-reduced-motion: reduce) {
        .hero-mockup * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    `;
    document.head.appendChild(style);
  }
};

// DOM optimization utilities
export const DOMOptimizer = {
  // Monitor DOM complexity
  monitorDOMComplexity: () => {
    if (typeof document !== 'undefined' && process.env.NODE_ENV === 'development') {
      const totalElements = document.querySelectorAll('*').length;
      const maxDepth = DOMOptimizer.getMaxDOMDepth(document.body);
      
      if (totalElements > 2000) {
        console.warn(`🚨 DOM Complexity Warning: ${totalElements} elements detected`);
      }
      
      if (maxDepth > 15) {
        console.warn(`🚨 DOM Depth Warning: Maximum depth of ${maxDepth} detected`);
      }
      
      console.log(`📊 DOM Stats: ${totalElements} elements, max depth: ${maxDepth}`);
    }
  },

  // Calculate DOM depth
  getMaxDOMDepth: (element: Element | null, depth = 0): number => {
    if (!element) return depth;
    
    let maxChildDepth = depth;
    for (const child of element.children) {
      const childDepth = DOMOptimizer.getMaxDOMDepth(child, depth + 1);
      maxChildDepth = Math.max(maxChildDepth, childDepth);
    }
    
    return maxChildDepth;
  },

  // Optimize component renders
  enablePerformanceHints: () => {
    if (typeof document !== 'undefined') {
      // Add performance hints to heavy components
      const heavyElements = document.querySelectorAll('[data-heavy]');
      heavyElements.forEach(el => {
        (el as HTMLElement).style.contentVisibility = 'auto';
        (el as HTMLElement).style.contain = 'layout style paint';
      });
    }
  }
};

export default PerformanceOptimizer;