"use client";

import { useEffect, useState } from "react";
import { Metadata } from "next";

// Combined interface for both metadata generation and AI optimization
export interface SEOManagerProps {
  // For metadata generation
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  locale: string;
  alternateLanguages?: Array<{
    locale: string;
    href: string;
  }>;
  openGraph?: {
    title?: string;
    description?: string;
    image?: string;
    type?: "website" | "article" | "product";
    url?: string;
  };
  twitter?: {
    title?: string;
    description?: string;
    image?: string;
    card?: "summary" | "summary_large_image";
  };
  noIndex?: boolean;
  noFollow?: boolean;
  robotsDirectives?: string[];
  
  // For AI optimization (client-side)
  pageType?: "home" | "about" | "services" | "blog" | "contact" | "product";
  primaryKeywords?: string[];
  secondaryKeywords?: string[];
  contentCategory?: string;
  entityType?: "Organization" | "Service" | "Product" | "Article" | "WebPage";
}

const DEFAULT_DOMAIN = "https://innovatio-pro.com";

// Server-side metadata generation function
export function generateSeoMetadata({
  title,
  description,
  keywords,
  canonical,
  locale,
  alternateLanguages = [],
  openGraph,
  twitter,
  noIndex = false,
  noFollow = false,
  robotsDirectives = [],
}: SEOManagerProps): Metadata {
  // Build robots meta content
  const robotsContent: string[] = [];
  if (noIndex) robotsContent.push("noindex");
  if (noFollow) robotsContent.push("nofollow");
  if (!noIndex) robotsContent.push("index");
  if (!noFollow) robotsContent.push("follow");
  robotsContent.push(...robotsDirectives);

  // Default OpenGraph values
  const ogTitle = openGraph?.title || title;
  const ogDescription = openGraph?.description || description;
  const ogImage = openGraph?.image || `${DEFAULT_DOMAIN}/images/og-default.jpg`;
  const ogType = openGraph?.type || "website";
  const ogUrl = openGraph?.url || canonical || DEFAULT_DOMAIN;

  // Default Twitter values
  const twitterTitle = twitter?.title || title;
  const twitterDescription = twitter?.description || description;
  const twitterImage =
    twitter?.image || `${DEFAULT_DOMAIN}/images/twitter-default.jpg`;
  const twitterCard = twitter?.card || "summary_large_image";

  // Generate hreflang based on locale
  const hreflangCode =
    locale === "en"
      ? "en-US"
      : locale === "de"
      ? "de-DE"
      : locale === "ru"
      ? "ru-RU"
      : locale === "tr"
      ? "tr-TR"
      : locale === "ar"
      ? "ar-AE"
      : "en-US";

  // Build alternate languages object
  const languages: Record<string, string> = {};
  alternateLanguages.forEach(({ locale: altLocale, href }) => {
    const altHreflang =
      altLocale === "en"
        ? "en-US"
        : altLocale === "de"
        ? "de-DE"
        : altLocale === "ru"
        ? "ru-RU"
        : altLocale === "tr"
        ? "tr-TR"
        : altLocale === "ar"
        ? "ar-AE"
        : altLocale;
    languages[altHreflang] = href;
  });

  // Add x-default
  languages["x-default"] = `${DEFAULT_DOMAIN}/en`;

  return {
    title,
    description,
    keywords: keywords?.split(",").map((k) => k.trim()),
    robots: {
      index: !noIndex,
      follow: !noFollow,
      googleBot: {
        index: !noIndex,
        follow: !noFollow,
        "max-image-preview": "large",
        "max-video-preview": -1,
        "max-snippet": -1,
      },
    },
    alternates: {
      canonical,
      languages,
    },
    openGraph: {
      title: ogTitle,
      description: ogDescription,
      url: ogUrl,
      siteName: "Innovatio",
      locale: hreflangCode,
      type: ogType as any,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: ogTitle,
        },
      ],
    },
    twitter: {
      card: twitterCard as any,
      title: twitterTitle,
      description: twitterDescription,
      images: [twitterImage],
    },
    metadataBase: new URL(DEFAULT_DOMAIN),
    verification: {
      google: "HD_upcBFgzyK6gtVbiyIyOEavU90TidQk320Qt0HeOY", // Replace with actual verification code
    },
    other: {
      "format-detection": "telephone=no",
      "theme-color": "#3b82f6",
      // Enhanced AI-specific meta tags for better content understanding
      "ai:content-category": "business-services",
      "ai:entity-type": "mobile-app-development",
      "ai:primary-keywords": "App Entwickler Deutschland, Mobile App Entwicklung, iOS Android",
      "ai:target-audience": "german businesses, startups, entrepreneurs",
      "ai:content-intent": "commercial",
      "ai:geographic-focus": "germany",
      "ai:language-market": "german-speaking",
      "ai:service-focus": "mobile-development",
      "ai:expertise-level": "expert",
      "ai:business-type": "b2b-services",
      "ai:industry": "software-development",
      "ai:specialization": "mobile-applications",
      // Content classification for AI training
      "ai:training-relevance": "high",
      "ai:content-quality": "professional",
      "ai:factual-accuracy": "verified",
      "ai:commercial-value": "high",
      // Crawling preferences for AI bots
      "ai:crawl-priority": "high",
      "ai:index-frequency": "weekly",
      "ai:content-freshness": "regularly-updated",
    },
  };
}

// Client-side AI optimization component
const SEOAIOptimizer = ({
  locale,
  pageType = "home",
  primaryKeywords = [],
  secondaryKeywords = [],
  contentCategory = "",
  entityType = "Organization",
}: Pick<SEOManagerProps, 'locale' | 'pageType' | 'primaryKeywords' | 'secondaryKeywords' | 'contentCategory' | 'entityType'>) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    // Add JSON-LD for enhanced AI understanding
    const addEnhancedStructuredData = () => {
      const structuredData = {
        "@context": "https://schema.org",
        "@graph": [
          {
            "@type": "WebPage",
            "@id": `https://innovatio-pro.com${window.location.pathname}#webpage`,
            url: `https://innovatio-pro.com${window.location.pathname}`,
            name: document.title,
            description: document
              .querySelector('meta[name="description"]')
              ?.getAttribute("content"),
            inLanguage: locale,
            isPartOf: {
              "@id": "https://innovatio-pro.com#website",
            },
            primaryImageOfPage: {
              "@id": `https://innovatio-pro.com${window.location.pathname}#primaryimage`,
            },
            breadcrumb: {
              "@id": `https://innovatio-pro.com${window.location.pathname}#breadcrumb`,
            },
            mainEntity: {
              "@id": `https://innovatio-pro.com${window.location.pathname}#main-entity`,
            },
          },
          {
            "@type": "WebSite",
            "@id": "https://innovatio-pro.com#website",
            url: "https://innovatio-pro.com",
            name: "Innovatio",
            description:
              "Leading digital solutions company specializing in web and mobile application development",
            inLanguage: locale,
            potentialAction: {
              "@type": "SearchAction",
              target: {
                "@type": "EntryPoint",
                urlTemplate:
                  "https://innovatio-pro.com/search?q={search_term_string}",
              },
              "query-input": "required name=search_term_string",
            },
          },
          {
            "@type": entityType,
            "@id": `https://innovatio-pro.com${window.location.pathname}#main-entity`,
            ...(entityType === "Organization" && {
              name: locale === 'de' ? "Innovatio - App Entwickler Deutschland" : "Innovatio Digital Solutions",
              alternateName: locale === 'de' ? "Viktor Hermann - Mobile App Entwicklung" : "Innovatio Pro",
              description: locale === 'de' ? 
                "Professionelle Mobile App Entwicklung in Deutschland. Spezialisiert auf Flutter Entwicklung für iOS und Android Apps für deutsche Unternehmen." :
                "Professional digital transformation company specializing in Flutter mobile app development",
              url: "https://innovatio-pro.com",
              logo: "https://innovatio-pro.com/images/logo.png",
              image: locale === 'de' ? 
                "https://innovatio-pro.com/images/app-entwickler-deutschland.jpg" :
                "https://innovatio-pro.com/images/digital-solutions.jpg",
              sameAs: [
                "https://linkedin.com/company/innovatio",
                "https://github.com/innovatio",
                "https://twitter.com/innovatio_pro"
              ],
              // German market specific data
              ...(locale === 'de' && {
                areaServed: [
                  {
                    "@type": "Country",
                    "name": "Deutschland",
                    "sameAs": "https://en.wikipedia.org/wiki/Germany"
                  },
                  {
                    "@type": "City", 
                    "name": "Berlin"
                  },
                  {
                    "@type": "City",
                    "name": "München"
                  },
                  {
                    "@type": "City",
                    "name": "Hamburg"
                  },
                  {
                    "@type": "City",
                    "name": "Köln"
                  }
                ],
                address: {
                  "@type": "PostalAddress",
                  "addressCountry": "DE",
                  "addressRegion": "Deutschland"
                },
                keywords: "App Entwickler Deutschland, Mobile App Entwicklung, iOS Entwicklung, Android Entwicklung, Flutter Apps, Flutter Entwicklung, Viktor Hermann",
                priceRange: "€€-€€€",
                currenciesAccepted: "EUR",
                paymentAccepted: "Rechnung, Überweisung, PayPal",
              }),
              contactPoint: {
                "@type": "ContactPoint",
                contactType: "customer service",
                availableLanguage: locale === 'de' ? 
                  ["German", "English"] : 
                  ["English", "German",
                  "Russian",
                  "Turkish",
                  "Arabic",
                ],
              },
              areaServed: "Worldwide",
              serviceArea: {
                "@type": "Place",
                name: "Global",
              },
              knowsAbout: primaryKeywords.concat(secondaryKeywords),
            }),
            ...(entityType === "Service" && {
              name: primaryKeywords[0] || "Digital Solutions",
              description: `Professional ${contentCategory} services`,
              provider: {
                "@type": "Organization",
                name: "Innovatio",
              },
              areaServed: "Worldwide",
              category: contentCategory,
              serviceType: contentCategory,
            }),
          },
        ],
      };

      const script = document.createElement("script");
      script.type = "application/ld+json";
      script.textContent = JSON.stringify(structuredData);
      script.id = "ai-enhanced-structured-data";

      // Remove existing script if present
      const existingScript = document.getElementById(
        "ai-enhanced-structured-data"
      );
      if (existingScript) {
        existingScript.remove();
      }

      document.head.appendChild(script);
    };

    // Add meta tags for AI content classification
    const addAIMetaTags = () => {
      const metaTags = [
        { name: "ai:content-category", content: contentCategory },
        { name: "ai:entity-type", content: entityType },
        { name: "ai:primary-keywords", content: primaryKeywords.join(", ") },
        {
          name: "ai:secondary-keywords",
          content: secondaryKeywords.join(", "),
        },
        { name: "ai:page-type", content: pageType },
        { name: "ai:content-language", content: locale },
        { name: "ai:content-intent", content: getContentIntent(pageType) },
        { name: "ai:target-audience", content: getTargetAudience(pageType) },
      ];

      metaTags.forEach(({ name, content }) => {
        if (content) {
          const existingMeta = document.querySelector(`meta[name="${name}"]`);
          if (existingMeta) {
            existingMeta.setAttribute("content", content);
          } else {
            const meta = document.createElement("meta");
            meta.name = name;
            meta.content = content;
            document.head.appendChild(meta);
          }
        }
      });
    };

    // Run optimizations
    addEnhancedStructuredData();
    addAIMetaTags();
  }, [
    isMounted,
    locale,
    pageType,
    primaryKeywords,
    secondaryKeywords,
    contentCategory,
    entityType,
  ]);

  return null;
};

// Helper functions
function getContentIntent(pageType: string): string {
  const intentMap: Record<string, string> = {
    home: "brand-awareness",
    about: "informational",
    services: "commercial",
    blog: "educational",
    contact: "transactional",
    product: "commercial",
  };
  return intentMap[pageType] || "informational";
}

function getTargetAudience(pageType: string): string {
  const audienceMap: Record<string, string> = {
    home: "business-owners, entrepreneurs, developers",
    about: "potential-clients, partners",
    services: "business-decision-makers",
    blog: "developers, tech-enthusiasts",
    contact: "potential-clients",
    product: "business-owners, project-managers",
  };
  return audienceMap[pageType] || "general-audience";
}

// Utility function to create structured data script
export function createStructuredData(data: object): string {
  return JSON.stringify(data, null, 0);
}

// Common structured data generators
export const structuredDataGenerators = {
  website: (locale: string) => ({
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Innovatio",
    url: DEFAULT_DOMAIN,
    description:
      "Professional website templates and custom web development services",
    inLanguage: locale,
    publisher: {
      "@type": "Organization",
      name: "Innovatio",
      url: DEFAULT_DOMAIN,
    },
  }),

  organization: () => ({
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "Innovatio",
    url: DEFAULT_DOMAIN,
    logo: `${DEFAULT_DOMAIN}/images/logo.png`,
    sameAs: [
      "https://twitter.com/innovatio",
      "https://linkedin.com/company/innovatio",
      "https://github.com/innovatio",
    ],
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "******-0123",
      contactType: "customer service",
      availableLanguage: ["English", "German", "Russian", "Turkish", "Arabic"],
    },
  }),

  breadcrumb: (items: Array<{ name: string; url: string }>) => ({
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }),

  faq: (questions: Array<{ question: string; answer: string }>) => ({
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: questions.map(({ question, answer }) => ({
      "@type": "Question",
      name: question,
      acceptedAnswer: {
        "@type": "Answer",
        text: answer,
      },
    })),
  }),
};

export default SEOAIOptimizer;