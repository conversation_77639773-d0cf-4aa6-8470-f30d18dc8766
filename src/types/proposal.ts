// Types für das Proposal & Contract System - Innovatio-Pro

// Additional hourly service interface
export interface AdditionalHourlyService {
    id: string;
    name: string;
    description: string;
    hourlyRate: number;
    estimatedHours: number;
    totalPrice: number;
    category: 'development' | 'design' | 'consulting' | 'testing' | 'maintenance' | 'other';
}

// Price modification interface
export interface PriceModification {
    id: string;
    type: 'increase' | 'decrease' | 'fixed';
    amount: number;
    reason: string;
    modifiedBy: string;
    modifiedAt: string;
    originalPrice: number;
    newPrice: number;
}

// Detailed price breakdown interface
export interface PriceBreakdown {
    baseServicePrice: number;
    additionalServices: AdditionalHourlyService[];
    priceModifications: PriceModification[];
    subtotal: number;
    tax?: number;
    totalPrice: number;
    currency: string;
}

export interface CustomerProposal {
    // Basic Information
    clientId: string;
    name: string;
    email: string;
    companyName?: string;
    phone?: string;

    // Address Information
    street?: string;        // Maps to "Street" in Notion
    houseNumber?: string;   // Maps to "HouseNumber" in Notion
    city?: string;          // Maps to "City" in Notion
    postalCode?: string;    // Maps to "PostalCode" in Notion
    country?: string;       // Maps to "Country" in Notion

    // URLs and Security
    proposalUrl: string;
    contractUrl: string;
    urlPassword: string;

    // Service Information
    interestedService: string; // Maps to "InterestedService" in Notion
    servicePrice: number; // Maps to "ServicePrice" in Notion
    estimatedBudget?: number; // Maps to "EstimatedBudget" in Notion
    projectTimeline?: string; // Maps to "ProjectTimeline" in Notion
    heardAbout?: string; // Maps to "Heard About" in Notion
    description?: string; // Maps to "Description" in Notion

    // Enhanced Pricing
    priceBreakdown?: PriceBreakdown;
    additionalServices?: AdditionalHourlyService[];
    priceModifications?: PriceModification[];

    // Status and Priority
    status: 'New Lead' | 'On Hold' | 'Qualified' | 'Won' | 'Lost'; // Matches Notion select options
    priority: 'Low' | 'Medium' | 'High' | 'Urgent'; // Matches Notion select options

    // Timestamps
    createdDate: string; // Maps to "Created Date" in Notion
    signatureDate?: string; // Maps to "Signature Date" in Notion

    // Notes and Comments
    notes?: string; // Maps to "Notes" in Notion
    internalComments?: string; // Maps to "Internal Comments" in Notion

    // Signature Data (AWS S3 URL)
    signatureUrl?: string; // Maps to "SignatureURL" in Notion

    // Additional fields for internal use
    selectedAddOns?: string[];
    totalPrice?: number;
    locale?: string;
}

// Service Packages für innovatio-pro
export interface ServicePackage {
    id: string;
    name: string;
    description: string;
    basePrice?: number;
    price?: number; // Keep for backward compatibility
    originalPrice?: number;
    timeframe: string;
    category: string;
    features: string[];
    isHourly?: boolean;
}

// Add-ons für innovatio-pro Services
export interface AddOn {
    id: string;
    name: string;
    description: string;
    price: number;
    category: 'design' | 'technical' | 'support' | 'integration';
    compatibleServices: string[];
}

// Proposal Creation Request
export interface CreateProposalRequest {
    clientId: string;
    name: string;
    email: string;
    companyName?: string;
    phone?: string;

    // Address Information
    street?: string;
    houseNumber?: string;
    city?: string;
    postalCode?: string;
    country?: string;

    interestedService: string;
    estimatedBudget?: number;
    projectTimeline?: string;
    heardAbout?: string;
    description?: string;
    selectedAddOns?: string[];
    notes?: string;
    additionalServices?: AdditionalHourlyService[];
    priceBreakdown?: PriceBreakdown;
}

// Proposal Acceptance Request
export interface AcceptProposalRequest {
    signature: string;           // Base64 encoded
    addOns: string[];
    legalConsents: {
        agb: boolean;
        dsgvo: boolean;
        widerruf: boolean;
    };
    finalNotes?: string;
}

// Admin Auth Request
export interface AdminAuthRequest {
    password: string;
}

// Password Verification Request
export interface VerifyPasswordRequest {
    clientId: string;
    password: string;
}

// Contract Completion Request
export interface CompleteContractRequest {
    clientId: string;
    signatureData: string;      // Base64 encoded
    agreements: {
        agb: boolean;
        dsgvo: boolean;
        widerruf: boolean;
    };
    finalComments?: string;
}

// Lead Update Request
export interface UpdateLeadRequest {
    status?: string;
    addOns?: string[];
    comments?: string;
    contractUrl?: string;
    signature?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}

// Enhanced Contact Form Data (für das angepasste Contact Form)
export interface EnhancedContactFormData {
    name: string;
    email: string;
    companyName?: string;
    phone?: string;
    selectedService?: string;
    estimatedBudget?: string;
    projectTimeline?: string;
    message: string;
}

// Request interfaces for API endpoints
export interface UpdateProposalRequest {
    clientId: string;
    companyName?: string;
    phone?: string;

    // Address Information
    street?: string;
    houseNumber?: string;
    city?: string;
    postalCode?: string;
    country?: string;

    interestedService?: string;
    selectedAddOns?: string[];
    estimatedBudget?: number;
    projectTimeline?: string;
    heardAbout?: string;
    description?: string;
    notes?: string;
    internalComments?: string;
    status?: CustomerProposal['status'];
    priority?: CustomerProposal['priority'];
    additionalServices?: AdditionalHourlyService[];
    priceModifications?: PriceModification[];
    priceBreakdown?: PriceBreakdown;
}

export interface ContractSubmissionRequest {
    clientId: string;
    signature: string;
    termsAccepted: boolean;
    privacyAccepted: boolean;
    gdprAccepted: boolean;
}

export interface ProposalResponse {
    success: boolean;
    data?: CustomerProposal;
    error?: string;
}

// Price modification request
export interface PriceModificationRequest {
    clientId: string;
    modificationType: 'increase' | 'decrease' | 'fixed';
    amount: number;
    reason: string;
    modifiedBy: string;
}

// Additional service request
export interface AdditionalServiceRequest {
    clientId: string;
    service: AdditionalHourlyService;
    action: 'add' | 'remove' | 'update';
} 