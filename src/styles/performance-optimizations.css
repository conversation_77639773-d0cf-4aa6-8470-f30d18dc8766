/* Performance Optimizations for Anti-Flickering */



/* Reduce animation complexity on low-end devices */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}



/* Enhanced Performance Optimizations for Animations */

/* GPU acceleration for all animated elements */
.animate-scroll,
.animate-scroll-horizontal,
[data-framer-motion] {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Faster scroll-based animations */
@keyframes scroll {
  0% {
    transform: translateX(0) translateZ(0);
  }
  100% {
    transform: translateX(calc(-50%)) translateZ(0);
  }
}

@keyframes scroll-horizontal {
  0% {
    transform: translateX(0) translateZ(0);
  }
  100% {
    transform: translateX(calc(-100% - 2rem)) translateZ(0);
  }
}

.animate-scroll {
  animation: scroll 15s linear infinite;
}

.animate-scroll-horizontal {
  animation: scroll-horizontal 20s linear infinite;
}

/* Pause animations on hover for better UX */
.animate-scroll:hover,
.animate-scroll-horizontal:hover {
  animation-play-state: paused;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .animate-scroll,
  .animate-scroll-horizontal,
  [data-framer-motion] {
    animation: none !important;
    transition: none !important;
    will-change: auto;
  }
}

/* Optimize for mobile performance */
@media (max-width: 768px) {
  .animate-scroll {
    animation-duration: 12s;
  }
  
  .animate-scroll-horizontal {
    animation-duration: 15s;
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
} 