/* Critical CSS for above-the-fold content */
/* Only includes styles needed for initial render and LCP optimization */

/* Hero container optimization for LCP */
.hero-container {
  contain: layout style paint;
  transform: translateZ(0);
  will-change: transform;
  isolation: isolate;
}



/* Prevent layout shifts in hero mockup */
.hero-image {
  content-visibility: auto;
  contain-intrinsic-size: 320px 568px;
}

@media (min-width: 640px) {
  .hero-image {
    contain-intrinsic-size: 384px 580px;
  }
}

@media (min-width: 1024px) {
  .hero-image {
    contain-intrinsic-size: 448px 640px;
  }
}

/* Mockup container aspect ratio for layout stability */
.mockup-container {
  aspect-ratio: 7/12; /* iPhone-like ratio */
  width: 100%;
  max-width: 448px;
}





/* Performance optimization for animations */
@media (prefers-reduced-motion: reduce) {
  .hero-mockup *,
  .hero-container *,
  [data-motion] {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    transform: none !important;
  }
}

/* Optimize images for immediate visibility */
img[data-priority="true"] {
  content-visibility: auto;
  contain-intrinsic-size: 320px 568px;
  image-rendering: -webkit-optimize-contrast;
}

