import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';

const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const CRM_DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '';

if (!CRM_DATABASE_ID) {
    console.warn('NOTION_CRM_DATABASE_ID not set in environment variables');
}

// Service packages matching the contact form options exactly
const SERVICE_PACKAGES: { [key: string]: any } = {
    prototype: {
        id: 'prototype',
        name: 'Rapid Prototype',
        price: 3000,
        originalPrice: 3500,
        timeframe: '1-2 Wochen',
        description: 'Wir liefern hochinteraktive Clickable Prototypen, UI/UX und Product Visuals that let you present your product vision before development costs.',
        features: [
            'Interactive clickable prototypes',
            'UX/UI design workshops',
            'Feature prioritization sessions',
            'MVP roadmap preparation',
            'Investor pitch materials',
            'Clean code architecture planning',
            'Always in contact for feedback'
        ],
        trustIndicators: ['🔬 Foundation Builder', '✨ Interactive', '📊 Professional MVP prep']
    },
    mvp: {
        id: 'mvp',
        name: 'MVP-Entwicklung',
        price: 7500,
        originalPrice: 9000,
        timeframe: 'wenige Wochen',
        description: 'Wir bauen Ihre full production-ready Flutter App, inklusive backend setup, security layers, scalable architecture, AI-ready pathways and deployment — all guided toward full product success.',
        features: [
            'Full Flutter app development',
            'Firebase backend setup',
            'Scalable architecture design',
            'AI integration pathways',
            'Authentication & security layers',
            'Clean code & best practices',
            'Always in contact during development',
            'Comprehensive testing & debugging'
        ],
        trustIndicators: ['🚀 Most Popular', '📱 Production-ready', '🔧 Complete']
    },
    'saas-growth': {
        id: 'saas-growth',
        name: 'SaaS Growth Package',
        price: 15000,
        originalPrice: 18000,
        timeframe: 'wenige Wochen',
        description: 'Wir erweitern your MVP into a fully scalable SaaS platform with cloud-native architecture, advanced backend optimization, admin panels, and multi-tenant infrastructure.',
        features: [
            'Flutter app expansion & enhancement',
            'Backend optimization (Firebase/Supabase)',
            'Admin panel integration',
            'SaaS-grade cloud infrastructure',
            'Full SaaS architecture consulting',
            'Clean code refactoring & optimization',
            'Always in contact for scaling decisions',
            'Performance monitoring & optimization'
        ],
        trustIndicators: ['📈 Scale Ready', '🔒 Enterprise-grade', '⚡ Growth-Focused']
    },
    consulting: {
        id: 'consulting',
        name: 'Technical Consulting',
        price: 150,
        originalPrice: 200,
        timeframe: 'Ongoing',
        description: 'CTO-level strategy and technical leadership at a fraction of the cost',
        features: [
            'CTO-level strategy at 80% less cost',
            'Technology roadmaps & reviews',
            'Direct expert decision-making',
            'Architecture design & optimization',
            'Team mentoring & guidance',
            'Technical due diligence',
            'Scalability planning',
            'Best practices implementation'
        ],
        trustIndicators: ['👨‍💻 Expert CTO-level', '⚡ Hourly Available', '🎓 Mentoring Included'],
        isHourly: true
    },
    custom: {
        id: 'custom',
        name: 'Individuelle Anfrage',
        price: 0,
        originalPrice: 0,
        timeframe: 'Nach Absprache',
        description: 'Maßgeschneiderte Lösung für Ihre spezifischen Anforderungen',
        features: [
            'Individuelle Projektanalyse',
            'Maßgeschneiderte Lösungsarchitektur',
            'Flexible Technologie-Auswahl',
            'Angepasste Entwicklungsprozesse',
            'Persönliche Beratung & Planung',
            'Skalierbare Projektstruktur',
            'Kontinuierliche Kommunikation',
            'Qualitätssicherung & Testing'
        ],
        trustIndicators: ['🎯 Custom-Made', '🔧 Flexible', '📋 Consultation-Based']
    }
};

export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ clientId: string }> }
) {
    try {
        const { clientId } = await params;

        if (!CRM_DATABASE_ID) {
            console.error('NOTION_CRM_DATABASE_ID is not configured');
            return NextResponse.json(
                { error: 'Database configuration missing' },
                { status: 500 }
            );
        }

        // Query the database for an entry with this Client ID
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            return NextResponse.json(
                { error: 'Proposal not found' },
                { status: 404 }
            );
        }

        // Get the first matching page
        const page = response.results[0];

        // Extract properties from the Notion page
        const properties = (page as any).properties;

        // Map budget properly - check both EstimatedBudget (number) and ServicePrice (number)
        let budgetValue = 'above30k'; // default to above30k since most projects are enterprise-level

        // First try to get budget from EstimatedBudget field
        if (properties['EstimatedBudget']?.number) {
            const budget = properties['EstimatedBudget'].number;
            if (budget < 5000) budgetValue = 'below5k';
            else if (budget >= 5000 && budget < 15000) budgetValue = '5to15k';
            else if (budget >= 15000 && budget < 30000) budgetValue = '15to30k';
            else budgetValue = 'above30k';
        }
        // If no EstimatedBudget, try ServicePrice
        else if (properties['ServicePrice']?.number) {
            const budget = properties['ServicePrice'].number;
            if (budget < 5000) budgetValue = 'below5k';
            else if (budget >= 5000 && budget < 15000) budgetValue = '5to15k';
            else if (budget >= 15000 && budget < 30000) budgetValue = '15to30k';
            else budgetValue = 'above30k';
        }
        // If neither has a value, check if there's any text indication
        else {
            // Check if there's any text in the description or other fields that indicates budget
            const description = properties['Description']?.rich_text?.[0]?.text?.content || '';
            const message = properties['ProjectTimeline']?.rich_text || '';
            const combinedText = (description + ' ' + JSON.stringify(message)).toLowerCase();

            if (combinedText.includes('above30k') || combinedText.includes('30k+') || combinedText.includes('over 30')) {
                budgetValue = 'above30k';
            } else if (combinedText.includes('15to30k') || combinedText.includes('15-30k')) {
                budgetValue = '15to30k';
            } else if (combinedText.includes('5to15k') || combinedText.includes('5-15k')) {
                budgetValue = '5to15k';
            } else if (combinedText.includes('below5k') || combinedText.includes('under 5k')) {
                budgetValue = 'below5k';
            }
        }

        // Get selected service and package details
        const serviceName = properties['InterestedService']?.select?.name;

        // Debug: log the raw service name from Notion
        console.log('🔍 Debug: Raw service name from Notion:', serviceName);

        // Clean mapping only for the 5 services we actually offer
        const serviceNameToId: { [key: string]: string } = {
            // Service IDs from contact form (primary)
            'prototype': 'prototype',
            'mvp': 'mvp',
            'saas-growth': 'saas-growth',
            'consulting': 'consulting',
            'custom': 'custom',
            // Service names (for backward compatibility)
            'Rapid Prototype': 'prototype',
            'MVP Development': 'mvp',
            'MVP-Entwicklung': 'mvp',
            'SaaS Growth Package': 'saas-growth',
            'Technical Consulting': 'consulting',
            'Individuelle Anfrage': 'custom',
            'Custom Request': 'custom'
        };

        const selectedServiceId = serviceNameToId[serviceName || ''] || 'mvp';
        const selectedPackage = SERVICE_PACKAGES[selectedServiceId];

        // Debug: log the mapping result
        console.log('🔍 Debug: Service mapping result:', {
            input: serviceName,
            mapped: selectedServiceId,
            packageFound: !!selectedPackage,
            packageName: selectedPackage?.name
        });

        // Ensure we have a valid package
        if (!selectedPackage) {
            console.warn(`Service not found: ${serviceName}, falling back to prototype`);
            const fallbackPackage = SERVICE_PACKAGES['prototype'];

            const proposalData = {
                clientName: properties['Name']?.title?.[0]?.text?.content || 'Unknown Client',
                clientEmail: properties['Email']?.email || '',
                company: properties['CompanyName']?.rich_text?.[0]?.text?.content || '',
                phone: properties['Phone']?.phone_number || '',
                
                // Address Information
                street: properties['Street']?.rich_text?.[0]?.text?.content || '',
                houseNumber: properties['HouseNumber']?.number?.toString() || '',
                city: properties['City']?.rich_text?.[0]?.text?.content || '',
                postalCode: properties['PostalCode']?.number?.toString() || '',
                country: properties['Country']?.rich_text?.[0]?.text?.content || '',
                
                selectedService: 'prototype',
                selectedPackage: fallbackPackage,
                estimatedBudget: budgetValue,
                projectTimeline: properties['ProjectTimeline']?.rich_text?.[0]?.text?.content || 'flexible',
                message: properties['Description']?.rich_text?.[0]?.text?.content || '',
                heardAbout: properties['HeardAbout']?.rich_text?.[0]?.text?.content || 'website',
                changeRequests: properties['ChangeRequest']?.rich_text?.[0]?.text?.content || '',
                changeRequestDate: properties['ChangeRequestDate']?.date?.start || null,
                status: properties['Status']?.select?.name || 'New Lead',
                urlPassword: properties['URLPassword']?.rich_text?.[0]?.text?.content || '',
                signature1: properties['Signature 1']?.rich_text?.[0]?.text?.content || '',
                signature2: properties['Signature 2']?.rich_text?.[0]?.text?.content || '',
                signature3: properties['Signature 3']?.rich_text?.[0]?.text?.content || '',
                signatureDate: properties['Signature Date']?.date?.start || null
            };

            return NextResponse.json(proposalData);
        }

        const proposalData = {
            clientName: properties['Name']?.title?.[0]?.text?.content || 'Unknown Client',
            clientEmail: properties['Email']?.email || '',
            company: properties['CompanyName']?.rich_text?.[0]?.text?.content || '',
            phone: properties['Phone']?.phone_number || '',
            
            // Address Information
            street: properties['Street']?.rich_text?.[0]?.text?.content || '',
            houseNumber: properties['HouseNumber']?.number?.toString() || '',
            city: properties['City']?.rich_text?.[0]?.text?.content || '',
            postalCode: properties['PostalCode']?.number?.toString() || '',
            country: properties['Country']?.rich_text?.[0]?.text?.content || '',
            
            selectedService: selectedServiceId,
            selectedPackage: selectedPackage,
            estimatedBudget: budgetValue,
            projectTimeline: properties['ProjectTimeline']?.rich_text?.[0]?.text?.content || 'flexible',
            message: properties['Description']?.rich_text?.[0]?.text?.content || '',
            heardAbout: properties['HeardAbout']?.rich_text?.[0]?.text?.content || 'website',
            changeRequests: properties['ChangeRequest']?.rich_text?.[0]?.text?.content || '',
            changeRequestDate: properties['ChangeRequestDate']?.date?.start || null,
            status: properties['Status']?.select?.name || 'New Lead',
            urlPassword: properties['URLPassword']?.rich_text?.[0]?.text?.content || '',
            signature1: properties['Signature 1']?.rich_text?.[0]?.text?.content || '',
            signature2: properties['Signature 2']?.rich_text?.[0]?.text?.content || '',
            signature3: properties['Signature 3']?.rich_text?.[0]?.text?.content || '',
            signatureDate: properties['Signature Date']?.date?.start || null
        };

        console.log('Retrieved proposal data from Notion:', {
            clientId,
            rawServiceName: serviceName,
            selectedServiceId,
            packageName: selectedPackage.name,
            serviceMapping: serviceNameToId,
            allProperties: Object.keys(properties).filter(key => key.includes('Service') || key.includes('Interest'))
        });

        return NextResponse.json(proposalData);

    } catch (error) {
        console.error('Error fetching proposal data:', error);
        return NextResponse.json(
            { error: 'Failed to fetch proposal data' },
            { status: 404 }
        );
    }
}