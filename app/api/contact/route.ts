import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { Resend } from 'resend';
import { createLeadFromContact } from '@/lib/notion';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.json();
    const { name, email, subject, message } = formData;

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    console.log('=== Environment Variables Check ===');
    console.log('SMTP_HOST:', process.env.SMTP_HOST);
    console.log('SMTP_PORT:', process.env.SMTP_PORT);
    console.log('SMTP_USER:', process.env.SMTP_USER);
    console.log('SMTP_PASS is set:', !!process.env.SMTP_PASS);
    console.log('RESEND_API_KEY is set:', !!process.env.RESEND_API_KEY);
    console.log('NOTION_TOKEN is set:', !!process.env.NOTION_TOKEN);
    console.log('NOTION_CRM_DATABASE_ID is set:', !!process.env.NOTION_CRM_DATABASE_ID);
    console.log('=====================================');

    // Try Resend API first (no SMTP passwords needed!)
    let emailSent = false;
    let lastError: any;

    // Try Resend API first - much simpler and more reliable
    if (process.env.RESEND_API_KEY) {
      try {
        console.log('\n=== Using Resend API (Recommended) ===');

        const resend = new Resend(process.env.RESEND_API_KEY);

        const data = await resend.emails.send({
          from: 'Contact Form <<EMAIL>>',
          to: ['<EMAIL>'],
          replyTo: email,
          subject: `New Contact Form Message: ${subject}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #3B82F6;">New Contact Form Submission</h2>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Name:</strong> ${name}</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Subject:</strong> ${subject}</p>
              </div>
              
              <div style="background: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h3 style="color: #495057; margin-top: 0;">Message:</h3>
                <p style="line-height: 1.6; color: #6c757d;">${message.replace(/\n/g, '<br>')}</p>
              </div>
              
              <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 8px;">
                <p style="margin: 0; color: #0066cc;">
                  <strong>Reply to:</strong> <a href="mailto:${email}">${email}</a>
                </p>
              </div>
              
              <hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">
              <p style="color: #6c757d; font-size: 12px; text-align: center;">
                This email was sent from the contact form on innovatio-pro.com via Resend
              </p>
            </div>
          `
        });

        console.log('✅ Resend Email sent successfully!');
        emailSent = true;

        // After successful email, create lead in Notion CRM
        let notionLead = null;
        try {
          console.log('\n=== Creating Notion CRM Lead ===');
          // Convert budget range to numeric value for ServicePrice field
          let budgetNumeric = 0;
          if (formData.estimatedBudget) {
            switch (formData.estimatedBudget) {
              case 'below5k':
                budgetNumeric = 3000;
                break;
              case '5to15k':
                budgetNumeric = 10000;
                break;
              case '15to30k':
                budgetNumeric = 22500;
                break;
              case 'above30k':
                budgetNumeric = 40000;
                break;
              default:
                budgetNumeric = 0;
            }
          }

          notionLead = await createLeadFromContact({
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            company: formData.companyName,
            message: formData.message,
            selectedService: formData.selectedService,
            estimatedBudget: formData.estimatedBudget,
            estimatedBudgetNumeric: budgetNumeric,
            projectTimeline: formData.projectTimeline,
            additionalServices: [],
            source: formData.heardAbout || 'Contact Form',
          });
          console.log('✅ Notion CRM lead created successfully:', notionLead.clientId);
        } catch (notionError) {
          console.error('❌ Notion CRM failed (but email was sent successfully):', notionError);
        }

        return NextResponse.json({
          message: 'Email sent successfully via Resend',
          messageId: data.data?.id || 'resend-email-sent',
          method: 'Resend',
          leadId: notionLead?.clientId || null,
          proposalUrl: notionLead?.proposalUrl || null
        }, { status: 200 });

      } catch (error: any) {
        console.error('❌ Resend failed:', error?.message);
        lastError = error;
      }
    }

    // Fallback to SMTP only if Resend is not configured
    if (!emailSent && !process.env.RESEND_API_KEY) {
      try {
        console.log('\n=== Fallback: Using SMTP (Gmail) ===');
        console.log('⚠️  Note: SMTP requires email credentials. Consider using Resend API instead.');

        const transporter = nodemailer.createTransport({
          host: 'smtp.gmail.com',
          port: 587,
          secure: false,
          auth: {
            user: process.env.SMTP_USER || '<EMAIL>',
            pass: process.env.SMTP_PASS || 'your-gmail-app-password'
          },
          tls: { rejectUnauthorized: false }
        });

        const mailOptions = {
          from: `"Contact Form" <${process.env.SMTP_USER || '<EMAIL>'}>`,
          to: '<EMAIL>',
          replyTo: email,
          subject: `New Contact Form Message: ${subject}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #3B82F6;">New Contact Form Submission</h2>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Name:</strong> ${name}</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Subject:</strong> ${subject}</p>
              </div>
              
              <div style="background: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h3 style="color: #495057; margin-top: 0;">Message:</h3>
                <p style="line-height: 1.6; color: #6c757d;">${message.replace(/\n/g, '<br>')}</p>
              </div>
              
              <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 8px;">
                <p style="margin: 0; color: #0066cc;">
                  <strong>Reply to:</strong> <a href="mailto:${email}">${email}</a>
                </p>
              </div>
              
              <hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">
              <p style="color: #6c757d; font-size: 12px; text-align: center;">
                This email was sent from the contact form on innovatio-pro.com via SMTP
              </p>
            </div>
          `
        };

        const info = await transporter.sendMail(mailOptions);
        console.log('✅ SMTP Email sent successfully!');
        emailSent = true;

        // After successful email, create lead in Notion CRM
        let notionLead = null;
        try {
          console.log('\n=== Creating Notion CRM Lead ===');
          // Convert budget range to numeric value for ServicePrice field
          let budgetNumeric = 0;
          if (formData.estimatedBudget) {
            switch (formData.estimatedBudget) {
              case 'below5k':
                budgetNumeric = 3000;
                break;
              case '5to15k':
                budgetNumeric = 10000;
                break;
              case '15to30k':
                budgetNumeric = 22500;
                break;
              case 'above30k':
                budgetNumeric = 40000;
                break;
              default:
                budgetNumeric = 0;
            }
          }

          notionLead = await createLeadFromContact({
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            company: formData.companyName,
            message: formData.message,
            selectedService: formData.selectedService,
            estimatedBudget: formData.estimatedBudget,
            estimatedBudgetNumeric: budgetNumeric,
            projectTimeline: formData.projectTimeline,
            additionalServices: [],
            source: formData.heardAbout || 'Contact Form',
          });
          console.log('✅ Notion CRM lead created successfully:', notionLead.clientId);
        } catch (notionError) {
          console.error('❌ Notion CRM failed (but email was sent successfully):', notionError);
        }

        return NextResponse.json({
          message: 'Email sent successfully via SMTP',
          messageId: info.messageId,
          method: 'SMTP',
          leadId: notionLead?.clientId || null,
          proposalUrl: notionLead?.proposalUrl || null
        }, { status: 200 });

      } catch (error: any) {
        console.error('❌ SMTP failed:', error?.message);
        lastError = error;
      }
    }

    // If both methods failed, throw the last error
    if (!emailSent) {
      throw lastError || new Error('No email service available');
    }

  } catch (error: any) {
    console.error('💥 All email methods failed:', error);

    let errorMessage = 'Failed to send email';
    let errorDetails = error?.message || 'Unknown error';

    if (error?.code === 'EAUTH') {
      errorMessage = 'Authentication failed. SMTP credentials may be incorrect.';
      errorDetails = 'Please verify your email credentials in the hosting control panel.';
    } else if (error?.code === 'ECONNECTION' || error?.code === 'ECONNREFUSED') {
      errorMessage = 'Connection failed. SMTP server may be unreachable.';
      errorDetails = 'The SMTP server is not accepting connections. This could be due to firewall restrictions or server issues.';
    }

    return NextResponse.json({
      error: errorMessage,
      details: errorDetails,
      suggestions: [
        'Check SMTP server configuration',
        'Verify firewall settings allow SMTP connections',
        'Consider using Resend by adding RESEND_API_KEY to environment variables',
        'Contact your hosting provider for SMTP support'
      ]
    }, { status: 500 });
  }
} 