import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
    try {
        const { email } = await request.json();

        if (!email || !email.includes('@')) {
            return NextResponse.json(
                { error: 'Invalid email address' },
                { status: 400 }
            );
        }

        // Check if Resend is configured
        if (!process.env.RESEND_API_KEY || !process.env.RESEND_AUDIENCE_ID) {
            console.error('Resend API key or audience ID not configured');
            return NextResponse.json(
                { error: 'Newsletter service not configured' },
                { status: 500 }
            );
        }

        // Remove from newsletter list
        try {
            await resend.contacts.remove({
                email,
                audienceId: process.env.RESEND_AUDIENCE_ID,
            });
        } catch (resendError: any) {
            console.error('Resend unsubscribe error:', resendError);

            // If contact doesn't exist, that's also success
            if (resendError?.message?.includes('not found')) {
                return NextResponse.json({
                    success: true,
                    message: 'Email was not subscribed or already unsubscribed'
                });
            }

            return NextResponse.json(
                { error: 'Failed to unsubscribe from newsletter' },
                { status: 500 }
            );
        }

        // Send goodbye email (optional)
        try {
            await resend.emails.send({
                from: 'Innovatio Tech Insights <<EMAIL>>',
                to: email,
                subject: 'Newsletter-Abmeldung bestätigt',
                html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #152660; margin-bottom: 10px;">Abmeldung bestätigt</h1>
                        <p style="color: #666; font-size: 18px;">Sie wurden erfolgreich von unserem Newsletter abgemeldet</p>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
                        <h2 style="margin: 0 0 15px 0; color: #152660;">Schade, dass Sie gehen!</h2>
                        <p style="margin: 0; font-size: 16px; color: #666;">Wir respektieren Ihre Entscheidung. Sie können sich jederzeit wieder anmelden.</p>
                    </div>
                    
                    <div style="text-align: center; margin-bottom: 30px;">
                        <p style="color: #666; margin-bottom: 20px;">Möchten Sie doch wieder dabei sein?</p>
                        <a href="https://innovatio-pro.com/blog#subscription" style="background: linear-gradient(135deg, #152660, #1e3a8a); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
                            Wieder anmelden
                        </a>
                    </div>
                    
                    <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #666; font-size: 14px;">
                        <p style="margin: 0;">
                            <strong>Innovatio</strong><br>
                            Innovative Mobile Solutions<br>
                            <a href="https://innovatio-pro.com" style="color: #152660;">innovatio-pro.com</a>
                        </p>
                    </div>
                </div>
                `,
            });
        } catch (emailError) {
            console.error('Error sending goodbye email:', emailError);
            // Don't fail the unsubscribe if email fails
        }

        return NextResponse.json({
            success: true,
            message: 'Successfully unsubscribed from newsletter'
        });

    } catch (error) {
        console.error('Newsletter unsubscribe error:', error);
        return NextResponse.json(
            { error: 'Failed to unsubscribe from newsletter' },
            { status: 500 }
        );
    }
}

export async function GET(request: NextRequest) {
    // Handle unsubscribe via GET request (for email links)
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
        return NextResponse.redirect('/blog?unsubscribe_error=missing_email');
    }

    try {
        // Remove from newsletter list
        if (process.env.RESEND_API_KEY && process.env.RESEND_AUDIENCE_ID) {
            await resend.contacts.remove({
                email,
                audienceId: process.env.RESEND_AUDIENCE_ID,
            });
        }

        // Redirect to success page
        return NextResponse.redirect('/blog?unsubscribe_success=true');
    } catch (error) {
        console.error('Newsletter unsubscribe error:', error);
        return NextResponse.redirect('/blog?unsubscribe_error=failed');
    }
} 