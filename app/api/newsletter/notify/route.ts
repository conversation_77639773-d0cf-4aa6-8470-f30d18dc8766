import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';
import { blogService } from '@/lib/blog-service';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
    try {
        const { postSlug, locale = 'de' } = await request.json();

        // Validate API key for security
        const authHeader = request.headers.get('authorization');
        const expectedToken = process.env.NEWSLETTER_API_SECRET || 'your-secret-token';

        if (authHeader !== `Bearer ${expectedToken}`) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        if (!postSlug) {
            return NextResponse.json(
                { error: 'Post slug is required' },
                { status: 400 }
            );
        }

        // Get the blog post
        const post = blogService.getPostBySlug(postSlug, locale);
        if (!post) {
            return NextResponse.json(
                { error: 'Post not found' },
                { status: 404 }
            );
        }

        // Check if Resend is configured
        if (!process.env.RESEND_API_KEY || !process.env.RESEND_AUDIENCE_ID) {
            console.error('Resend API key or audience ID not configured');
            return NextResponse.json(
                { error: 'Newsletter service not configured' },
                { status: 500 }
            );
        }

        // Get all subscribers from the audience
        let subscribers: string[] = [];
        try {
            const audienceContacts = await resend.contacts.list({
                audienceId: process.env.RESEND_AUDIENCE_ID,
            });

            if (audienceContacts.data?.data) {
                subscribers = audienceContacts.data.data.map((contact: any) => contact.email);
            }
        } catch (error) {
            console.error('Error fetching subscribers:', error);
            return NextResponse.json(
                { error: 'Failed to fetch subscribers' },
                { status: 500 }
            );
        }

        if (subscribers.length === 0) {
            return NextResponse.json({
                success: true,
                message: 'No subscribers to notify',
                subscriberCount: 0
            });
        }

        // Generate email content
        const postUrl = `https://innovatio-pro.com/${locale}/blog/${post.slug}`;
        const unsubscribeUrl = `https://innovatio-pro.com/${locale}/newsletter/unsubscribe`;

        const emailHtml = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <!-- Header -->
                <div style="text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #152660;">
                    <h1 style="color: #152660; margin: 0; font-size: 28px;">Innovatio Tech Insights</h1>
                    <p style="color: #666; margin: 5px 0 0 0;">Neuer Artikel verfügbar!</p>
                </div>

                <!-- Featured Image -->
                <div style="margin-bottom: 30px; text-align: center;">
                    <img src="${post.featuredImage}" alt="${post.title}" style="max-width: 100%; height: 200px; object-fit: cover; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                </div>

                <!-- Article Content -->
                <div style="background: linear-gradient(135deg, #152660, #1e3a8a); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px;">
                    <h2 style="margin: 0 0 15px 0; font-size: 24px; line-height: 1.3;">${post.title}</h2>
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.6; opacity: 0.9;">${post.excerpt}</p>
                    
                    <!-- Meta Info -->
                    <div style="display: flex; gap: 20px; margin-bottom: 25px; font-size: 14px; opacity: 0.8;">
                        <span>📚 ${post.category.charAt(0).toUpperCase() + post.category.slice(1)}</span>
                        <span>⏱️ ${post.readingTime} Min Lesezeit</span>
                        <span>👤 ${post.author.name}</span>
                    </div>

                    <!-- CTA Button -->
                    <div style="text-align: center;">
                        <a href="${postUrl}" style="background: white; color: #152660; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
                            Artikel lesen →
                        </a>
                    </div>
                </div>

                <!-- Tags -->
                ${post.tags && post.tags.length > 0 ? `
                <div style="margin-bottom: 30px;">
                    <h3 style="color: #152660; margin-bottom: 15px; font-size: 16px;">Tags:</h3>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                        ${post.tags.map(tag => `
                            <span style="background: #e3f2fd; color: #1565c0; padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: 500;">${tag}</span>
                        `).join('')}
                    </div>
                </div>
                ` : ''}

                <!-- More Articles -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                    <h3 style="color: #152660; margin: 0 0 15px 0;">Mehr Tech-Insights entdecken</h3>
                    <p style="color: #666; margin: 0 0 15px 0;">Bleiben Sie auf dem neuesten Stand der Technik!</p>
                    <a href="https://innovatio-pro.com/${locale}/blog" style="background: #152660; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
                        Alle Artikel ansehen
                    </a>
                </div>

                <!-- Footer -->
                <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #666; font-size: 14px;">
                    <p style="margin: 0 0 10px 0;">
                        <strong>Innovatio</strong><br>
                        Innovative Mobile Solutions<br>
                        <a href="https://innovatio-pro.com" style="color: #152660;">innovatio-pro.com</a>
                    </p>
                    <p style="margin: 10px 0; font-size: 12px;">
                        Sie erhalten diese E-Mail, weil Sie unseren Newsletter abonniert haben.<br>
                        <a href="${unsubscribeUrl}" style="color: #666;">Hier abmelden</a>
                    </p>
                </div>
            </div>
        `;

        const emailText = `
Neuer Artikel: ${post.title}

${post.excerpt}

Kategorie: ${post.category}
Lesezeit: ${post.readingTime} Minuten
Autor: ${post.author.name}

Lesen Sie den vollständigen Artikel hier: ${postUrl}

---
Innovatio - Innovative Mobile Solutions
${unsubscribeUrl}
        `;

        // Send newsletter to all subscribers
        let successCount = 0;
        let failCount = 0;

        // Send emails in batches to avoid rate limits
        const batchSize = 50;
        for (let i = 0; i < subscribers.length; i += batchSize) {
            const batch = subscribers.slice(i, i + batchSize);

            try {
                await resend.emails.send({
                    from: 'Innovatio Tech Insights <<EMAIL>>',
                    to: batch,
                    subject: `📱 Neuer Artikel: ${post.title}`,
                    html: emailHtml,
                    text: emailText,
                });

                successCount += batch.length;
            } catch (error) {
                console.error(`Failed to send to batch ${i / batchSize + 1}:`, error);
                failCount += batch.length;
            }

            // Add delay between batches to respect rate limits
            if (i + batchSize < subscribers.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        return NextResponse.json({
            success: true,
            message: 'Newsletter sent successfully',
            stats: {
                totalSubscribers: subscribers.length,
                successCount,
                failCount,
                postTitle: post.title,
                postUrl
            }
        });

    } catch (error) {
        console.error('Newsletter notification error:', error);
        return NextResponse.json(
            { error: 'Failed to send newsletter notification' },
            { status: 500 }
        );
    }
} 