import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';
import { uploadSignatureToNotion } from '@/lib/notion';

const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const CRM_DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '';

export async function POST(request: NextRequest) {
    try {
        const { clientId, signature, agreements } = await request.json();

        if (!CRM_DATABASE_ID) {
            console.error('NOTION_CRM_DATABASE_ID is not configured');
            return NextResponse.json(
                { error: 'Database configuration missing' },
                { status: 500 }
            );
        }

        // Find the Notion page by clientId
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            return NextResponse.json(
                { error: 'Client not found' },
                { status: 404 }
            );
        }

        const notionPage = response.results[0];
        const notionPageId = notionPage.id;

        // Upload signature to S3 and update Notion with the URL
        await uploadSignatureToNotion(clientId, signature);

        // Note: LegalAgreements property removed as it doesn't exist in the Notion database
        // The agreements data is handled by the uploadSignatureToNotion function

        return NextResponse.json({
            success: true,
            message: 'Contract concluded successfully'
        });

    } catch (error) {
        console.error('Error concluding contract:', error);
        return NextResponse.json(
            { error: 'Failed to conclude contract' },
            { status: 500 }
        );
    }
} 