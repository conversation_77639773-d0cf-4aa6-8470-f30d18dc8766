import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';

const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const CRM_DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '';

export async function POST(request: NextRequest) {
    try {
        const { clientId, password } = await request.json();

        if (!clientId || !password) {
            return NextResponse.json(
                { success: false, error: 'Client ID and password are required' },
                { status: 400 }
            );
        }

        // Validate clientId format (should be UUID)
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(clientId)) {
            return NextResponse.json(
                { success: false, error: 'Invalid client ID format' },
                { status: 400 }
            );
        }

        // Query the database for the client
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            // Don't reveal if client exists or not for security
            return NextResponse.json(
                { success: false, error: 'Invalid credentials' },
                { status: 401 }
            );
        }

        const page = response.results[0] as any;
        const properties = page.properties;

        // Get the stored password - check both possible field names
        const storedPassword = properties['URLPassword']?.rich_text?.[0]?.text?.content ||
            properties['URLPassword']?.rich_text?.[0]?.text?.content || '';

        // Use secure comparison to prevent timing attacks
        let isValid = false;
        if (storedPassword && password && storedPassword.length === password.length) {
            let matches = 0;
            for (let i = 0; i < storedPassword.length; i++) {
                if (storedPassword[i] === password[i]) {
                    matches++;
                }
            }
            isValid = matches === storedPassword.length;
        }

        if (isValid) {
            // Clear any sensitive data from logs
            console.log(`✅ Password verification success for client: ${clientId.substring(0, 8)}...`);
            return NextResponse.json({
                success: true,
                message: 'Access granted'
            }, {
                headers: {
                    // Security headers
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });
        } else {
            // Log failed attempt without revealing password
            console.log(`❌ Failed password verification for client: ${clientId.substring(0, 8)}...`);
            return NextResponse.json(
                { success: false, error: 'Invalid credentials' },
                { status: 401 }
            );
        }

    } catch (error) {
        console.error('Error verifying password:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
} 