import { NextRequest, NextResponse } from 'next/server';
import { getLeadByClientId, updateCustomerProposal } from '@/lib/notion';
import { PriceModificationRequest } from '@/types/proposal';

export async function POST(request: NextRequest) {
    try {
        const body: PriceModificationRequest = await request.json();
        const { clientId, modificationType, amount, reason, modifiedBy } = body;

        // Validate required fields
        if (!clientId || !modificationType || !amount || !reason || !modifiedBy) {
            return NextResponse.json(
                { success: false, error: 'Missing required fields' },
                { status: 400 }
            );
        }

        // Get current proposal data
        const currentProposal = await getLeadByClientId(clientId);
        if (!currentProposal) {
            return NextResponse.json(
                { success: false, error: 'Proposal not found' },
                { status: 404 }
            );
        }

        // Create new price modification
        const newModification = {
            id: Date.now().toString(),
            type: modificationType,
            amount: amount,
            reason: reason,
            modifiedBy: modifiedBy,
            modifiedAt: new Date().toISOString(),
            originalPrice: currentProposal.servicePrice || 0,
            newPrice: modificationType === 'fixed' ? amount :
                (modificationType === 'increase' ?
                    (currentProposal.servicePrice || 0) + amount :
                    (currentProposal.servicePrice || 0) - amount)
        };

        // Get existing modifications or create new array
        const existingModifications = currentProposal.priceModifications || [];
        const updatedModifications = [...existingModifications, newModification];

        // Update notes with the modification reason
        const currentNotes = currentProposal.notes || '';
        const modificationNote = `\n\n--- PREISÄNDERUNG (${new Date().toLocaleDateString('de-DE')}) ---\n${reason}\nÄnderung: ${modificationType === 'increase' ? '+' : modificationType === 'decrease' ? '-' : 'Festpreis: '}€${amount.toLocaleString('de-DE')}\nDurch: ${modifiedBy}`;
        const updatedNotes = currentNotes + modificationNote;

        // Update the proposal in Notion
        await updateCustomerProposal(clientId, {
            clientId,
            priceModifications: updatedModifications,
            notes: updatedNotes
        });

        console.log(`✅ Price modification added for ${clientId}:`, {
            type: modificationType,
            amount: amount,
            reason: reason,
            modifiedBy: modifiedBy
        });

        return NextResponse.json({
            success: true,
            message: 'Price modification added successfully',
            modification: newModification
        });

    } catch (error) {
        console.error('Error modifying price:', error);
        return NextResponse.json(
            { success: false, error: 'Failed to modify price' },
            { status: 500 }
        );
    }
} 