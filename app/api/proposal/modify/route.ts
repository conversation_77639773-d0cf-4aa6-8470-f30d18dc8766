import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';

const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '20af774e19468030b825e8bdbe58605a';

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { clientId, modifications, date, street, houseNumber, city, postalCode, country, message } = body;

        console.log('Processing request for client:', clientId);

        // Check if this is an address update request
        const isAddressUpdate = street !== undefined || houseNumber !== undefined ||
            city !== undefined || postalCode !== undefined || country !== undefined;

        // Check if this is a message/description update request
        const isMessageUpdate = message !== undefined;

        // First, find the Notion page by clientId
        const response = await notion.databases.query({
            database_id: DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            console.error('No Notion page found for clientId:', clientId);
            return NextResponse.json(
                { success: false, error: 'Client not found in database' },
                { status: 404 }
            );
        }

        const notionPage = response.results[0];
        const notionPageId = notionPage.id;

        // Build update properties based on request type
        const updateProperties: any = {};

        if (isAddressUpdate) {
            // Handle address update
            console.log('Processing address update for client:', clientId);

            if (street !== undefined) {
                updateProperties['Street'] = {
                    rich_text: street && street.toString().trim() ? [{ text: { content: street.toString().trim() } }] : []
                };
            }

            if (houseNumber !== undefined) {
                const houseNum = houseNumber && houseNumber.toString().trim() ? parseFloat(houseNumber.toString().trim()) : null;
                updateProperties['HouseNumber'] = {
                    number: !isNaN(houseNum) ? houseNum : null
                };
            }

            if (city !== undefined) {
                updateProperties['City'] = {
                    rich_text: city && city.toString().trim() ? [{ text: { content: city.toString().trim() } }] : []
                };
            }

            if (postalCode !== undefined) {
                const postalNum = postalCode && postalCode.toString().trim() ? parseFloat(postalCode.toString().trim()) : null;
                updateProperties['PostalCode'] = {
                    number: !isNaN(postalNum) ? postalNum : null
                };
            }

            if (country !== undefined) {
                updateProperties['Country'] = {
                    rich_text: country && country.toString().trim() ? [{ text: { content: country.toString().trim() } }] : []
                };
            }
        } else if (isMessageUpdate) {
            // Handle message/description update
            console.log('Processing message update for client:', clientId);

            updateProperties['Description'] = {
                rich_text: message ? [{ text: { content: message } }] : []
            };
        } else {
            // Handle modification request
            console.log('Processing modification request for client:', clientId);

            // Format modification request with timestamp
            const newChangeRequest = `[${new Date(date).toLocaleDateString('de-DE')} ${new Date(date).toLocaleTimeString('de-DE')}]\n${modifications}`;

            // Get existing change requests first
            let existingChangeRequests = '';
            try {
                const properties = (notionPage as any).properties;
                existingChangeRequests = properties['ChangeRequest']?.rich_text?.[0]?.text?.content || '';
            } catch (error) {
                console.log('Could not retrieve existing change requests:', error);
            }

            // Append new change request with divider
            const allChangeRequests = existingChangeRequests
                ? `${existingChangeRequests}\n\n--- NEUE ÄNDERUNG ---\n\n${newChangeRequest}`
                : newChangeRequest;

            updateProperties['Status'] = {
                select: {
                    name: 'ChangeRequest'
                }
            };

            updateProperties['ChangeRequest'] = {
                rich_text: [
                    {
                        type: 'text',
                        text: {
                            content: allChangeRequests
                        }
                    }
                ]
            };

            updateProperties['ChangeRequestDate'] = {
                date: {
                    start: new Date(date).toISOString()
                }
            };
        }

        // Update Notion with the properties
        try {
            await notion.pages.update({
                page_id: notionPageId,
                properties: updateProperties,
            });

            console.log(`Notion updated successfully for page: ${notionPageId}`);
        } catch (notionError) {
            console.error('Error updating Notion:', notionError);
            return NextResponse.json(
                {
                    success: false,
                    error: 'Failed to update Notion page. Please check if the page exists and is accessible.'
                },
                { status: 404 }
            );
        }

        // Send notification email only for modification requests (not address or message updates)
        if (!isAddressUpdate && !isMessageUpdate && modifications) {
            try {
                const notificationResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/send-modification-notification`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        clientId,
                        modifications,
                        date,
                    }),
                });

                if (!notificationResponse.ok) {
                    console.error('Failed to send modification notification email');
                }
            } catch (emailError) {
                console.error('Error sending notification email:', emailError);
            // Continue even if email fails
            }
        }

        return NextResponse.json({
            success: true,
            message: isAddressUpdate ? 'Address updated successfully' :
                isMessageUpdate ? 'Project description updated successfully' :
                    'Modification request submitted successfully'
        });

    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(
            {
                success: false,
                error: 'Failed to process request'
            },
            { status: 500 }
        );
    }
} 