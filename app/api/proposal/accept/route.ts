import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';

// Helper function to get properly formatted base URL
function getFormattedBaseUrl(): string {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    // For localhost or already formatted URLs, return as is
    if (baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1')) {
        return baseUrl;
    }

    // For production URLs, ensure www subdomain is included
    if (!baseUrl.startsWith('http')) {
        return `https://www.${baseUrl}`;
    } else if (baseUrl.includes('innovatio-pro.com') && !baseUrl.includes('www.')) {
        return baseUrl.replace('https://innovatio-pro.com', 'https://www.innovatio-pro.com');
    }

    return baseUrl;
}

const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '20af774e19468030b825e8bdbe58605a';

export async function POST(request: NextRequest) {
    try {
        const { clientId } = await request.json();

        console.log('Processing proposal acceptance for client:', clientId);

        // First, find the Notion page by clientId
        const response = await notion.databases.query({
            database_id: DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            console.error('No Notion page found for clientId:', clientId);
            return NextResponse.json(
                { success: false, error: 'Client not found in database' },
                { status: 404 }
            );
        }

        const notionPage = response.results[0];
        const notionPageId = notionPage.id;

        // Get URLPassword from the Notion page
        const properties = (notionPage as any).properties;
        const urlPassword = properties['URLPassword']?.rich_text?.[0]?.text?.content || '';

        // Update Notion status to "Qualified"
        try {
            await notion.pages.update({
                page_id: notionPageId,
                properties: {
                    'Status': {
                        select: {
                            name: 'Qualified'
                        }
                    },
                    'ProposalURL': {
                        url: `${getFormattedBaseUrl()}/de/proposal/${clientId}`
                    },
                    'ContractURL': {
                        url: `${getFormattedBaseUrl()}/de/contract/${clientId}`
                    }
                },
            });

            console.log('Notion updated successfully for page:', notionPageId);
        } catch (notionError) {
            console.error('Error updating Notion:', notionError);
            return NextResponse.json(
                {
                    success: false,
                    error: 'Failed to update Notion page. Please check if the page exists and is accessible.'
                },
                { status: 404 }
            );
        }

        // Send contract email
        try {
            const finalBaseUrl = getFormattedBaseUrl();

            const emailResponse = await fetch(`${finalBaseUrl}/api/send-contract-email`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    clientId,
                    contractUrl: `${finalBaseUrl}/de/contract/${clientId}`,
                }),
            });

            if (!emailResponse.ok) {
                console.error('Failed to send contract email');
            }
        } catch (emailError) {
            console.error('Error sending contract email:', emailError);
        }

        return NextResponse.json({
            success: true,
            message: 'Proposal accepted successfully'
        });

    } catch (error) {
        console.error('Error processing proposal acceptance:', error);
        return NextResponse.json(
            {
                success: false,
                error: 'Failed to process proposal acceptance'
            },
            { status: 500 }
        );
    }
} 