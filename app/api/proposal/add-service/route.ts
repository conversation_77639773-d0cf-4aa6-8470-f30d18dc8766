import { NextRequest, NextResponse } from 'next/server';
import { getLeadByClientId, updateCustomerProposal } from '@/lib/notion';
import { AdditionalServiceRequest } from '@/types/proposal';

export async function POST(request: NextRequest) {
    try {
        const body: AdditionalServiceRequest = await request.json();
        const { clientId, service, action } = body;

        // Validate required fields
        if (!clientId || !service || !action) {
            return NextResponse.json(
                { success: false, error: 'Missing required fields' },
                { status: 400 }
            );
        }

        // Get current proposal data
        const currentProposal = await getLeadByClientId(clientId);
        if (!currentProposal) {
            return NextResponse.json(
                { success: false, error: 'Proposal not found' },
                { status: 404 }
            );
        }

        // Get existing services or create new array
        const existingServices = currentProposal.additionalServices || [];
        let updatedServices = [...existingServices];
        let actionMessage = '';

        switch (action) {
            case 'add':
                // Generate unique ID for new service
                const newService = {
                    ...service,
                    id: Date.now().toString(),
                    totalPrice: service.hourlyRate * service.estimatedHours
                };
                updatedServices.push(newService);
                actionMessage = `Service "${service.name}" hinzugefügt`;
                break;

            case 'remove':
                updatedServices = updatedServices.filter(s => s.id !== service.id);
                actionMessage = `Service "${service.name}" entfernt`;
                break;

            case 'update':
                const serviceIndex = updatedServices.findIndex(s => s.id === service.id);
                if (serviceIndex >= 0) {
                    updatedServices[serviceIndex] = {
                        ...service,
                        totalPrice: service.hourlyRate * service.estimatedHours
                    };
                    actionMessage = `Service "${service.name}" aktualisiert`;
                }
                break;

            default:
                return NextResponse.json(
                    { success: false, error: 'Invalid action' },
                    { status: 400 }
                );
        }

        // Update notes with the service action
        const currentNotes = currentProposal.notes || '';
        const serviceNote = `\n\n--- SERVICE ${action.toUpperCase()} (${new Date().toLocaleDateString('de-DE')}) ---\n${actionMessage}\nDetails: ${service.description}\nPreis: €${(service.hourlyRate * service.estimatedHours).toLocaleString('de-DE')} (${service.estimatedHours}h × €${service.hourlyRate}/h)`;
        const updatedNotes = currentNotes + serviceNote;

        // Update the proposal in Notion
        await updateCustomerProposal(clientId, {
            clientId,
            additionalServices: updatedServices,
            notes: updatedNotes
        });

        console.log(`✅ Service ${action} for ${clientId}:`, {
            serviceName: service.name,
            action: action,
            totalPrice: service.hourlyRate * service.estimatedHours
        });

        return NextResponse.json({
            success: true,
            message: actionMessage,
            services: updatedServices
        });

    } catch (error) {
        console.error('Error managing service:', error);
        return NextResponse.json(
            { success: false, error: 'Failed to manage service' },
            { status: 500 }
        );
    }
} 