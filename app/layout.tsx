import './globals.css'
import '@/styles/critical.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { ThemeProvider } from '@/providers/ThemeProvider'
import { AccessibilityProvider } from '@/providers/AccessibilityProvider'
import { AccessibilityMenu } from '@/components/ui/AccessibilityMenu'
import { CookieConsent } from '@/components/ui/CookieConsent'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'
import GoogleAnalytics from "@/components/GoogleAnalytics";
import PerformanceOptimizer from "@/components/performance/PerformanceOptimizer";
import ClientOnly from "@/components/ui/ClientOnly";

const inter = Inter({ 
  subsets: ["latin", "cyrillic"],
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  title: "Innovatio - Web & Mobile Digital Solutions Company",
  description:
    "We develop cutting-edge mobile and web applications that transform businesses through innovative technology solutions, optimized UX design, and AI integration.",
  keywords:
    "digital solutions, web development, mobile apps, technology innovation, business transformation, software development, next.js development, flutter development, AI integration, UX/UI design",
  metadataBase: new URL("https://innovatio-pro.com"),
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-video-preview": -1,
      "max-snippet": -1,
    },
  },
  openGraph: {
    title: "Innovatio - Web & Mobile Digital Solutions Company",
    description:
      "We develop cutting-edge mobile and web applications that transform businesses through innovative technology solutions, optimized UX design, and AI integration.",
    url: "https://innovatio-pro.com",
    siteName: "Innovatio",
    locale: "en_US",
    type: "website",
    images: [
      {
        url: "https://innovatio-pro.com/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Innovatio - Digital Solutions Company",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Innovatio - Web & Mobile Digital Solutions Company",
    description:
      "We develop cutting-edge mobile and web applications that transform businesses through innovative technology solutions, optimized UX design, and AI integration.",
    images: ["https://innovatio-pro.com/twitter-image.jpg"],
  },
  verification: {
    google: "HD_upcBFgzyK6gtVbiyIyOEavU90TidQk320Qt0HeOY", // Replace with your actual verification code when you have it
  },
};

// The lang attribute will be set by the locale layout based on URL path
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      className="h-full overflow-auto overflow-x-hidden"
      suppressHydrationWarning
    >
      <body className={`${inter.className} h-full `}>
        {/* Skip Link für bessere Barrierefreiheit - direkt zum Hauptinhalt springen */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-white dark:bg-gray-800 text-primary dark:text-[#8fa3c7] px-4 py-2 rounded-md shadow-md focus:outline-none"
          tabIndex={0}
        >
          Zum Hauptinhalt springen
        </a>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          <AccessibilityProvider>
            <PerformanceOptimizer />
            {children}
            <AccessibilityMenu />
            <CookieConsent />
            <ClientOnly>
              <Analytics />
              <SpeedInsights />
              {process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID && (
                <GoogleAnalytics
                  GA_MEASUREMENT_ID={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}
                />
              )}
            </ClientOnly>
          </AccessibilityProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
