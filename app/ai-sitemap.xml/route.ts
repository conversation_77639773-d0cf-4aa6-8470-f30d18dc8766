import { locales } from '../../middleware'

export async function GET() {
  const baseUrl = 'https://innovatio-pro.com'
  
  // Generate additional locale URLs
  const additionalLocaleUrls = locales
    .filter(locale => locale !== 'de' && locale !== 'en')
    .map(locale => `
  <url>
    <loc>${baseUrl}/${locale}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`).join('')
  
  // AI-optimized sitemap with enhanced metadata for AI crawlers
  const aiSitemapXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
  <!-- Main German landing page for AI indexing -->
  <url>
    <loc>${baseUrl}/de</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
    <news:news>
      <news:publication>
        <news:name>Innovatio Blog</news:name>
        <news:language>de</news:language>
      </news:publication>
      <news:genres>Technology, Software Development</news:genres>
      <news:keywords>App Entwickler Deutschland, Mobile App Entwicklung, iOS Android Entwicklung</news:keywords>
    </news:news>
  </url>

  <!-- German Services Page -->
  <url>
    <loc>${baseUrl}/de/services</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>

  <!-- German About Page -->
  <url>
    <loc>${baseUrl}/de/about</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- German Contact Page -->
  <url>
    <loc>${baseUrl}/de/contact</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- German FAQ Page -->
  <url>
    <loc>${baseUrl}/de/faq</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>

  <!-- Blog Overview (German) -->
  <url>
    <loc>${baseUrl}/de/blog</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- Regional German pages for local SEO -->
  <url>
    <loc>${baseUrl}/de/berlin</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>

  <url>
    <loc>${baseUrl}/de/muenchen</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>

  <url>
    <loc>${baseUrl}/de/hamburg</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>

  <url>
    <loc>${baseUrl}/de/koeln</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>

  <!-- English main pages for international reach -->
  <url>
    <loc>${baseUrl}/en</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>

  <url>
    <loc>${baseUrl}/en/services</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- Additional locale-specific pages -->
  ${additionalLocaleUrls}
</urlset>`;

  return new Response(aiSitemapXml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 's-maxage=86400, stale-while-revalidate',
    },
  })
}