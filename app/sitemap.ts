import { MetadataRoute } from 'next'
import { locales } from '../middleware'
import { blogService } from '@/lib/blog-service'

// Define the last modified dates for main pages
const pageLastModified = {
  home: new Date('2025-01-15'),
  about: new Date('2025-01-15'),
  services: new Date('2025-01-15'),
  contact: new Date('2025-01-15'),
  portfolio: new Date('2025-01-15'),
  blog: new Date('2025-01-15'),
  faq: new Date('2025-01-15'),
}

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://innovatio-pro.com'
  
  const routes: MetadataRoute.Sitemap = []

  // Add root URL (redirect to default locale)
  routes.push({
    url: baseUrl,
    lastModified: pageLastModified.home,
    changeFrequency: 'daily',
    priority: 1,
  })

  // Generate URLs for each locale
  locales.forEach(locale => {
    // Main pages for each locale
    const mainPages = [
      { path: '', lastMod: pageLastModified.home, priority: 1, freq: 'daily' as const },
      { path: '/about', lastMod: pageLastModified.about, priority: 0.9, freq: 'monthly' as const },
      { path: '/services', lastMod: pageLastModified.services, priority: 0.9, freq: 'monthly' as const },
      { path: '/contact', lastMod: pageLastModified.contact, priority: 0.8, freq: 'monthly' as const },
      { path: '/faq', lastMod: pageLastModified.faq, priority: 0.7, freq: 'monthly' as const },
      { path: '/blog', lastMod: pageLastModified.blog, priority: 0.9, freq: 'weekly' as const },
    ]

    // Add regional pages for German market
    if (locale === 'de') {
      const regionalPages = [
        { path: '/berlin', lastMod: new Date('2025-01-15'), priority: 0.9, freq: 'monthly' as const },
        { path: '/muenchen', lastMod: new Date('2025-01-15'), priority: 0.9, freq: 'monthly' as const },
        { path: '/hamburg', lastMod: new Date('2025-01-15'), priority: 0.9, freq: 'monthly' as const },
        { path: '/koeln', lastMod: new Date('2025-01-15'), priority: 0.9, freq: 'monthly' as const },
      ]
      mainPages.push(...regionalPages)
    }

    mainPages.forEach(page => {
      routes.push({
        url: `${baseUrl}/${locale}${page.path}`,
        lastModified: page.lastMod,
        changeFrequency: page.freq,
        priority: page.priority,
      })
    })

    // Add blog posts for each locale
    try {
      const blogPosts = blogService.getAllPosts(locale)
      blogPosts.forEach(post => {
        routes.push({
          url: `${baseUrl}/${locale}/blog/${post.slug}`,
          lastModified: new Date(post.publishedAt),
          changeFrequency: 'monthly',
          priority: 0.7,
        })
      })
    } catch (error) {
      console.warn(`Failed to get blog posts for locale ${locale}:`, error)
    }
  })

  return routes
}
