@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* RTL Support */
[dir="rtl"] .rtl-layout {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .rtl-section {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .rtl-layout .ml-1,
[dir="rtl"] .rtl-layout .ml-2,
[dir="rtl"] .rtl-layout .ml-3,
[dir="rtl"] .rtl-layout .ml-4 {
  margin-left: 0;
  margin-right: 0.25rem;
  margin-right: 0.5rem;
  margin-right: 0.75rem;
  margin-right: 1rem;
}

[dir="rtl"] .rtl-layout .mr-1,
[dir="rtl"] .rtl-layout .mr-2,
[dir="rtl"] .rtl-layout .mr-3,
[dir="rtl"] .rtl-layout .mr-4 {
  margin-right: 0;
  margin-left: 0.25rem;
  margin-left: 0.5rem;
  margin-left: 0.75rem;
  margin-left: 1rem;
}

[dir="rtl"] .rtl-layout .pl-1,
[dir="rtl"] .rtl-layout .pl-2,
[dir="rtl"] .rtl-layout .pl-3,
[dir="rtl"] .rtl-layout .pl-4 {
  padding-left: 0;
  padding-right: 0.25rem;
  padding-right: 0.5rem;
  padding-right: 0.75rem;
  padding-right: 1rem;
}

[dir="rtl"] .rtl-layout .pr-1,
[dir="rtl"] .rtl-layout .pr-2,
[dir="rtl"] .rtl-layout .pr-3,
[dir="rtl"] .rtl-layout .pr-4 {
  padding-right: 0;
  padding-left: 0.25rem;
  padding-left: 0.5rem;
  padding-left: 0.75rem;
  padding-left: 1rem;
}

/* Flip icons in RTL mode */
[dir="rtl"] .rtl-icon {
  transform: scaleX(-1);
}

/* Infinite horizontal scroll animation for company logos */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-33.333%);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  /* Smooth scrolling for anchor links */
  html {
    scroll-behavior: smooth;
  }
  
  /* Respect reduced motion preference */
  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }
  }
}

/* Custom styles for testimonials section */
.mask-fade-edges {
  position: relative;
}

.mask-fade-edges::before,
.mask-fade-edges::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 10;
  pointer-events: none;
}

.mask-fade-edges::before {
  left: 0;
  width: 2rem;
  background: linear-gradient(to right, rgba(249, 250, 251, 1) 0%, rgba(249, 250, 251, 0) 100%);
}

.mask-fade-edges::after {
  right: 0;
  width: 5rem;
  background: linear-gradient(to left, rgba(249, 250, 251, 1) 0%, rgba(249, 250, 251, 0) 100%);
}

.dark .mask-fade-edges::before {
  width: 2rem;
  background: linear-gradient(to right, rgba(17, 24, 39, 1) 0%, rgba(17, 24, 39, 0) 100%);
}

.dark .mask-fade-edges::after {
  width: 5rem;
  background: linear-gradient(to left, rgba(17, 24, 39, 1) 0%, rgba(17, 24, 39, 0) 100%);
}

/* Hide scrollbar for testimonials slider */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Shimmer animation for service details view */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2.5s infinite;
}

/* Horizontal scrolling animation */
@keyframes scroll-horizontal {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll-horizontal {
  animation: scroll-horizontal 20s linear infinite;
}

/* Accessibility Styles */

/* High Contrast Mode */
.high-contrast {
  --background: #000000;
  --foreground: #ffffff;
  --primary: #ffff00;
  --primary-foreground: #000000;
  --secondary: #ffffff;
  --secondary-foreground: #000000;
  --muted: #000000;
  --muted-foreground: #ffffff;
  --accent: #ffff00;
  --accent-foreground: #000000;
  --border: #ffffff;
  --input: #ffffff;
}

.high-contrast a {
  color: #ffff00 !important;
  text-decoration: underline !important;
}

.high-contrast button {
  border: 2px solid #ffffff !important;
}

.high-contrast img {
  filter: grayscale(100%) contrast(120%) !important;
}

/* Reduced Motion */
.reduced-motion * {
  animation-duration: 0.001ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.001ms !important;
  scroll-behavior: auto !important;
}

/* Enhanced Focus Styles */
*:focus-visible {
  outline: 3px solid var(--primary) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5) !important;
}

/* Keyboard Navigation Styles */
.keyboard-user *:focus {
  outline: 3px solid var(--primary) !important;
  outline-offset: 2px !important;
}

/* Skip Link Enhancement */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--primary);
  color: var(--primary-foreground);
  padding: 8px;
  z-index: 100;
  transition: top 0.2s;
}

.skip-link:focus {
  top: 0;
}

/* Accessibility Menu Styles */
.accessibility-menu {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Text-to-Speech Highlight */
.tts-highlight {
  background-color: rgba(59, 130, 246, 0.2);
  border-radius: 2px;
}
