import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import { getDictionary } from "@/lib/dictionaries";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  
  return {
    title: dictionary.seo?.meta?.pricing?.title || "Pricing - Innovatio Pro",
    description: dictionary.seo?.meta?.pricing?.description || "Transparent pricing for mobile app development",
    keywords: dictionary.seo?.meta?.pricing?.keywords || "app development cost, mobile app pricing",
    alternates: {
      canonical: `https://innovatio-pro.com/${locale}/pricing`
    }
  }
}

export default async function PricingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  
  redirect(`/${locale}#pricing`);
}