import { notFound } from "next/navigation";
import { getDictionary } from "@/lib/dictionaries";
import { BlogPostContent } from "@/components/blog/BlogPostContent";
import { BlogPostHeader } from "@/components/blog/BlogPostHeader";
import { BlogPostSidebar } from "@/components/blog/BlogPostSidebar";
import { blogService } from "@/lib/blog-service";

interface BlogPostPageProps {
  params: Promise<{ locale: string; slug: string }>;
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { locale, slug } = await params;
  const dict = await getDictionary(locale);

  // Find the blog post by slug using the blog service
  const post = blogService.getPostBySlug(slug, locale);

  if (!post) {
    notFound();
  }

  return (
    <main className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Blog Post Header */}
      <BlogPostHeader post={post} locale={locale} />

      {/* Blog Post Content */}
      <div className="max-w-7xl mx-auto px-4 py-8 sm:py-12 lg:py-16">
        <div className="grid lg:grid-cols-4 gap-6 sm:gap-8 lg:gap-12">
          {/* Main Content */}
          <div className="lg:col-span-3 order-2 lg:order-1">
            <BlogPostContent post={post} locale={locale} />
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 order-1 lg:order-2">
            <BlogPostSidebar
              dictionary={dict.blog}
              currentPost={post}
              relatedPosts={blogService.getRelatedPosts(post.id, 3, locale)}
              locale={locale}
            />
          </div>
        </div>
      </div>
    </main>
  );
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}) {
  const { locale, slug } = await params;

  const post = blogService.getPostBySlug(slug, locale);

  if (!post) {
    return {
      title: "Post Not Found | Innovatio",
      description: "The requested blog post could not be found.",
    };
  }

  return {
    title: `${post.title} | Innovatio Blog`,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: "article",
      publishedTime: post.publishedAt,
      authors: [post.author.name],
      images: [
        {
          url: post.featuredImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: [post.featuredImage],
    },
  };
}
