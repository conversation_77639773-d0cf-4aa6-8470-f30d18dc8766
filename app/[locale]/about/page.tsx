import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import { getDictionary } from "@/lib/dictionaries";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  
  return {
    title: dictionary.seo?.meta?.about?.title || "About - Innovatio Pro",
    description: dictionary.seo?.meta?.about?.description || "Learn about our mobile app development expertise",
    keywords: dictionary.seo?.meta?.about?.keywords || "app developer, mobile development, about us",
    alternates: {
      canonical: `https://innovatio-pro.com/${locale}/about`
    }
  }
}

export default async function AboutPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  
  redirect(`/${locale}#about`);
}