import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import { getDictionary } from "@/lib/dictionaries";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  
  return {
    title: dictionary.seo?.meta?.contact?.title || "Contact - Innovatio Pro",
    description: dictionary.seo?.meta?.contact?.description || "Contact us for mobile app development services",
    keywords: dictionary.seo?.meta?.contact?.keywords || "contact app developer, mobile development inquiry",
    alternates: {
      canonical: `https://innovatio-pro.com/${locale}/contact`
    }
  }
}

export default async function ContactPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  
  redirect(`/${locale}#contact`);
}