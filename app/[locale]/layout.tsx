import { locales } from "../../middleware";
import { I18nProvider } from "@/providers/I18nProvider";
import { SimpleHeader } from "@/components/layout/SimpleHeader";
import OrganizationJsonLd from "./JsonLd";
import FAQJsonLd from "./FaqJsonLd"; // Import the FAQ structured data component
import LocalBusinessJsonLd from "./LocalBusinessJsonLd"; // Import the Local Business schema
import { createPageMetadata } from "@/lib/seo";
import { getDictionary } from "../../src/lib/dictionaries";
import { Metadata } from "next";
import Head from "next/head";

// Generate metadata for each locale
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  // Enhanced metadata with AI optimization
  const metadata = createPageMetadata("home", locale, dictionary, {
    // Additional AI-friendly meta properties
    openGraph: {
      type: "website",
      title: "Innovatio - Digital Solutions",
      description: "Leading digital transformation company",
      url: `https://innovatio-pro.com${locale === "en" ? "" : `/${locale}`}`,
      image: `https://innovatio-pro.com/images/og-${locale}.jpg`,
    },
  });

  return {
    ...metadata,
  };
}

// Generate static params for all locales
export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // The locale is handled by the I18nProvider
  // which reads it from the URL path
  const { locale } = await params;

  return (
    <>
      <OrganizationJsonLd />
      <FAQJsonLd />
      <LocalBusinessJsonLd locale={locale} />
      <I18nProvider>
        <SimpleHeader />
        <main id="main-content">{children}</main>
      </I18nProvider>
    </>
  );
}
