import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import { getDictionary } from "@/lib/dictionaries";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  
  return {
    title: dictionary.seo?.meta?.services?.title || "Services - Innovatio Pro",
    description: dictionary.seo?.meta?.services?.description || "Professional mobile app development services",
    keywords: dictionary.seo?.meta?.services?.keywords || "mobile app development, ios, android, flutter",
    alternates: {
      canonical: `https://innovatio-pro.com/${locale}/services`
    }
  }
}

export default async function ServicesPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  
  redirect(`/${locale}#services`);
}