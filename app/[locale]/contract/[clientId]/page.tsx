"use client";

import { useEffect, useState, useRef } from "react";
import { PasswordProtected } from "@/components/ui/PasswordProtected";
import SignatureCanvas from "react-signature-canvas";
import {
  Check,
  Clock,
  Info,
  Lock,
  Star,
  CheckCircle,
  Shield,
  Award,
  Calculator,
  Package,
  User,
  Mail,
  Building,
  Phone,
  MessageCircle,
  Plus,
  Trash2,
  DollarSign,
} from "lucide-react";
import {
  AdditionalHourlyService,
  PriceModification,
  PriceBreakdown,
} from "@/types/proposal";

interface ProposalData {
  clientName: string;
  clientEmail: string;
  company?: string;
  phone?: string;

  // Address Information
  street?: string;
  houseNumber?: string;
  city?: string;
  postalCode?: string;
  country?: string;

  selectedService: string;
  selectedPackage?: {
    id: string;
    name: string;
    price: number;
    originalPrice?: number;
    timeframe: string;
    description: string;
    features: string[];
    trustIndicators: string[];
  };
  estimatedBudget: string;
  projectTimeline: string;
  message: string;
  heardAbout?: string;
  changeRequests?: string;
  changeRequestDate?: string | null;
  status?: string;
  urlPassword?: string;
  signatureUrl?: string;
  signatureDate?: string | null;
  notes?: string;
  additionalServices?: AdditionalHourlyService[];
  priceModifications?: PriceModification[];
  priceBreakdown?: PriceBreakdown;
}

interface ContractPageProps {
  params: Promise<{
    clientId: string;
    locale: string;
  }>;
}

export default function ContractPage({ params }: ContractPageProps) {
  const [clientId, setClientId] = useState<string>("");
  const [proposalData, setProposalData] = useState<ProposalData | null>(null);
  const [loading, setLoading] = useState(true);

  // Resolve params async
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setClientId(resolvedParams.clientId);
    };
    resolveParams();
  }, [params]);

  // Load proposal data when clientId is available
  useEffect(() => {
    if (!clientId) return;

    const fetchProposalData = async () => {
      try {
        const response = await fetch(`/api/get-proposal-data/${clientId}`);
        if (response.ok) {
          const data = await response.json();
          setProposalData(data);
        } else {
          console.error("Failed to load proposal data:", response.status);
        }
      } catch (error) {
        console.error("Error fetching proposal data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProposalData();
  }, [clientId]);

  // Calculate total price including all modifications and additional services
  const calculateTotalPrice = () => {
    if (!proposalData?.selectedPackage) return 0;

    const basePrice = proposalData.selectedPackage.price || 0;
    const additionalServicesTotal = (
      proposalData?.additionalServices || []
    ).reduce((sum, service) => sum + service.totalPrice, 0);
    const modificationsTotal = (proposalData?.priceModifications || []).reduce(
      (sum, mod) => {
        if (mod.type === "increase") return sum + mod.amount;
        if (mod.type === "decrease") return sum - mod.amount;
        return sum; // For 'fixed' type, it replaces the base price
      },
      0
    );

    return basePrice + additionalServicesTotal + modificationsTotal;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!clientId) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <Lock className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-4">Invalid Access</h1>
          <p className="text-gray-300 mb-6">No client ID provided.</p>
        </div>
      </div>
    );
  }

  // Wrap the entire contract content in PasswordProtected
  return (
    <PasswordProtected
      clientId={clientId}
      redirectPath={`/de/contract/${clientId}`}
      title="Contract Access"
      description="Please enter the password that was provided to you to access this contract."
    >
      <ContractContent
        proposalData={proposalData}
        clientId={clientId}
        calculateTotalPrice={calculateTotalPrice}
      />
    </PasswordProtected>
  );
}

// Separate component for the actual contract content
function ContractContent({
  proposalData,
  clientId,
  calculateTotalPrice,
}: {
  proposalData: ProposalData | null;
  clientId: string;
  calculateTotalPrice: () => number;
}) {
  const [hasSignature, setHasSignature] = useState(false);
  const [agreements, setAgreements] = useState({
    agb: false,
    dsgvo: false,
    widerruf: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const signatureRef = useRef<SignatureCanvas>(null);

  // If contract is already concluded, load existing signature
  useEffect(() => {
    if (proposalData?.status === "Won" && proposalData.signatureUrl) {
      setHasSignature(true);
      setAgreements({
        agb: true,
        dsgvo: true,
        widerruf: true,
      });
    }
  }, [proposalData]);

  const handleAgreementChange = (key: keyof typeof agreements) => {
    if (proposalData?.status === "Won") return; // Don't allow changes if already concluded
    setAgreements((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const isFormValid = () => {
    return (
      agreements.agb && agreements.dsgvo && agreements.widerruf && hasSignature
    );
  };

  const clearSignature = () => {
    if (proposalData?.status === "Won") return; // Don't allow changes if already concluded
    signatureRef.current?.clear();
    setHasSignature(false);
  };

  const handleSignatureEnd = () => {
    if (proposalData?.status === "Won") return; // Don't allow changes if already concluded
    setHasSignature(!signatureRef.current?.isEmpty());
  };

  const handleConcludeContract = async () => {
    if (!isFormValid() || proposalData?.status === "Won") return;

    setIsSubmitting(true);
    try {
      const signatureDataURL = signatureRef.current?.toDataURL();
      if (!signatureDataURL) {
        alert("Please provide a signature before concluding the contract.");
        setIsSubmitting(false);
        return;
      }

      const response = await fetch("/api/contract/conclude", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId: clientId,
          signature: signatureDataURL,
          agreements: agreements,
        }),
      });

      if (response.ok) {
        setShowSuccessModal(true);
      } else {
        alert("Failed to conclude contract. Please try again.");
      }
    } catch (error) {
      console.error("Error concluding contract:", error);
      alert("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!proposalData) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Contract Not Found
          </h1>
          <p className="text-gray-300 mb-6">
            The contract data could not be loaded.
          </p>
        </div>
      </div>
    );
  }

  const getStatusInfo = (status: string) => {
    switch (status) {
      case "Won":
        return {
          text: "Vertrag abgeschlossen",
          color: "text-green-400",
          bgColor: "bg-green-500/20 border-green-500/30",
          icon: <CheckCircle className="w-4 h-4" />,
        };
      case "Qualified":
        return {
          text: "Angebot angenommen",
          color: "text-blue-400",
          bgColor: "bg-blue-500/20 border-blue-500/30",
          icon: <Star className="w-4 h-4" />,
        };
      case "ChangeRequest":
        return {
          text: "Änderung angefragt",
          color: "text-orange-400",
          bgColor: "bg-orange-500/20 border-orange-500/30",
          icon: <Clock className="w-4 h-4" />,
        };
      default:
        return {
          text: "In Bearbeitung",
          color: "text-gray-400",
          bgColor: "bg-gray-500/20 border-gray-500/30",
          icon: <Info className="w-4 h-4" />,
        };
    }
  };

  const statusInfo = getStatusInfo(proposalData.status || "");

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-gray-800 border border-blue-500 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-2">
            <div className="text-blue-500">📄</div>
            <h1 className="text-2xl font-bold text-white">
              Vertrag - {proposalData.clientName}
            </h1>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-blue-400">Contract ID: {clientId}</p>
            <div
              className={`px-3 py-1 rounded-full text-sm font-semibold border ${statusInfo.bgColor} ${statusInfo.color}`}
            >
              <div className="flex items-center gap-2">
                {statusInfo.icon}
                <span>{statusInfo.text}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Data */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <User className="text-white w-5 h-5" />
            <h2 className="text-xl font-semibold text-white">Kundendaten</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-3">
              <User className="text-gray-400 w-4 h-4" />
              <div>
                <span className="text-gray-400 text-sm">Name: </span>
                <span className="text-white">{proposalData.clientName}</span>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Mail className="text-gray-400 w-4 h-4" />
              <div>
                <span className="text-gray-400 text-sm">Email: </span>
                <span className="text-white">{proposalData.clientEmail}</span>
              </div>
            </div>
            {proposalData.company && (
              <div className="flex items-center gap-3">
                <Building className="text-gray-400 w-4 h-4" />
                <div>
                  <span className="text-gray-400 text-sm">Firma: </span>
                  <span className="text-white">{proposalData.company}</span>
                </div>
              </div>
            )}
            {proposalData.phone && (
              <div className="flex items-center gap-3">
                <Phone className="text-gray-400 w-4 h-4" />
                <div>
                  <span className="text-gray-400 text-sm">Telefon: </span>
                  <span className="text-white">{proposalData.phone}</span>
                </div>
              </div>
            )}
          </div>

          {/* Address Information */}
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Building className="text-gray-400 w-5 h-5" />
              Adressinformationen
            </h3>

            {proposalData.street ||
            proposalData.city ||
            proposalData.country ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {proposalData.street && (
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-gray-400 text-sm mb-1">Straße</div>
                    <div className="text-white font-medium">
                      {proposalData.street} {proposalData.houseNumber || ""}
                    </div>
                  </div>
                )}
                {proposalData.city && (
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-gray-400 text-sm mb-1">Stadt</div>
                    <div className="text-white font-medium">
                      {proposalData.postalCode && `${proposalData.postalCode} `}
                      {proposalData.city}
                    </div>
                  </div>
                )}
                {proposalData.country && (
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-gray-400 text-sm mb-1">Land</div>
                    <div className="text-white font-medium">
                      {proposalData.country}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-gray-700/50 border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                <Building className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-400">
                  Keine Adressinformationen verfügbar
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Project Description */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <MessageCircle className="text-white w-5 h-5" />
            <h2 className="text-xl font-semibold text-white">
              Projektbeschreibung
            </h2>
          </div>
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="text-white whitespace-pre-wrap">
              {proposalData.message || "Keine Projektbeschreibung verfügbar"}
            </div>
          </div>
        </div>

        {/* Selected Package */}
        {proposalData.selectedPackage && (
          <div className="bg-gray-800 rounded-lg p-6 mb-6">
            <div className="flex items-center gap-3 mb-6">
              <Package className="text-white w-5 h-5" />
              <h2 className="text-xl font-semibold text-white">
                Ausgewähltes Paket
              </h2>
            </div>

            <div className="bg-gradient-to-br from-blue-600 to-purple-700 rounded-lg p-6 text-white relative overflow-hidden">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4" />
                    <span className="text-blue-100">
                      {proposalData.selectedPackage.timeframe}
                    </span>
                  </div>
                  <h3 className="text-2xl font-bold mb-2">
                    {proposalData.selectedPackage.name}
                  </h3>
                </div>
                {proposalData.selectedPackage.originalPrice && (
                  <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    💾 Save{" "}
                    {Math.round(
                      ((proposalData.selectedPackage.originalPrice -
                        proposalData.selectedPackage.price) /
                        proposalData.selectedPackage.originalPrice) *
                        100
                    )}
                    %
                  </div>
                )}
              </div>

              <p className="text-blue-100 mb-6">
                {proposalData.selectedPackage.description}
              </p>

              <div className="space-y-3">
                {proposalData.selectedPackage.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span className="text-white">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Detailed Price Breakdown */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-6">
            <Calculator className="text-white w-5 h-5" />
            <h3 className="text-xl font-semibold text-white">
              Detaillierte Preisaufschlüsselung
            </h3>
          </div>

          <div className="space-y-3">
            {/* Base Package */}
            {proposalData.selectedPackage && (
              <>
                <div className="flex justify-between items-center py-3 border-b border-gray-700">
                  <div>
                    <span className="text-gray-300">
                      Grundpaket ({proposalData.selectedPackage.name})
                    </span>
                    <div className="text-xs text-gray-500">
                      {proposalData.selectedPackage.description}
                    </div>
                  </div>
                  <span className="text-white font-semibold">
                    €
                    {(
                      proposalData.selectedPackage.originalPrice ||
                      proposalData.selectedPackage.price
                    ).toLocaleString("de-DE")}
                  </span>
                </div>

                {/* Package Discount */}
                {proposalData.selectedPackage.originalPrice &&
                  proposalData.selectedPackage.originalPrice >
                    proposalData.selectedPackage.price && (
                    <div className="flex justify-between items-center py-3 border-b border-gray-700">
                      <span className="text-gray-300">
                        Paket-Rabatt (
                        {Math.round(
                          ((proposalData.selectedPackage.originalPrice -
                            proposalData.selectedPackage.price) /
                            proposalData.selectedPackage.originalPrice) *
                            100
                        )}
                        %)
                      </span>
                      <span className="text-green-400 font-semibold">
                        -€
                        {(
                          proposalData.selectedPackage.originalPrice -
                          proposalData.selectedPackage.price
                        ).toLocaleString("de-DE")}
                      </span>
                    </div>
                  )}
              </>
            )}

            {/* Additional Hourly Services */}
            {proposalData.additionalServices &&
              proposalData.additionalServices.length > 0 && (
                <div className="py-2">
                  <h4 className="text-lg font-semibold text-white mb-3">
                    Zusätzliche Services
                  </h4>
                  {proposalData.additionalServices.map((service) => (
                    <div
                      key={service.id}
                      className="flex justify-between items-center py-2 pl-4 border-l-2 border-blue-500"
                    >
                      <div>
                        <span className="text-gray-300">{service.name}</span>
                        <div className="text-xs text-gray-500">
                          {service.estimatedHours}h × €{service.hourlyRate}/h •{" "}
                          {service.category}
                        </div>
                        <div className="text-xs text-gray-400">
                          {service.description}
                        </div>
                      </div>
                      <span className="text-white font-semibold">
                        €{service.totalPrice.toLocaleString("de-DE")}
                      </span>
                    </div>
                  ))}
                </div>
              )}

            {/* Price Modifications */}
            {proposalData.priceModifications &&
              proposalData.priceModifications.length > 0 && (
                <div className="py-2">
                  <h4 className="text-lg font-semibold text-white mb-3">
                    Preisanpassungen
                  </h4>
                  {proposalData.priceModifications.map((modification) => (
                    <div
                      key={modification.id}
                      className="flex justify-between items-center py-2 pl-4 border-l-2 border-orange-500"
                    >
                      <div>
                        <span className="text-gray-300">
                          {modification.reason}
                        </span>
                        <div className="text-xs text-gray-500">
                          Angepasst am{" "}
                          {new Date(modification.modifiedAt).toLocaleDateString(
                            "de-DE"
                          )}{" "}
                          von {modification.modifiedBy}
                        </div>
                      </div>
                      <span
                        className={`font-semibold ${modification.type === "increase" ? "text-red-400" : "text-green-400"}`}
                      >
                        {modification.type === "increase" ? "+" : "-"}€
                        {modification.amount.toLocaleString("de-DE")}
                      </span>
                    </div>
                  ))}
                </div>
              )}

            {/* Subtotal */}
            <div className="flex justify-between items-center py-3 border-t border-gray-600">
              <span className="text-gray-300">Zwischensumme</span>
              <span className="text-white font-semibold">
                €
                {(proposalData.selectedPackage
                  ? proposalData.selectedPackage.price +
                    (proposalData.additionalServices?.reduce(
                      (sum, service) => sum + service.totalPrice,
                      0
                    ) || 0) +
                    (proposalData.priceModifications?.reduce((sum, mod) => {
                      if (mod.type === "increase") return sum + mod.amount;
                      if (mod.type === "decrease") return sum - mod.amount;
                      return sum;
                    }, 0) || 0)
                  : 0
                ).toLocaleString("de-DE")}
              </span>
            </div>

            {/* Total */}
            <div className="flex justify-between items-center py-4 border-t-2 border-gray-600 text-xl font-bold">
              <span className="text-white">Gesamtinvestition</span>
              <span className="text-blue-400">
                €{calculateTotalPrice().toLocaleString("de-DE")}
              </span>
            </div>
          </div>
        </div>

        {/* Project Details */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            Projektdetails
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">Budget</div>
              <div className="text-white font-semibold">
                {proposalData.estimatedBudget
                  .replace("_", " - ")
                  .replace("k", ".000€")}
              </div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">Timeline</div>
              <div className="text-white font-semibold">
                {proposalData.projectTimeline
                  .replace("_", "-")
                  .replace("months", " Monate")
                  .replace("month", " Monat")}
              </div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">Quelle</div>
              <div className="text-white font-semibold">
                {proposalData.heardAbout}
              </div>
            </div>
          </div>

          <div className="bg-gray-700 rounded-lg p-4">
            <div className="text-gray-400 text-sm mb-2">
              Projektbeschreibung
            </div>
            <div className="text-white">{proposalData.message}</div>
          </div>
        </div>

        {/* Enhanced Notes Section */}
        {proposalData.notes && (
          <div className="bg-gray-800 rounded-lg p-6 mb-6">
            <div className="flex items-center gap-3 mb-4">
              <MessageCircle className="text-white w-5 h-5" />
              <h3 className="text-xl font-semibold text-white">
                Projektnotizen & Details
              </h3>
            </div>
            <div className="bg-gray-700 rounded-lg p-6 max-h-96 overflow-y-auto">
              <div className="text-gray-300 whitespace-pre-wrap text-sm leading-relaxed">
                {proposalData.notes}
              </div>
            </div>
          </div>
        )}

        {/* Legal Agreements */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Rechtliche Bestimmungen
          </h3>

          <div className="space-y-4">
            {[
              {
                key: "agb",
                label: "Allgemeine Geschäftsbedingungen",
                required: true,
              },
              {
                key: "dsgvo",
                label: "Datenschutzgrundverordnung (DSGVO)",
                required: true,
              },
              { key: "widerruf", label: "Widerrufsrecht", required: true },
            ].map((item) => (
              <label
                key={item.key}
                className="flex items-start space-x-3 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={agreements[item.key as keyof typeof agreements]}
                  onChange={() =>
                    handleAgreementChange(item.key as keyof typeof agreements)
                  }
                  disabled={proposalData.status === "Won"}
                  className="mt-1 h-4 w-4 text-blue-600 border-gray-600 rounded focus:ring-blue-500"
                />
                <div>
                  <span className="text-white font-medium">{item.label}</span>
                  {item.required && (
                    <span className="text-red-400 ml-1">*</span>
                  )}
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Signature Section */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            Digitale Unterschrift
          </h3>

          {proposalData.status === "Won" ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
              <h4 className="text-xl font-semibold text-green-400 mb-2">
                Vertrag bereits abgeschlossen
              </h4>
              <p className="text-gray-300">
                Unterschrieben am:{" "}
                {proposalData.signatureDate
                  ? new Date(proposalData.signatureDate).toLocaleDateString(
                      "de-DE"
                    )
                  : "Unbekannt"}
              </p>
            </div>
          ) : (
            <>
              <div className="border-2 border-dashed border-gray-600 rounded-lg p-4 mb-4">
                <SignatureCanvas
                  ref={signatureRef}
                  canvasProps={{
                    width: 500,
                    height: 200,
                    className: "signature-canvas w-full bg-white rounded",
                  }}
                  onEnd={handleSignatureEnd}
                />
              </div>

              <div className="flex justify-between items-center mb-4">
                <button
                  onClick={clearSignature}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  Unterschrift löschen
                </button>
                <span className="text-sm text-gray-400">
                  Bitte unterschreiben Sie in dem Feld oben
                </span>
              </div>
            </>
          )}
        </div>

        {/* Action Buttons */}
        {proposalData.status !== "Won" && (
          <div className="flex justify-center">
            <button
              onClick={handleConcludeContract}
              disabled={!isFormValid() || isSubmitting}
              className={`px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 ${
                isFormValid() && !isSubmitting
                  ? "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105"
                  : "bg-gray-600 text-gray-400 cursor-not-allowed"
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Wird verarbeitet...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Check className="w-5 h-5" />
                  <span>Vertrag abschließen</span>
                </div>
              )}
            </button>
          </div>
        )}

        {/* Success Modal */}
        {showSuccessModal && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-xl p-8 max-w-md w-full border border-gray-600">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4">
                  Vertrag erfolgreich abgeschlossen!
                </h3>
                <p className="text-gray-300 mb-6">
                  Vielen Dank für Ihr Vertrauen. Wir werden uns umgehend bei
                  Ihnen melden.
                </p>
                <button
                  onClick={() => (window.location.href = "/")}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg transition-colors font-semibold"
                >
                  Zur Homepage
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
