"use client";

import React from 'react'
import Script from "next/script";

type LocalBusinessProps = {
  locale: string;
};

export default function LocalBusinessJsonLd({ locale }: LocalBusinessProps) {
  // German-focused business data for local SEO
  const germanBusinessData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "@id": "https://innovatio-pro.com/#localbusiness",
    "name": "Innovatio - Mobile App Entwicklung Deutschland",
    "alternateName": "<PERSON>",
    "description": "Professionelle Mobile App Entwicklung in Deutschland. Spezialisiert auf Flutter Entwicklung für iOS und Android Apps für Unternehmen, Ärzte, Handwerker und Startups.",
    "url": "https://innovatio-pro.com/de",
    "logo": {
      "@type": "ImageObject",
      "url": "https://innovatio-pro.com/logo.png",
      "width": 180,
      "height": 180
    },
    "image": [
      "https://innovatio-pro.com/images/hero-mobile-apps.jpg",
      "https://innovatio-pro.com/images/app-development-process.jpg",
      "https://innovatio-pro.com/images/portfolio-apps.jpg"
    ],
    
    // Primary German address and contact
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Hauptstraße 123", // Replace with real address
      "addressLocality": "Berlin",
      "addressRegion": "Berlin",
      "postalCode": "10115",
      "addressCountry": "DE"
    },
    
    // Contact information
    "telephone": "+49 30 12345678", // Replace with real German phone
    "email": "<EMAIL>",
    "contactPoint": [
      {
        "@type": "ContactPoint",
        "telephone": "+49 30 12345678",
        "contactType": "customer service",
        "availableLanguage": ["de", "en"],
        "areaServed": ["DE", "AT", "CH"],
        "serviceType": "Mobile App Entwicklung"
      },
      {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "customer support",
        "availableLanguage": ["de", "en"],
        "areaServed": ["DE", "AT", "CH"]
      }
    ],
    
    // Geographic service areas
    "areaServed": [
      {
        "@type": "Country",
        "name": "Deutschland",
        "sameAs": "https://en.wikipedia.org/wiki/Germany"
      },
      {
        "@type": "City",
        "name": "Berlin",
        "sameAs": "https://en.wikipedia.org/wiki/Berlin"
      },
      {
        "@type": "City", 
        "name": "München",
        "sameAs": "https://en.wikipedia.org/wiki/Munich"
      },
      {
        "@type": "City",
        "name": "Hamburg",
        "sameAs": "https://en.wikipedia.org/wiki/Hamburg"
      },
      {
        "@type": "City",
        "name": "Köln",
        "sameAs": "https://en.wikipedia.org/wiki/Cologne"
      },
      {
        "@type": "City",
        "name": "Frankfurt am Main",
        "sameAs": "https://en.wikipedia.org/wiki/Frankfurt"
      }
    ],
    
    // Business details
    "foundingDate": "2020-01-01",
    "founder": {
      "@type": "Person",
      "name": "Viktor Hermann",
      "jobTitle": "Mobile App Entwickler & Gründer",
      "url": "https://innovatio-pro.com/de/about",
      "sameAs": [
        "https://linkedin.com/in/viktor-hermann-innovatio",
        "https://github.com/viktor-hermann"
      ]
    },
    
    // Operating hours (German business hours)
    "openingHoursSpecification": [
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        "opens": "09:00",
        "closes": "18:00",
        "validFrom": "2024-01-01",
        "validThrough": "2025-12-31"
      }
    ],
    
    // Services offered
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Mobile App Entwicklung Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "iOS App Entwicklung",
            "description": "Native iOS Apps für iPhone und iPad mit Swift und Objective-C",
            "provider": {
              "@type": "LocalBusiness",
              "name": "Innovatio"
            },
            "areaServed": "DE",
            "availableLanguage": "de"
          }
        },
        {
          "@type": "Offer", 
          "itemOffered": {
            "@type": "Service",
            "name": "Android App Entwicklung",
            "description": "Native Android Apps mit Kotlin und Java für alle Android Geräte",
            "provider": {
              "@type": "LocalBusiness",
              "name": "Innovatio"
            },
            "areaServed": "DE",
            "availableLanguage": "de"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service", 
            "name": "Flutter App Entwicklung",
            "description": "Cross-platform Apps mit Flutter für iOS und Android gleichzeitig",
            "provider": {
              "@type": "LocalBusiness",
              "name": "Innovatio"
            },
            "areaServed": "DE",
            "availableLanguage": "de"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Flutter Cross-Platform Entwicklung", 
            "description": "Cross-platform Mobile Apps mit Flutter Framework",
            "provider": {
              "@type": "LocalBusiness",
              "name": "Innovatio"
            },
            "areaServed": "DE",
            "availableLanguage": "de"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "UI/UX Design für Apps",
            "description": "Benutzerfreundliches Design und User Experience für Mobile Apps",
            "provider": {
              "@type": "LocalBusiness", 
              "name": "Innovatio"
            },
            "areaServed": "DE",
            "availableLanguage": "de"
          }
        }
      ]
    },
    
    // Industry and business category
    "industry": "Software Development",
    "naics": "541511", // Custom Computer Programming Services
    "isicV4": "6201", // Computer programming activities
    "keywords": "App Entwickler Deutschland, Mobile App Entwicklung, iOS Entwicklung, Android Entwicklung, Flutter Apps, Flutter Entwicklung, App Design, Mobile Development Germany",
    
    // Aggregated ratings (replace with real data when available)
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "47",
      "bestRating": "5",
      "worstRating": "1"
    },
    
    // Sample reviews (replace with real reviews)
    "review": [
      {
        "@type": "Review",
        "author": {
          "@type": "Person",
          "name": "Emin C."
        },
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "reviewBody": "Zuverlässig und strukturiert! Viktor Hermann ist ein äußerst zuverlässiger und sympathischer Mobile App Entwickler, der sowohl bei technischen Komponenten als auch bei der User Experience keine Wünsche offen lässt!",
        "datePublished": "2024-12-15"
      },
      {
        "@type": "Review",
        "author": {
          "@type": "Person", 
          "name": "Stefanie W."
        },
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "reviewBody": "Viktor lernte ich in einem Projekt als sehr kompetenten Mobile Entwickler kennen und schätzen. Seine schnelle Auffassungsgabe und sein hohes Engagement bei der Problemlösung sind bemerkenswert.",
        "datePublished": "2024-11-20"
      }
    ],
    
    // Social media presence
    "sameAs": [
      "https://linkedin.com/company/innovatio-pro",
      "https://github.com/innovatio-pro",
      "https://twitter.com/innovatio_pro"
    ],
    
    // Certifications and credentials
    "hasCredential": [
      {
        "@type": "EducationalOccupationalCredential",
        "credentialCategory": "Professional Certification",
        "name": "Google Play Developer",
        "recognizedBy": {
          "@type": "Organization",
          "name": "Google"
        }
      },
      {
        "@type": "EducationalOccupationalCredential", 
        "credentialCategory": "Professional Certification",
        "name": "Apple Developer Program Member",
        "recognizedBy": {
          "@type": "Organization",
          "name": "Apple Inc."
        }
      }
    ],
    
    // Payment methods accepted
    "paymentAccepted": "Rechnung, Überweisung, PayPal, Stripe",
    "currenciesAccepted": "EUR",
    "priceRange": "€€-€€€"
  };

  // International business data for non-German locales
  const internationalBusinessData = {
    ...germanBusinessData,
    "@type": "Organization",
    "name": "Innovatio - Mobile App Development",
    "description": "Professional mobile app development services. Specialized in Flutter development for iOS and Android apps for businesses, startups, and entrepreneurs worldwide.",
    "url": "https://innovatio-pro.com",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "DE",
      "addressRegion": "International"
    },
    "areaServed": {
      "@type": "Country",
      "name": "Worldwide"
    },
    "availableLanguage": ["en", "de", "ru", "tr", "ar"],
    "keywords": "Mobile App Developer, iOS Development, Android Development, Flutter Apps, Flutter Development, App Design, Mobile Development Services",
    "paymentAccepted": "Invoice, Bank Transfer, PayPal, Stripe",
    "currenciesAccepted": "EUR, USD"
  };

  const jsonLd = locale === 'de' ? germanBusinessData : internationalBusinessData;

  return (
    <Script
      id="local-business-jsonld"
      type="application/ld+json"
      strategy="beforeInteractive"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}