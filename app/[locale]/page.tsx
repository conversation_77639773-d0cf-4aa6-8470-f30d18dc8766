import { HeroSection } from '@/components/sections/HeroSection'
import { getDictionary } from '@/lib/dictionaries'
import { PainPointSection } from '@/components/sections/PainPointSection';
import { USPSection } from '@/components/sections/USPSection';
import { FloatingContact } from "@/components/ui/FloatingContact";
import { FloatingBlogButton } from "@/components/ui/FloatingBlogButton";
import ClientOnly from "@/components/ui/ClientOnly";

// Lazy load heavy components that are below the fold
import dynamic from 'next/dynamic';

const AboutSection = dynamic(() => import("@/components/sections/AboutSection").then(mod => ({ default: mod.AboutSection })), {
  loading: () => <div className="h-96 bg-gray-50 dark:bg-gray-900 animate-pulse" />,
});

const PremiumTestimonials = dynamic(() => import('@/components/ui/premium-testimonials').then(mod => ({ default: mod.PremiumTestimonials })), {
  loading: () => <div className="h-64 bg-gray-50 dark:bg-gray-900 animate-pulse" />,
});


const ContactSection = dynamic(() => import('@/components/sections/ContactSection').then(mod => ({ default: mod.ContactSection })), {
  loading: () => <div className="h-64 bg-gray-50 dark:bg-gray-900 animate-pulse" />,
});

const FooterSection = dynamic(() => import('@/components/sections/FooterSection').then(mod => ({ default: mod.FooterSection })), {
  loading: () => <div className="h-32 bg-gray-50 dark:bg-gray-900 animate-pulse" />,
});

const ClientSuccessStoriesSection = dynamic(() => import("@/components/sections/SolutionsPortfolioSection").then(mod => ({ default: mod.ClientSuccessStoriesSection })), {
  loading: () => <div className="h-96 bg-gray-50 dark:bg-gray-900 animate-pulse" />,
});


const ServicesSection = dynamic(() => import("@/components/sections/ServicesSection").then(mod => ({ default: mod.ServicesSection })), {
  loading: () => <div className="h-64 bg-gray-50 dark:bg-gray-900 animate-pulse" />,
});

const AiEnhancedSection = dynamic(() => import("@/components/sections/AiEnhancedSection").then(mod => ({ default: mod.AiEnhancedSection })), {
  loading: () => <div className="h-64 bg-gray-50 dark:bg-gray-900 animate-pulse" />,
});


const PortfolioSection = dynamic(() => import("@/components/sections/PortfolioSection").then(mod => ({ default: mod.PortfolioSection })), {
  loading: () => <div className="h-96 bg-gray-50 dark:bg-gray-900 animate-pulse" />,
});

const PricingSection = dynamic(() => import("@/components/sections/PricingSection").then(mod => ({ default: mod.default })), {
  loading: () => <div className="h-96 bg-gray-50 dark:bg-gray-900 animate-pulse" />,
});

const SEOAIOptimizer = dynamic(() => import("@/components/seo/SEOManager"), {
  loading: () => null,
});

const PerformanceOptimizer = dynamic(() => import("@/components/performance/PerformanceOptimizer"), {
  loading: () => null,
});

interface HomeProps {
  params: Promise<{ locale: string }>;
}

export default async function Home({ params }: HomeProps) {
  const { locale } = await params;
  // Get the dictionary for the current locale
  const dict = await getDictionary(locale);

  return (
    <main
      className={`flex min-h-screen flex-col w-full overflow-x-hidden justify-between ${
        locale === "ar" ? "rtl" : ""
      }`}
    >
      {/* Performance and AI Optimization */}
      <ClientOnly>
        <PerformanceOptimizer />
        <SEOAIOptimizer
          locale={locale}
          pageType="home"
          primaryKeywords={[
            "Web Development",
            "Mobile App Development",
            "Digital Solutions",
            "Next.js Development",
            "Flutter Development",
          ]}
          secondaryKeywords={[
            "AI Integration",
            "Digital Transformation",
            "UX/UI Design",
            "Custom Software",
            "Business Solutions",
          ]}
          contentCategory="Digital Solutions"
          entityType="Organization"
        />
      </ClientOnly>

      {/* Floating Contact Component */}
      <FloatingContact dictionary={dict.contact} />

      {/* Floating Blog Button - positioned to the left of contact */}
      <FloatingBlogButton locale={locale} />

      {/* Hero Section - Introduction and company overview */}
      <HeroSection dictionary={dict.hero} />

      {/* USP Section - Why we're different */}
      <USPSection
        dictionary={
          dict.usp || {
            badge: "Our Strengths",
            title: "Why We're Different",
            subtitle: "No meetings. No tech jargon. Just your app.",
            description:
              "While other agencies confuse you with tech babble, we speak your language and deliver results.",
            features: [
              {
                title: "Personal consultation for absolute tech novices",
                description:
                  "We explain everything in simple terms. No jargon, no confusion.",
                icon: "👥",
              },
              {
                title: "Apps for every industry and budget",
                description:
                  "Whether doctor, craftsman, or startup – we develop the perfect solution.",
                icon: "🏗️",
              },
              {
                title: "Everything from one source: Design to Launch",
                description:
                  "One contact person for everything. From first idea to app store.",
                icon: "🎯",
              },
              {
                title: "No hidden costs, no surprises",
                description:
                  "Transparent prices from the start. What we say, we deliver.",
                icon: "💰",
              },
              {
                title: "Fixed contact persons, clear communication",
                description:
                  "You always have the same contact person and know exactly what's happening.",
                icon: "📞",
              },
              {
                title: "Fast delivery without quality loss",
                description:
                  "Thanks to proven processes and modern tools, we deliver fast and reliable.",
                icon: "⚡",
              },
              {
                title: "Future-proof technology",
                description:
                  "We use modern, scalable solutions that grow with your success.",
                icon: "🚀",
              },
              {
                title: "100% focus on your success",
                description:
                  "We're only satisfied when you are. Your success is our success.",
                icon: "🏆",
              },
            ],
            cta: {
              primary: "Get Free Consultation Now",
              secondary: "Book Non-Binding Initial Call",
            },
          }
        }
      />

      {/* Services Section - Our competencies */}
      {dict.services && <ServicesSection dictionary={dict.services} />}

      {/* AI Enhanced Section - Technology leadership */}
      {dict.aiEnhanced && <AiEnhancedSection dictionary={dict.aiEnhanced} />}

      {/* Portfolio Section - Show developed apps */}
      {dict.portfolio && <PortfolioSection dictionary={dict.portfolio} />}

      {/* Pricing Section - Show pricing packages */}
      <section id="pricing">
        <PricingSection 
          dictionary={dict.prices} 
          packages={dict.packages || dict.prices?.packages} 
        />
      </section>

      {/* Testimonials Section - Premium testimonials with enhanced animations */}
      <PremiumTestimonials dictionary={dict.testimonials} />

      {/* Contact Section - Call to action */}
      <ContactSection dictionary={dict.contact} />

      {/* Footer Section - Links and copyright */}
      <FooterSection dictionary={dict.footer} locale={locale} />
    </main>
  );
}