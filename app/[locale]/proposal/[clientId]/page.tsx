"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { PasswordProtected } from "@/components/ui/PasswordProtected";
import {
  Calendar,
  Clock,
  Package,
  User,
  Mail,
  Building,
  Phone,
  MessageCircle,
  CheckCircle,
  X,
  Edit,
  ExternalLink,
  Lock,
  Plus,
  Minus,
  Save,
  Calculator,
  AlertCircle,
  Trash2,
  DollarSign,
} from "lucide-react";
import {
  AdditionalHourlyService,
  PriceModification,
  PriceBreakdown,
} from "@/types/proposal";

interface ProposalPageProps {
  params: Promise<{
    clientId: string;
    locale: string;
  }>;
}

interface ProposalData {
  clientName: string;
  clientEmail: string;
  company?: string;
  phone?: string;

  // Address Information
  street?: string;
  houseNumber?: string;
  city?: string;
  postalCode?: string;
  country?: string;

  selectedService: string;
  selectedPackage?: {
    id: string;
    name: string;
    price: number;
    originalPrice?: number;
    timeframe: string;
    description: string;
    features: string[];
    trustIndicators: string[];
  };
  estimatedBudget: string;
  projectTimeline: string;
  message: string;
  heardAbout?: string;
  changeRequests?: string;
  changeRequestDate?: string | null;
  status?: string;
  urlPassword?: string;
  signatureUrl?: string;
  signatureDate?: string | null;
  notes?: string;
  additionalServices?: AdditionalHourlyService[];
  priceModifications?: PriceModification[];
  priceBreakdown?: PriceBreakdown;
}

interface ServicePackage {
  id: string;
  name: string;
  originalPrice: number;
  discountedPrice: number;
  timeline: string;
  description: string;
  features: string[];
  discount: number;
}

// Service packages are now loaded from the API - no longer needed here

export default function ProposalPage({ params }: ProposalPageProps) {
  const [loading, setLoading] = useState(true);
  const [proposalData, setProposalData] = useState<ProposalData | null>(null);
  const [modifications, setModifications] = useState<string>("");
  const [showModificationForm, setShowModificationForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [proposalAccepted, setProposalAccepted] = useState(false);
  const [clientId, setClientId] = useState<string>("");

  // New states for enhanced features
  const [showPriceEditModal, setShowPriceEditModal] = useState(false);
  const [showAddServiceModal, setShowAddServiceModal] = useState(false);
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [priceModification, setPriceModification] = useState({
    type: "increase" as "increase" | "decrease" | "fixed",
    amount: 0,
    reason: "",
  });
  const [newService, setNewService] = useState<AdditionalHourlyService>({
    id: "",
    name: "",
    description: "",
    hourlyRate: 150,
    estimatedHours: 1,
    totalPrice: 150,
    category: "development",
  });
  const [addressData, setAddressData] = useState({
    street: "",
    houseNumber: "",
    city: "",
    postalCode: "",
    country: "",
  });
  const [descriptionData, setDescriptionData] = useState("");

  // Resolve params async
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setClientId(resolvedParams.clientId);
    };
    resolveParams();
  }, [params]);

  // Load proposal data when clientId is available
  useEffect(() => {
    if (!clientId) return;

    const fetchProposalData = async () => {
      try {
        const response = await fetch(`/api/get-proposal-data/${clientId}`);
        if (response.ok) {
          const data = await response.json();
          setProposalData(data);

          // Initialize address form data
          setAddressData({
            street: data.street || "",
            houseNumber: data.houseNumber || "",
            city: data.city || "",
            postalCode: data.postalCode || "",
            country: data.country || "",
          });

          // Initialize description data
          setDescriptionData(data.message || "");
        } else {
          console.error("Failed to fetch proposal data");
        }
      } catch (error) {
        console.error("Error fetching proposal data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProposalData();
  }, [clientId]);

  // Use the package data returned from the API instead of local lookup
  const selectedPackage = proposalData?.selectedPackage || null;

  const isQualified = proposalData?.status === "Qualified";

  // Calculate total price including all modifications and additional services
  const calculateTotalPrice = () => {
    const basePrice = selectedPackage?.price || 0;
    const additionalServicesTotal = (
      proposalData?.additionalServices || []
    ).reduce((sum, service) => sum + service.totalPrice, 0);
    const modificationsTotal = (proposalData?.priceModifications || []).reduce(
      (sum, mod) => {
        if (mod.type === "increase") return sum + mod.amount;
        if (mod.type === "decrease") return sum - mod.amount;
        return sum; // For 'fixed' type, it replaces the base price
      },
      0
    );

    return basePrice + additionalServicesTotal + modificationsTotal;
  };

  const handleAcceptProposal = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/proposal/accept", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId: clientId,
        }),
      });

      if (response.ok) {
        setProposalAccepted(true);
        setShowSuccessModal(true);
        setSuccessMessage(
          "Vielen Dank! Wir werden Ihre Anfrage überprüfen und Ihnen einen Vertrag zum Unterzeichnen zusenden."
        );
      }
    } catch (error) {
      console.error("Error accepting proposal:", error);
    }
    setIsSubmitting(false);
  };

  const handleRequestModification = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/proposal/modify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId: clientId,
          modifications: modifications,
          date: new Date().toISOString(),
        }),
      });

      if (response.ok) {
        setSuccessMessage("Änderungsanfrage erfolgreich gesendet!");
        setShowModificationForm(false);
        setModifications("");

        // Refresh proposal data to show the new change request
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        setSuccessMessage("Fehler beim Senden der Änderungsanfrage.");
      }
    } catch (error) {
      console.error("Error sending modification request:", error);
    }
    setIsSubmitting(false);
  };

  // Handle price modification
  const handlePriceModification = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/proposal/modify-price", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId: clientId,
          modificationType: priceModification.type,
          amount: priceModification.amount,
          reason: priceModification.reason,
          modifiedBy: "Admin",
        }),
      });

      if (response.ok) {
        setSuccessMessage("Preis erfolgreich geändert!");
        setShowPriceEditModal(false);
        setPriceModification({ type: "increase", amount: 0, reason: "" });

        // Refresh data
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        setSuccessMessage("Fehler beim Ändern des Preises.");
      }
    } catch (error) {
      console.error("Error modifying price:", error);
    }
    setIsSubmitting(false);
  };

  // Handle additional service
  const handleAddService = async () => {
    setIsSubmitting(true);
    try {
      const serviceToAdd = {
        ...newService,
        id: Date.now().toString(),
        totalPrice: newService.hourlyRate * newService.estimatedHours,
      };

      const response = await fetch("/api/proposal/add-service", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId: clientId,
          service: serviceToAdd,
          action: "add",
        }),
      });

      if (response.ok) {
        setSuccessMessage("Service erfolgreich hinzugefügt!");
        setShowAddServiceModal(false);
        setNewService({
          id: "",
          name: "",
          description: "",
          hourlyRate: 150,
          estimatedHours: 1,
          totalPrice: 150,
          category: "development",
        });

        // Refresh data
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        setSuccessMessage("Fehler beim Hinzufügen des Services.");
      }
    } catch (error) {
      console.error("Error adding service:", error);
    }
    setIsSubmitting(false);
  };

  // Handle address update
  const handleSaveAddress = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/proposal/modify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId,
          // Address update fields
          street: addressData.street,
          houseNumber: addressData.houseNumber,
          city: addressData.city,
          postalCode: addressData.postalCode,
          country: addressData.country,
        }),
      });

      if (response.ok) {
        setSuccessMessage("Adresse erfolgreich gespeichert!");
        setIsEditingAddress(false);

        // Update local state
        setProposalData((prev) => ({
          ...prev!,
          street: addressData.street,
          houseNumber: addressData.houseNumber,
          city: addressData.city,
          postalCode: addressData.postalCode,
          country: addressData.country,
        }));

        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(""), 3000);
      } else {
        setSuccessMessage("Fehler beim Speichern der Adresse.");
        setTimeout(() => setSuccessMessage(""), 3000);
      }
    } catch (error) {
      console.error("Error saving address:", error);
      setSuccessMessage("Fehler beim Speichern der Adresse.");
      setTimeout(() => setSuccessMessage(""), 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle description update
  const handleSaveDescription = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/proposal/modify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId,
          // Description update field
          message: descriptionData,
        }),
      });

      if (response.ok) {
        setSuccessMessage("Projektbeschreibung erfolgreich gespeichert!");
        setIsEditingDescription(false);

        // Update local state
        setProposalData((prev) => ({
          ...prev!,
          message: descriptionData,
        }));

        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(""), 3000);
      } else {
        setSuccessMessage("Fehler beim Speichern der Projektbeschreibung.");
        setTimeout(() => setSuccessMessage(""), 3000);
      }
    } catch (error) {
      console.error("Error saving description:", error);
      setSuccessMessage("Fehler beim Speichern der Projektbeschreibung.");
      setTimeout(() => setSuccessMessage(""), 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle remove service
  const handleRemoveService = async (serviceId: string) => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/proposal/add-service", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId: clientId,
          serviceId: serviceId,
          action: "remove",
        }),
      });

      if (response.ok) {
        setSuccessMessage("Service erfolgreich entfernt!");

        // Refresh data
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        setSuccessMessage("Fehler beim Entfernen des Services.");
      }
    } catch (error) {
      console.error("Error removing service:", error);
    }
    setIsSubmitting(false);
  };

  const handleScheduleMeeting = () => {
    window.open(
      "https://calendly.com/v-hermann-it/introductory-call-your-needs-goals?back=1&month=2025-01",
      "_blank"
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!clientId) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <Lock className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-4">Invalid Access</h1>
          <p className="text-gray-300 mb-6">No client ID provided.</p>
        </div>
      </div>
    );
  }

  if (!proposalData) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Proposal Not Found
          </h1>
          <p className="text-gray-300 mb-6">
            The proposal data could not be loaded. This may be because:
          </p>
          <ul className="text-gray-400 text-sm text-left mb-6 space-y-2">
            <li>• The proposal ID is invalid</li>
            <li>• The Notion page doesn't exist</li>
            <li>• There's no data for this client</li>
            <li>• Access permissions are missing</li>
          </ul>
          <p className="text-sm text-gray-500">
            Please contact support if you believe this is an error.
          </p>
        </div>
      </div>
    );
  }

  if (!selectedPackage) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Service Package Not Found
          </h1>
          <p className="text-gray-300 mb-6">
            The selected service package "{proposalData.selectedService}" is not
            available.
          </p>
          <p className="text-sm text-gray-500">
            Please contact support to resolve this issue.
          </p>
        </div>
      </div>
    );
  }

  // Wrap the entire proposal content in PasswordProtected
  return (
    <PasswordProtected
      clientId={clientId}
      redirectPath={`/de/proposal/${clientId}`}
      title="Proposal Access"
      description="Please enter the password that was provided to you to access this proposal."
    >
      <ProposalContent
        proposalData={proposalData}
        clientId={clientId}
        handleAcceptProposal={handleAcceptProposal}
        handleRequestModification={handleRequestModification}
        handleScheduleMeeting={handleScheduleMeeting}
        selectedPackage={selectedPackage}
        isQualified={isQualified}
        modifications={modifications}
        setModifications={setModifications}
        showModificationForm={showModificationForm}
        setShowModificationForm={setShowModificationForm}
        isSubmitting={isSubmitting}
        successMessage={successMessage}
        showSuccessModal={showSuccessModal}
        setShowSuccessModal={setShowSuccessModal}
        proposalAccepted={proposalAccepted}
        calculateTotalPrice={calculateTotalPrice}
        showPriceEditModal={showPriceEditModal}
        setShowPriceEditModal={setShowPriceEditModal}
        showAddServiceModal={showAddServiceModal}
        setShowAddServiceModal={setShowAddServiceModal}
        isEditingAddress={isEditingAddress}
        setIsEditingAddress={setIsEditingAddress}
        isEditingDescription={isEditingDescription}
        setIsEditingDescription={setIsEditingDescription}
        priceModification={priceModification}
        setPriceModification={setPriceModification}
        newService={newService}
        setNewService={setNewService}
        addressData={addressData}
        setAddressData={setAddressData}
        descriptionData={descriptionData}
        setDescriptionData={setDescriptionData}
        handlePriceModification={handlePriceModification}
        handleAddService={handleAddService}
        handleRemoveService={handleRemoveService}
        handleSaveAddress={handleSaveAddress}
        handleSaveDescription={handleSaveDescription}
      />
    </PasswordProtected>
  );
}

// Separate component for the actual proposal content
function ProposalContent({
  proposalData,
  clientId,
  handleAcceptProposal,
  handleRequestModification,
  handleScheduleMeeting,
  selectedPackage,
  isQualified,
  modifications,
  setModifications,
  showModificationForm,
  setShowModificationForm,
  isSubmitting,
  successMessage,
  showSuccessModal,
  setShowSuccessModal,
  proposalAccepted,
  calculateTotalPrice,
  showPriceEditModal,
  setShowPriceEditModal,
  showAddServiceModal,
  setShowAddServiceModal,
  isEditingAddress,
  setIsEditingAddress,
  isEditingDescription,
  setIsEditingDescription,
  priceModification,
  setPriceModification,
  newService,
  setNewService,
  addressData,
  setAddressData,
  descriptionData,
  setDescriptionData,
  handlePriceModification,
  handleAddService,
  handleRemoveService,
  handleSaveAddress,
  handleSaveDescription,
}: {
  proposalData: ProposalData | null;
  clientId: string;
  handleAcceptProposal: () => void;
  handleRequestModification: () => void;
  handleScheduleMeeting: () => void;
  selectedPackage: ProposalData["selectedPackage"] | null;
  isQualified: boolean;
  modifications: string;
  setModifications: (value: string) => void;
  showModificationForm: boolean;
  setShowModificationForm: (value: boolean) => void;
  isSubmitting: boolean;
  successMessage: string;
  showSuccessModal: boolean;
  setShowSuccessModal: (value: boolean) => void;
  proposalAccepted: boolean;
  calculateTotalPrice: () => number;
  showPriceEditModal: boolean;
  setShowPriceEditModal: (value: boolean) => void;
  showAddServiceModal: boolean;
  setShowAddServiceModal: (value: boolean) => void;
  isEditingAddress: boolean;
  setIsEditingAddress: (value: boolean) => void;
  isEditingDescription: boolean;
  setIsEditingDescription: (value: boolean) => void;
  priceModification: {
    type: "increase" | "decrease" | "fixed";
    amount: number;
    reason: string;
  };
  setPriceModification: (value: {
    type: "increase" | "decrease" | "fixed";
    amount: number;
    reason: string;
  }) => void;
  newService: AdditionalHourlyService;
  setNewService: (value: AdditionalHourlyService) => void;
  addressData: {
    street: string;
    houseNumber: string;
    city: string;
    postalCode: string;
    country: string;
  };
  setAddressData: (value: {
    street: string;
    houseNumber: string;
    city: string;
    postalCode: string;
    country: string;
  }) => void;
  descriptionData: string;
  setDescriptionData: (value: string) => void;
  handlePriceModification: () => void;
  handleAddService: () => void;
  handleRemoveService: (serviceId: string) => void;
  handleSaveAddress: () => void;
  handleSaveDescription: () => void;
}) {
  if (!proposalData) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Proposal Not Found
          </h1>
          <p className="text-gray-300 mb-6">
            The proposal data could not be loaded.
          </p>
        </div>
      </div>
    );
  }

  if (!selectedPackage) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Service Package Not Found
          </h1>
          <p className="text-gray-300 mb-6">
            The selected service package is not available.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-gray-800 border border-blue-500 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-2">
            <div className="text-blue-500">📄</div>
            <h1 className="text-2xl font-bold text-white">
              Projektangebot - {proposalData.clientName}
            </h1>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-blue-400">
              Proposal ID: {clientId} • Gültig bis:{" "}
              {new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toLocaleDateString("de-DE")}
            </p>
            {proposalData?.status && (
              <div
                className={`px-3 py-1 rounded-full text-sm font-semibold ${
                  proposalData.status === "ChangeRequest"
                    ? "bg-orange-500/20 text-orange-400 border border-orange-500/30"
                    : proposalData.status === "qualified"
                      ? "bg-green-500/20 text-green-400 border border-green-500/30"
                      : "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                }`}
              >
                Status:{" "}
                {proposalData.status === "ChangeRequest"
                  ? "Änderung angefragt"
                  : proposalData.status === "qualified"
                    ? "Akzeptiert"
                    : proposalData.status}
              </div>
            )}
          </div>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="bg-green-800 border border-green-500 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3">
              <CheckCircle className="text-green-400 w-5 h-5" />
              <span className="text-green-100">{successMessage}</span>
            </div>
          </div>
        )}

        {/* Customer Data */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <User className="text-white w-5 h-5" />
            <h2 className="text-xl font-semibold text-white">Kundendaten</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-3">
              <User className="text-gray-400 w-4 h-4" />
              <div>
                <span className="text-gray-400 text-sm">Name: </span>
                <span className="text-white">{proposalData.clientName}</span>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Mail className="text-gray-400 w-4 h-4" />
              <div>
                <span className="text-gray-400 text-sm">Email: </span>
                <span className="text-white">{proposalData.clientEmail}</span>
              </div>
            </div>
            {proposalData.company && (
              <div className="flex items-center gap-3">
                <Building className="text-gray-400 w-4 h-4" />
                <div>
                  <span className="text-gray-400 text-sm">Firma: </span>
                  <span className="text-white">{proposalData.company}</span>
                </div>
              </div>
            )}
            {proposalData.phone && (
              <div className="flex items-center gap-3">
                <Phone className="text-gray-400 w-4 h-4" />
                <div>
                  <span className="text-gray-400 text-sm">Telefon: </span>
                  <span className="text-white">{proposalData.phone}</span>
                </div>
              </div>
            )}
          </div>

          {/* Address Information */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                <Building className="text-gray-400 w-5 h-5" />
                Adressinformationen
              </h3>
              {!isEditingAddress &&
                (proposalData.street ||
                  proposalData.city ||
                  proposalData.country) && (
                  <button
                    onClick={() => setIsEditingAddress(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-semibold transition-colors flex items-center gap-1"
                  >
                    <Edit className="w-3 h-3" />
                    Bearbeiten
                  </button>
                )}
            </div>

            {/* Address Form (always visible, required fields) */}
            {isEditingAddress || !proposalData.street || !proposalData.city ? (
              <div className="bg-gray-700/50 rounded-lg p-6 border-2 border-blue-500/30">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Straße <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        value={addressData.street}
                        onChange={(e) =>
                          setAddressData({
                            ...addressData,
                            street: e.target.value,
                          })
                        }
                        className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                        placeholder="Musterstraße"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Hausnummer <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        value={addressData.houseNumber}
                        onChange={(e) =>
                          setAddressData({
                            ...addressData,
                            houseNumber: e.target.value,
                          })
                        }
                        className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                        placeholder="123"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Postleitzahl <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        value={addressData.postalCode}
                        onChange={(e) =>
                          setAddressData({
                            ...addressData,
                            postalCode: e.target.value,
                          })
                        }
                        className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                        placeholder="12345"
                        required
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Stadt <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        value={addressData.city}
                        onChange={(e) =>
                          setAddressData({
                            ...addressData,
                            city: e.target.value,
                          })
                        }
                        className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                        placeholder="Berlin"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Land
                    </label>
                    <select
                      value={addressData.country}
                      onChange={(e) =>
                        setAddressData({
                          ...addressData,
                          country: e.target.value,
                        })
                      }
                      className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                    >
                      <option value="">Land auswählen (optional)</option>
                      <option value="Deutschland">Deutschland</option>
                      <option value="Österreich">Österreich</option>
                      <option value="Schweiz">Schweiz</option>
                      <option value="Niederlande">Niederlande</option>
                      <option value="Belgien">Belgien</option>
                      <option value="Frankreich">Frankreich</option>
                      <option value="Italien">Italien</option>
                      <option value="Spanien">Spanien</option>
                      <option value="Polen">Polen</option>
                      <option value="Tschechien">Tschechien</option>
                      <option value="Türkei">Türkei</option>
                      <option value="Vereinigte Staaten">
                        Vereinigte Staaten
                      </option>
                      <option value="Kanada">Kanada</option>
                      <option value="Vereinigtes Königreich">
                        Vereinigtes Königreich
                      </option>
                      <option value="Sonstige">Sonstige</option>
                    </select>
                  </div>

                  <div className="flex gap-3 pt-4">
                    <button
                      onClick={handleSaveAddress}
                      disabled={
                        !addressData.street.trim() ||
                        !addressData.houseNumber.trim() ||
                        !addressData.city.trim() ||
                        !addressData.postalCode.trim() ||
                        isSubmitting
                      }
                      className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 px-6 rounded-lg font-semibold transition-colors flex items-center gap-2"
                    >
                      <Save className="w-4 h-4" />
                      {isSubmitting ? "Speichert..." : "Adresse speichern"}
                    </button>
                    {isEditingAddress && (
                      <button
                        onClick={() => {
                          setIsEditingAddress(false);
                          // Reset form to original data
                          setAddressData({
                            street: proposalData.street || "",
                            houseNumber: proposalData.houseNumber || "",
                            city: proposalData.city || "",
                            postalCode: proposalData.postalCode || "",
                            country: proposalData.country || "",
                          });
                        }}
                        className="px-6 bg-gray-600 hover:bg-gray-500 text-white py-3 rounded-lg font-semibold transition-colors"
                      >
                        Abbrechen
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              /* Address Display (when not editing and address exists) */
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {proposalData.street && (
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-gray-400 text-sm mb-1">Straße</div>
                    <div className="text-white font-medium">
                      {proposalData.street} {proposalData.houseNumber || ""}
                    </div>
                  </div>
                )}
                {proposalData.city && (
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-gray-400 text-sm mb-1">Stadt</div>
                    <div className="text-white font-medium">
                      {proposalData.postalCode && `${proposalData.postalCode} `}
                      {proposalData.city}
                    </div>
                  </div>
                )}
                {proposalData.country && (
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-gray-400 text-sm mb-1">Land</div>
                    <div className="text-white font-medium">
                      {proposalData.country}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Selected Package - Styled like in the image */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-6">
            <Package className="text-white w-5 h-5" />
            <h2 className="text-xl font-semibold text-white">
              Ausgewähltes Paket
            </h2>
          </div>

          {/* Package Card */}
          <div className="bg-gradient-to-br from-blue-600 to-purple-700 rounded-lg p-6 text-white relative overflow-hidden">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4" />
                  <span className="text-blue-100">
                    {selectedPackage.timeframe}
                  </span>
                </div>
                <h3 className="text-2xl font-bold mb-2">
                  {selectedPackage.name}
                </h3>
              </div>
              {selectedPackage.originalPrice && (
                <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  💾 Save{" "}
                  {Math.round(
                    ((selectedPackage.originalPrice - selectedPackage.price) /
                      selectedPackage.originalPrice) *
                      100
                  )}
                  %
                </div>
              )}
            </div>

            {/* Description */}
            <p className="text-blue-100 mb-6">{selectedPackage.description}</p>

            {/* Price */}
            <div className="mb-6">
              <div className="flex items-baseline gap-3">
                <span className="text-4xl font-bold">
                  €{selectedPackage.price.toLocaleString("de-DE")}
                </span>
                {selectedPackage.originalPrice && (
                  <span className="text-xl text-blue-200 line-through">
                    €{selectedPackage.originalPrice.toLocaleString("de-DE")}
                  </span>
                )}
              </div>
            </div>

            {/* Features */}
            <div className="space-y-3">
              {selectedPackage.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-white">{feature}</span>
                </div>
              ))}
            </div>

            {/* Timeline and tags */}
            <div className="mt-6 flex flex-wrap gap-2">
              <div className="bg-blue-500/30 text-blue-100 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                ⚡ Fast Delivery
              </div>
              <div className="bg-green-500/30 text-green-100 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                🔒 Secure
              </div>
              <div className="bg-purple-500/30 text-purple-100 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                🎨 Modern
              </div>
            </div>
          </div>
        </div>

        {/* Project Details */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            Projektdetails
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">Budget</div>
              <div className="text-white font-semibold">
                {proposalData.estimatedBudget
                  .replace("_", " - ")
                  .replace("k", ".000€")}
              </div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">Timeline</div>
              <div className="text-white font-semibold">
                {proposalData.projectTimeline
                  .replace("_", "-")
                  .replace("months", " Monate")
                  .replace("month", " Monat")}
              </div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">Quelle</div>
              <div className="text-white font-semibold">
                {proposalData.heardAbout}
              </div>
            </div>
          </div>

          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="text-gray-400 text-sm">Projektbeschreibung</div>
              {!isEditingDescription ? (
                <button
                  onClick={() => setIsEditingDescription(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-semibold transition-colors flex items-center gap-1"
                >
                  <Edit className="w-3 h-3" />
                  Bearbeiten
                </button>
              ) : (
                <div className="flex gap-2">
                  <button
                    onClick={handleSaveDescription}
                    disabled={isSubmitting}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-sm font-semibold transition-colors flex items-center gap-1"
                  >
                    <Save className="w-3 h-3" />
                    {isSubmitting ? "Speichert..." : "Speichern"}
                  </button>
                  <button
                    onClick={() => {
                      setIsEditingDescription(false);
                      setDescriptionData(proposalData.message || "");
                    }}
                    className="bg-gray-600 hover:bg-gray-500 text-white px-3 py-1 rounded text-sm font-semibold transition-colors"
                  >
                    Abbrechen
                  </button>
                </div>
              )}
            </div>

            {isEditingDescription ? (
              <div className="space-y-3">
                <textarea
                  value={descriptionData}
                  onChange={(e) => setDescriptionData(e.target.value)}
                  className="w-full p-3 bg-gray-600 text-white rounded-lg border border-gray-500 focus:border-blue-500 focus:outline-none min-h-[120px] resize-vertical"
                  placeholder="Beschreiben Sie Ihr Projekt im Detail..."
                  rows={6}
                />
                <div className="text-xs text-gray-400">
                  Erweitern Sie die Projektbeschreibung mit zusätzlichen
                  Details, Anforderungen oder Wünschen.
                </div>
              </div>
            ) : (
              <div className="text-white whitespace-pre-wrap">
                {proposalData.message || "Keine Projektbeschreibung verfügbar"}
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Investment Breakdown */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Calculator className="text-white w-5 h-5" />
              <h3 className="text-xl font-semibold text-white">
                Detaillierte Preisaufschlüsselung
              </h3>
            </div>
            <button
              onClick={() => setShowPriceEditModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-semibold transition-colors"
            >
              <Edit className="w-4 h-4" />
              Preis bearbeiten
            </button>
          </div>

          <div className="space-y-3">
            {/* Base Package */}
            <div className="flex justify-between items-center py-3 border-b border-gray-700">
              <div>
                <span className="text-gray-300">
                  Grundpaket ({selectedPackage.name})
                </span>
                <div className="text-xs text-gray-500">
                  {selectedPackage.description}
                </div>
              </div>
              <span className="text-white font-semibold">
                €
                {(
                  selectedPackage.originalPrice || selectedPackage.price
                ).toLocaleString("de-DE")}
              </span>
            </div>

            {/* Package Discount */}
            {selectedPackage.originalPrice &&
              selectedPackage.originalPrice > selectedPackage.price && (
                <div className="flex justify-between items-center py-3 border-b border-gray-700">
                  <span className="text-gray-300">
                    Paket-Rabatt (
                    {Math.round(
                      ((selectedPackage.originalPrice - selectedPackage.price) /
                        selectedPackage.originalPrice) *
                        100
                    )}
                    %)
                  </span>
                  <span className="text-green-400 font-semibold">
                    -€
                    {(
                      selectedPackage.originalPrice - selectedPackage.price
                    ).toLocaleString("de-DE")}
                  </span>
                </div>
              )}

            {/* Additional Hourly Services */}
            {proposalData.additionalServices &&
              proposalData.additionalServices.length > 0 && (
                <>
                  <div className="py-2">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-lg font-semibold text-white">
                        Zusätzliche Services
                      </h4>
                      <button
                        onClick={() => setShowAddServiceModal(true)}
                        className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs font-semibold transition-colors flex items-center gap-1"
                      >
                        <Plus className="w-3 h-3" />
                        Hinzufügen
                      </button>
                    </div>
                    {proposalData.additionalServices.map((service) => (
                      <div
                        key={service.id}
                        className="flex justify-between items-center py-2 pl-4 border-l-2 border-blue-500"
                      >
                        <div>
                          <span className="text-gray-300">{service.name}</span>
                          <div className="text-xs text-gray-500">
                            {service.estimatedHours}h × €{service.hourlyRate}/h
                            • {service.category}
                          </div>
                          <div className="text-xs text-gray-400">
                            {service.description}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-white font-semibold">
                            €{service.totalPrice.toLocaleString("de-DE")}
                          </span>
                          <button
                            onClick={() => handleRemoveService(service.id)}
                            className="text-red-400 hover:text-red-300 p-1"
                          >
                            <Trash2 className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}

            {/* Add Service Button (if no additional services) */}
            {(!proposalData.additionalServices ||
              proposalData.additionalServices.length === 0) && (
              <div className="py-3 border-b border-gray-700">
                <button
                  onClick={() => setShowAddServiceModal(true)}
                  className="w-full bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white py-3 rounded-lg flex items-center justify-center gap-2 transition-colors border-2 border-dashed border-gray-600"
                >
                  <Plus className="w-4 h-4" />
                  Zusätzliche Stunden-Services hinzufügen
                </button>
              </div>
            )}

            {/* Price Modifications */}
            {proposalData.priceModifications &&
              proposalData.priceModifications.length > 0 && (
                <>
                  <div className="py-2">
                    <h4 className="text-lg font-semibold text-white mb-3">
                      Preisanpassungen
                    </h4>
                    {proposalData.priceModifications.map((modification) => (
                      <div
                        key={modification.id}
                        className="flex justify-between items-center py-2 pl-4 border-l-2 border-orange-500"
                      >
                        <div>
                          <span className="text-gray-300">
                            {modification.reason}
                          </span>
                          <div className="text-xs text-gray-500">
                            Angepasst am{" "}
                            {new Date(
                              modification.modifiedAt
                            ).toLocaleDateString("de-DE")}{" "}
                            von {modification.modifiedBy}
                          </div>
                        </div>
                        <span
                          className={`font-semibold ${modification.type === "increase" ? "text-red-400" : "text-green-400"}`}
                        >
                          {modification.type === "increase" ? "+" : "-"}€
                          {modification.amount.toLocaleString("de-DE")}
                        </span>
                      </div>
                    ))}
                  </div>
                </>
              )}

            {/* Subtotal */}
            <div className="flex justify-between items-center py-3 border-t border-gray-600">
              <span className="text-gray-300">Zwischensumme</span>
              <span className="text-white font-semibold">
                €
                {(
                  selectedPackage.price +
                  (proposalData.additionalServices?.reduce(
                    (sum, service) => sum + service.totalPrice,
                    0
                  ) || 0) +
                  (proposalData.priceModifications?.reduce((sum, mod) => {
                    if (mod.type === "increase") return sum + mod.amount;
                    if (mod.type === "decrease") return sum - mod.amount;
                    return sum;
                  }, 0) || 0)
                ).toLocaleString("de-DE")}
              </span>
            </div>

            {/* Total */}
            <div className="flex justify-between items-center py-4 border-t-2 border-gray-600 text-xl font-bold">
              <span className="text-white">Gesamtinvestition</span>
              <span className="text-blue-400">
                €{calculateTotalPrice().toLocaleString("de-DE")}
              </span>
            </div>
          </div>
        </div>

        {/* Proposal Acceptance Note */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            Angebot akzeptieren
          </h3>
          <p className="text-gray-400 mb-4">
            Durch das Akzeptieren dieses Angebots stimmen Sie den Projektdetails
            und dem Preis zu. Nach der Annahme erhalten Sie eine E-Mail mit dem
            finalen Vertrag zur digitalen Unterzeichnung.
          </p>
          <div className="bg-blue-900/30 border border-blue-500/30 rounded-lg p-4">
            <div className="flex items-center gap-3 text-blue-300">
              <div className="text-2xl">📋</div>
              <div>
                <h4 className="font-semibold">Nächste Schritte:</h4>
                <ul className="text-sm mt-2 space-y-1">
                  <li>1. Angebot akzeptieren</li>
                  <li>2. Sie erhalten eine E-Mail mit dem Vertrag</li>
                  <li>3. Digitale Unterschrift im Vertrag</li>
                  <li>4. Projektstart</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Status Message for Qualified Proposals */}
        {isQualified && (
          <div className="bg-green-900/30 border border-green-500/30 rounded-lg p-6 mb-6">
            <div className="flex items-center gap-3 text-green-300">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-lg">
                  Angebot bereits angenommen!
                </h4>
                <p className="text-sm mt-1 text-green-400">
                  ✅ Status: Qualified | 📧 Vertragsunterlagen werden
                  vorbereitet | 📞 Wir melden uns in Kürze
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons - Only show if not qualified */}
        {!isQualified && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <button
              onClick={handleAcceptProposal}
              disabled={isSubmitting}
              className={`py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2 ${
                !isSubmitting
                  ? "bg-green-600 hover:bg-green-700 text-white"
                  : "bg-gray-600 text-gray-400 cursor-not-allowed"
              }`}
            >
              <CheckCircle className="w-5 h-5" />
              {isSubmitting ? "Wird verarbeitet..." : "Angebot akzeptieren"}
            </button>

            <button
              onClick={() => setShowModificationForm(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
            >
              <Edit className="w-5 h-5" />
              Änderungen anfordern
            </button>

            <button
              onClick={handleScheduleMeeting}
              className="bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
            >
              <Calendar className="w-5 h-5" />
              Meeting planen
            </button>
          </div>
        )}

        {/* Existing Change Requests */}
        {proposalData.changeRequests && (
          <div className="bg-gray-800 rounded-lg p-6 mb-6 border border-orange-500">
            <div className="flex items-center gap-3 mb-4">
              <Edit className="text-orange-400 w-5 h-5" />
              <h3 className="text-xl font-semibold text-white">
                Bisherige Änderungsanfragen
              </h3>
              {proposalData.changeRequestDate && (
                <span className="text-orange-400 text-sm">
                  Letzte Änderung:{" "}
                  {new Date(proposalData.changeRequestDate).toLocaleDateString(
                    "de-DE"
                  )}
                </span>
              )}
            </div>
            <div className="bg-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
              <div className="text-gray-300 whitespace-pre-wrap font-mono text-sm">
                {proposalData.changeRequests
                  .split("--- NEUE ÄNDERUNG ---")
                  .map((request, index) => (
                    <div
                      key={index}
                      className={
                        index > 0 ? "mt-6 pt-6 border-t border-gray-600" : ""
                      }
                    >
                      {request.trim() && (
                        <div className="bg-gray-600 rounded p-3">
                          {request.trim()}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {/* Modification Form - Only show if not qualified */}
        {!isQualified && showModificationForm && (
          <div className="bg-gray-800 rounded-lg p-6 mb-6 border border-yellow-500">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">
                Neue Änderungsanfrage
              </h3>
              <button
                onClick={() => setShowModificationForm(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <textarea
              value={modifications}
              onChange={(e) => setModifications(e.target.value)}
              placeholder="Beschreiben Sie die gewünschten Änderungen..."
              className="w-full h-32 p-4 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none resize-none"
            />
            <div className="flex gap-4 mt-4">
              <button
                onClick={handleRequestModification}
                disabled={!modifications.trim() || isSubmitting}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-2 rounded transition-colors"
              >
                {isSubmitting ? "Wird gesendet..." : "Änderung senden"}
              </button>
              <button
                onClick={() => setShowModificationForm(false)}
                className="bg-gray-600 hover:bg-gray-500 text-white px-6 py-2 rounded transition-colors"
              >
                Abbrechen
              </button>
            </div>
          </div>
        )}

        {/* Enhanced Notes Section */}
        {proposalData.notes && (
          <div className="bg-gray-800 rounded-lg p-6 mb-6">
            <div className="flex items-center gap-3 mb-4">
              <MessageCircle className="text-white w-5 h-5" />
              <h3 className="text-xl font-semibold text-white">
                Projektnotizen & Details
              </h3>
            </div>
            <div className="bg-gray-700 rounded-lg p-6 max-h-96 overflow-y-auto">
              <div className="text-gray-300 whitespace-pre-wrap text-sm leading-relaxed">
                {proposalData.notes}
              </div>
            </div>
          </div>
        )}

        {/* Contact Information */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            Fragen? Kontaktieren Sie uns!
          </h3>
          <p className="text-gray-400 mb-4">
            Wir sind hier, um alle Ihre Fragen zu diesem Angebot zu beantworten.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              <Mail className="w-5 h-5 text-blue-400" />
              <span className="text-white"><EMAIL></span>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-5 h-5 text-green-400" />
              <span className="text-white">+49 175 9918357</span>
            </div>
            <div className="flex items-center gap-3">
              <MessageCircle className="w-5 h-5 text-purple-400" />
              <span className="text-white">WhatsApp Support</span>
            </div>
          </div>
        </div>
      </div>

      {/* Price Edit Modal */}
      {showPriceEditModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full border border-gray-600">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white flex items-center gap-2">
                <DollarSign className="w-5 h-5" />
                Preis bearbeiten
              </h3>
              <button
                onClick={() => setShowPriceEditModal(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Änderungstyp
                </label>
                <select
                  value={priceModification.type}
                  onChange={(e) =>
                    setPriceModification({
                      ...priceModification,
                      type: e.target.value as "increase" | "decrease" | "fixed",
                    })
                  }
                  className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                >
                  <option value="increase">Preis erhöhen</option>
                  <option value="decrease">Preis reduzieren</option>
                  <option value="fixed">Festen Betrag setzen</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Betrag (€)
                </label>
                <input
                  type="number"
                  min="0"
                  step="50"
                  value={priceModification.amount}
                  onChange={(e) =>
                    setPriceModification({
                      ...priceModification,
                      amount: Number(e.target.value),
                    })
                  }
                  className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Grund für die Änderung *
                </label>
                <textarea
                  value={priceModification.reason}
                  onChange={(e) =>
                    setPriceModification({
                      ...priceModification,
                      reason: e.target.value,
                    })
                  }
                  className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none resize-none"
                  rows={3}
                  placeholder="Begründung für die Preisänderung..."
                />
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={handlePriceModification}
                  disabled={
                    !priceModification.reason.trim() ||
                    priceModification.amount <= 0 ||
                    isSubmitting
                  }
                  className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {isSubmitting ? "Speichert..." : "Preis ändern"}
                </button>
                <button
                  onClick={() => setShowPriceEditModal(false)}
                  className="px-6 bg-gray-600 hover:bg-gray-500 text-white py-3 rounded-lg font-semibold transition-colors"
                >
                  Abbrechen
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Service Modal */}
      {showAddServiceModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full border border-gray-600">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white flex items-center gap-2">
                <Plus className="w-5 h-5" />
                Service hinzufügen
              </h3>
              <button
                onClick={() => setShowAddServiceModal(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Service Name *
                </label>
                <input
                  type="text"
                  value={newService.name}
                  onChange={(e) =>
                    setNewService({
                      ...newService,
                      name: e.target.value,
                    })
                  }
                  className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                  placeholder="z.B. Extra Design Iterations"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Beschreibung
                </label>
                <textarea
                  value={newService.description}
                  onChange={(e) =>
                    setNewService({
                      ...newService,
                      description: e.target.value,
                    })
                  }
                  className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none resize-none"
                  rows={2}
                  placeholder="Kurze Beschreibung des zusätzlichen Services..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Stundensatz (€)
                  </label>
                  <input
                    type="number"
                    min="50"
                    step="25"
                    value={newService.hourlyRate}
                    onChange={(e) => {
                      const rate = Number(e.target.value);
                      setNewService({
                        ...newService,
                        hourlyRate: rate,
                        totalPrice: rate * newService.estimatedHours,
                      });
                    }}
                    className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Geschätzte Stunden
                  </label>
                  <input
                    type="number"
                    min="0.5"
                    step="0.5"
                    value={newService.estimatedHours}
                    onChange={(e) => {
                      const hours = Number(e.target.value);
                      setNewService({
                        ...newService,
                        estimatedHours: hours,
                        totalPrice: newService.hourlyRate * hours,
                      });
                    }}
                    className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Kategorie
                </label>
                <select
                  value={newService.category}
                  onChange={(e) =>
                    setNewService({
                      ...newService,
                      category: e.target
                        .value as AdditionalHourlyService["category"],
                    })
                  }
                  className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none"
                >
                  <option value="development">Development</option>
                  <option value="design">Design</option>
                  <option value="consulting">Consulting</option>
                  <option value="testing">Testing</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Gesamtpreis:</span>
                  <span className="text-white font-semibold text-lg">
                    €{newService.totalPrice.toLocaleString("de-DE")}
                  </span>
                </div>
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={handleAddService}
                  disabled={
                    !newService.name.trim() ||
                    newService.estimatedHours <= 0 ||
                    isSubmitting
                  }
                  className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  {isSubmitting ? "Wird hinzugefügt..." : "Service hinzufügen"}
                </button>
                <button
                  onClick={() => setShowAddServiceModal(false)}
                  className="px-6 bg-gray-600 hover:bg-gray-500 text-white py-3 rounded-lg font-semibold transition-colors"
                >
                  Abbrechen
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-xl p-8 max-w-md w-full border border-gray-600">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">
                {proposalAccepted ? "Angebot angenommen!" : "Erfolgreich!"}
              </h3>
              <p className="text-gray-300 mb-6">{successMessage}</p>

              {proposalAccepted && (
                <div className="text-sm text-gray-400 mb-6 space-y-2">
                  <p>✅ Status wurde auf "Qualified" aktualisiert</p>
                  <p>📧 Vertragsunterlagen werden vorbereitet</p>
                  <p>📞 Unser Team meldet sich in Kürze bei Ihnen</p>
                </div>
              )}

              <div className="flex justify-center">
                <button
                  onClick={() => {
                    try {
                      window.location.replace("/");
                    } catch (error) {
                      window.location.href = "/";
                    }
                  }}
                  className="w-full max-w-sm bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg transition-colors font-semibold"
                >
                  Zur Homepage
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Message for Modifications */}
      {successMessage && !showSuccessModal && (
        <div className="fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50">
          {successMessage}
        </div>
      )}
    </div>
  );
}
