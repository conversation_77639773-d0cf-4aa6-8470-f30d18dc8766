import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import { getDictionary } from "@/lib/dictionaries";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  
  return {
    title: dictionary.seo?.meta?.faq?.title || "FAQ - Innovatio Pro",
    description: dictionary.seo?.meta?.faq?.description || "Frequently asked questions about mobile app development",
    keywords: dictionary.seo?.meta?.faq?.keywords || "app development faq, mobile development questions",
    alternates: {
      canonical: `https://innovatio-pro.com/${locale}/faq`
    }
  }
}

export default async function FAQPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  
  redirect(`/${locale}#faq`);
}