import { redirect } from 'next/navigation';
import { locales, defaultLocale } from "../middleware";
import { headers } from "next/headers";

// Direct redirects for production vs. development environments
export default async function Home() {
  // Get request headers to check for country information
  const headersList = await headers();

  // Special handling for production (innovatio-pro.com)
  const hostname =
    process.env.VERCEL_URL ||
    process.env.NEXT_PUBLIC_SITE_URL ||
    (await headersList.get("host")) ||
    "";

  if (hostname.includes("innovatio-pro.com")) {
    // Hard redirect to German version for the production domain
    console.log("Production domain detected - redirecting to German version");
    return redirect("/de");
  }

  // Get the URL and search parameters
  const requestUrl =
    (await headersList.get("x-url")) ||
    (await headersList.get("x-invoke-path")) ||
    "";
  const searchParams = new URLSearchParams(
    (await headersList.get("x-invoke-query")) || ""
  );
  const countryParam = searchParams.get("country");

  // Try to detect country from headers or URL parameter
  let country = null;

  // First check URL parameter
  if (countryParam) {
    country = countryParam.toUpperCase();
    console.log("Country from URL parameter:", country);
  } else {
    // Then try headers
    country =
      (await headersList.get("x-vercel-ip-country")) ||
      (await headersList.get("cf-ipcountry"));
    console.log("Country from headers:", country);
  }

  console.log("Final country detection:", country);

  // Country to language mapping (same as in middleware)
  const countryLanguageMap: Record<string, string> = {
    DE: "de",
    AT: "de",
    CH: "de", // German-speaking
    RU: "ru",
    BY: "ru",
    KZ: "ru", // Russian-speaking
    TR: "tr",
    CY: "tr", // Turkish-speaking
    AE: "ar",
    SA: "ar",
    EG: "ar", // Arabic-speaking
    QA: "ar",
    BH: "ar",
    KW: "ar",
    OM: "ar",
    IQ: "ar",
  };

  if (country && countryLanguageMap[country]) {
    // Redirect to the appropriate locale based on country
    console.log(
      `Country detected: ${country} - redirecting to ${countryLanguageMap[country]}`
    );
    return redirect(`/${countryLanguageMap[country]}`);
  }

  // For local development, force German locale
  const isLocalhost =
    hostname.includes("localhost") || hostname.includes("127.0.0.1");
  if (isLocalhost) {
    console.log("Local development detected - redirecting to German version");
    return redirect("/de");
  }

  // For other environments, redirect to default locale
  console.log(
    `No country detected - redirecting to default locale: ${defaultLocale}`
  );
  return redirect(`/${defaultLocale}`);
}