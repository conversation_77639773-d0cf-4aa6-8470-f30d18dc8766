# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.


## Standard Workflow
1. First think through the problem, read the codebase for relevant files, and write a plan to tasks/todo.md.
2.The plan should have a list of todo items that you can
check off as you complete them - create an tasks.md file for this in /tasks folder.
3. Before you begin working, check in with me and I will verify the plan.
4. Then, begin working on the todo items, marking them as complete as you go.
5. Please every step of the way just give me a high level explanation of what changes you made
6. Make every task and code change you do as simple as possible. We want to avoid making any massive or complex changes. Every change should impact as little code as possible. Everything is about simplicity.
7. Finally, add a review section to the todo.md file with a summary of the changes you made and any other relevant information.
8.After every fully finished tasks ( main task) - make a git commit - generate a proper understanding message

Keep going until the job is completely solved before ending your turn

If you’re unsure about the code or files, open them - dont hallucinate.

Plan thoroughly before every tool call and reflect on the outcome after.

Repeat the task as long as needed till its fully functional and cleanly implemented - dont add unnessacry codes - before adding new code check if it exists and extend the exisiting one.

## Architecture Overview

This is a Next.js 15 application with the App Router, featuring:

### Multi-language Support
- **Locales**: en, de, ru, tr, ar (English, German, Russian, Turkish, Arabic)
- **Middleware**: `src/middleware.ts` handles locale detection and routing
- **Geolocation**: Country-based language detection using Vercel/Cloudflare headers
- **Dictionaries**: Stored in `src/dictionaries/` with type-safe loading via `src/lib/dictionaries.ts`
- **URL Structure**: `[locale]/page` pattern throughout the app

### Key Architecture Patterns

#### CMS Integration
- **Adapter Pattern**: `src/lib/cms/CMSAdapter.ts` defines interface for content management
- **Local CMS**: `src/lib/cms/adapters/LocalCMSAdapter.ts` implements local content storage
- **Provider**: `src/providers/CMSProvider.tsx` provides CMS context throughout app

#### Component Structure
- **Sections**: `src/components/sections/` - Main page sections (Hero, Services, Contact, etc.)
- **UI Components**: `src/components/ui/` - Reusable components with accessibility features
- **Layout**: `src/components/layout/` - Layout-specific components
- **Admin**: `src/components/admin/` - Content management interface

#### Styling & Theming
- **Tailwind CSS**: Custom config with dark mode support
- **Theme Provider**: `src/providers/ThemeProvider.tsx` manages light/dark themes
- **Custom Colors**: Defined in `tailwind.config.js` with semantic naming

#### API Routes
- **Contact Forms**: `/api/contact/` and `/api/contact/enhanced/`
- **Newsletter**: `/api/newsletter/subscribe/`
- **Business Logic**: Contract generation, proposal handling, email sending

### Performance Optimizations
- **Image Optimization**: Next.js Image component with AVIF/WebP support
- **Bundle Splitting**: Custom webpack config for vendor chunks
- **SEO**: Comprehensive sitemap generation with locale-specific URLs
- **Security Headers**: CSP, HSTS, and other security headers configured

### Important Notes
- **TypeScript**: Strict mode enabled with path aliases (`@/*` → `src/*`)
- **Accessibility**: Comprehensive accessibility components and ARIA support
- **No Tests**: This project doesn't have a test suite configured
- **Production Domain**: `innovatio-pro.com` always redirects to German locale
- **Local Development**: Forces German locale for consistency

### Configuration Files
- `next.config.js` - Next.js configuration with performance optimizations
- `tailwind.config.js` - Tailwind CSS configuration with custom theme
- `next-sitemap.config.js` - Sitemap generation with AI crawler support
- `tsconfig.json` - TypeScript configuration with strict mode
