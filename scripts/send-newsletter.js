#!/usr/bin/env node

/**
 * Newsletter Sending Script
 *
 * Usage:
 * node scripts/send-newsletter.js <post-slug> [locale]
 *
 * Example:
 * node scripts/send-newsletter.js "flutter-vs-native-development" "de"
 */

const https = require("https");
const http = require("http");

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
const API_SECRET = process.env.NEWSLETTER_API_SECRET || "your-secret-token";

function sendNewsletter(postSlug, locale = "de") {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      postSlug,
      locale,
    });

    const url = new URL(`${BASE_URL}/api/newsletter/notify`);
    const isHttps = url.protocol === "https:";
    const httpModule = isHttps ? https : http;

    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${API_SECRET}`,
        "Content-Length": Buffer.byteLength(data),
      },
    };

    const req = httpModule.request(options, (res) => {
      let responseData = "";

      res.on("data", (chunk) => {
        responseData += chunk;
      });

      res.on("end", () => {
        try {
          const result = JSON.parse(responseData);

          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(result);
          } else {
            reject(
              new Error(
                `HTTP ${res.statusCode}: ${result.error || "Unknown error"}`
              )
            );
          }
        } catch (parseError) {
          reject(new Error(`Failed to parse response: ${responseData}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.error("❌ Error: Post slug is required");
    console.log("\nUsage:");
    console.log("  node scripts/send-newsletter.js <post-slug> [locale]");
    console.log("\nExample:");
    console.log(
      '  node scripts/send-newsletter.js "flutter-vs-native-development" "de"'
    );
    process.exit(1);
  }

  const postSlug = args[0];
  const locale = args[1] || "de";

  console.log(`📧 Sending newsletter for post: ${postSlug} (${locale})`);
  console.log(`🌐 Using base URL: ${BASE_URL}`);
  console.log("⏳ Please wait...\n");

  try {
    const result = await sendNewsletter(postSlug, locale);

    console.log("✅ Newsletter sent successfully!");
    console.log("📊 Statistics:");
    console.log(`   • Post: ${result.stats.postTitle}`);
    console.log(`   • Total subscribers: ${result.stats.totalSubscribers}`);
    console.log(`   • Successfully sent: ${result.stats.successCount}`);
    console.log(`   • Failed: ${result.stats.failCount}`);
    console.log(`   • Post URL: ${result.stats.postUrl}`);
  } catch (error) {
    console.error("❌ Failed to send newsletter:", error.message);

    if (error.message.includes("401")) {
      console.error(
        "\n💡 Tip: Make sure NEWSLETTER_API_SECRET is set correctly in your environment variables"
      );
    } else if (error.message.includes("404")) {
      console.error("\n💡 Tip: Check if the post slug exists and is correct");
    } else if (error.message.includes("ECONNREFUSED")) {
      console.error("\n💡 Tip: Make sure your Next.js server is running");
    }

    process.exit(1);
  }
}

// Check if required environment variables are set
if (!process.env.RESEND_API_KEY) {
  console.warn("⚠️  Warning: RESEND_API_KEY not set in environment variables");
}

if (!process.env.RESEND_AUDIENCE_ID) {
  console.warn(
    "⚠️  Warning: RESEND_AUDIENCE_ID not set in environment variables"
  );
}

main();
